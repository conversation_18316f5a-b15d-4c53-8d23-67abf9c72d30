import React from "react";
import {
  QrCode,
  CreditCard,
  Users,
  MonitorSpeaker,
  Layers,
  FileText,
  Printer,
  BarChart3,
  Rocket,
} from "lucide-react";

const EpisysFeatures = () => {
  const features = [
    {
      icon: QrCode,
      title: "قائمة رمز الاستجابة السريعة",
      description: "طلب بدون تلامس أصبح سهلاً",
    },
    {
      icon: CreditCard,
      title: "تكامل بوابة الدفع",
      description: "مدفوعات سريعة وآمنة ومرنة باستخدام Stripe و Razorpay",
    },
    {
      icon: Users,
      title: "إدارة الموظفين",
      description: "تسجيل دخول منفصل لكل دور موظف مع صلاحيات مختلفة",
    },
    {
      icon: MonitorSpeaker,
      title: "نقطة البيع (POS)",
      description: "تكامل كامل لنقطة البيع",
    },
    {
      icon: Layers,
      title: "مخططات الطوابق المخصصة",
      description: "صمم تخطيط مطعمك",
    },
    {
      icon: FileText,
      title: "تذاكر طلبات المطبخ (KOT)",
      description: "سير عمل مطبخ فعال",
    },
    {
      icon: Printer,
      title: "طباعة الفواتير",
      description: "فوترة سريعة ودقيقة",
    },
    {
      icon: BarChart3,
      title: "التقارير",
      description: "قرارات مدفوعة بالبيانات",
    },
  ];

  return (
    <div
      className="  my-8 bg-gradient-to-br from-white via-gray-50 to-orange-50 py-20 px-6"
     
    >
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-4 mb-8">
            <Rocket className="w-10 h-10 text-[#FF6500]" />
            <h1 className="text-5xl md:text-6xl font-black bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent">
              ليه تستخدم نظام
            </h1>
            <span className="text-5xl md:text-6xl font-black bg-gradient-to-r from-[#FF6500] via-[#FCB190] to-[#FFD5C7] bg-clip-text text-transparent">
              episys
            </span>
          </div>

          <div className="max-w-3xl mx-auto">
            <p className="text-xl md:text-2xl text-gray-600 leading-relaxed font-light">
              اكتشف القوة الحقيقية لإدارة المطاعم مع{" "}
              <span className="text-[#FF6500] font-semibold">episys</span> -
              نظام شامل ومتطور
            </p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#FFD5C7]/20 to-transparent blur-3xl"></div>

          <div className="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div
                  key={feature.title}
                  className="group relative"
                  style={{
                    animationDelay: `${index * 0.1}s`,
                  }}
                >
                  <div className="relative h-full p-8 rounded-3xl bg-gradient-to-br from-white/90 to-gray-50/90 backdrop-blur-sm border border-gray-200/50 hover:border-[#FCB190]/50 shadow-lg hover:shadow-2xl hover:shadow-[#FF6500]/10 transition-all duration-500 hover:scale-105">
                    {/* Icon Container */}
                    <div className="relative mb-6">
                      <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-[#FF6500]/10 to-[#FCB190]/10 border border-[#FCB190]/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <IconComponent className="w-8 h-8 text-[#FF6500] group-hover:text-[#FCB190] transition-colors duration-300" />
                      </div>

                      {/* Glow effect */}
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#FF6500]/0 via-[#FCB190]/0 to-[#FFD5C7]/0 group-hover:from-[#FF6500]/20 group-hover:via-[#FCB190]/30 group-hover:to-[#FFD5C7]/20 blur-xl transition-all duration-500"></div>
                    </div>

                    {/* Content */}
                    <div className="text-center">
                      <h3 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-[#FF6500] transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                        {feature.description}
                      </p>
                    </div>

                    {/* Hover overlay */}
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-[#FF6500]/0 via-[#FCB190]/0 to-[#FFD5C7]/0 group-hover:from-[#FF6500]/5 group-hover:via-[#FCB190]/5 group-hover:to-[#FFD5C7]/5 transition-all duration-500"></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EpisysFeatures;
