"use client"
import React, { useState } from 'react';
import { Clock, User, MapPin, Droplets, Filter, Search, Plus, CheckCircle, AlertCircle, Calendar } from 'lucide-react';

const WaiterRequestGrid = () => {
  const [activeTab, setActiveTab] = useState('INDOOR');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // grid or list

  // Fake data
  const waterRequests = {
    INDOOR: [
      {
        id: 'IN01',
        status: 'attended',
        timeAgo: '2 months ago',
        location: 'Main Building - Floor 2',
        priority: 'medium',
        description: 'Leaking faucet in kitchen area',
        requestedBy: '<PERSON>',
        assignedTo: '<PERSON>'
      },
      {
        id: 'IN02',
        status: 'pending',
        timeAgo: '1 week ago',
        location: 'Office Block A - Room 201',
        priority: 'high',
        description: 'No water supply in restroom',
        requestedBy: '<PERSON>',
        assignedTo: null
      },
      {
        id: 'IN03',
        status: 'in-progress',
        timeAgo: '3 days ago',
        location: 'Cafeteria - Ground Floor',
        priority: 'low',
        description: 'Low water pressure in sink',
        requestedBy: '<PERSON>',
        assignedTo: '<PERSON>'
      },
      {
        id: 'IN04',
        status: 'attended',
        timeAgo: '5 days ago',
        location: 'Laboratory - Floor 3',
        priority: 'high',
        description: 'Emergency shower not working',
        requestedBy: 'Lisa Chen',
        assignedTo: 'Mike Wilson'
      }
    ],
    TERACE: [
      {
        id: 'TR01',
        status: 'pending',
        timeAgo: '1 day ago',
        location: 'Rooftop Garden - East Wing',
        priority: 'medium',
        description: 'Irrigation system malfunction',
        requestedBy: 'Garden Team',
        assignedTo: null
      },
      {
        id: 'TR02',
        status: 'in-progress',
        timeAgo: '4 hours ago',
        location: 'Terrace - West Side',
        priority: 'high',
        description: 'Water pipe burst near seating area',
        requestedBy: 'Maintenance Staff',
        assignedTo: 'Alex Brown'
      }
    ]
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'attended': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-red-100 text-red-800 border-red-200';
      case 'in-progress': return 'bg-[#FF6500]/10 text-[#FF6500] border-[#FF6500]/20';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-red-600';
      case 'medium': return 'text-[#FF6500]';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'attended': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <AlertCircle className="w-4 h-4" />;
      case 'in-progress': return <Clock className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const filteredRequests = waterRequests[activeTab]?.filter(request => {
    const matchesSearch = request.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || request.status === filterStatus;
    return matchesSearch && matchesFilter;
  }) || [];

  return (
    <div className="min-h-screen bg-gray-50 p-4 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Water Requests</h1>
          <p className="text-gray-600">Manage and track water maintenance requests</p>
        </div>

        {/* Controls */}
        <div className="mb-6 space-y-4 lg:space-y-0 lg:flex lg:items-center lg:justify-between">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search requests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none w-full sm:w-64"
              />
            </div>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="in-progress">In Progress</option>
              <option value="attended">Attended</option>
            </select>
          </div>

          {/* View Controls */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-2 rounded-lg transition-colors ${
                viewMode === 'grid' 
                  ? 'bg-[#FF6500] text-white' 
                  : 'bg-white text-gray-600 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-2 rounded-lg transition-colors ${
                viewMode === 'list' 
                  ? 'bg-[#FF6500] text-white' 
                  : 'bg-white text-gray-600 border border-gray-300 hover:bg-gray-50'
              }`}
            >
              List
            </button>
            <button className="ml-2 px-4 py-2 bg-[#FF6500] text-white rounded-lg hover:bg-[#e55a00] transition-colors flex items-center gap-2">
              <Plus className="w-4 h-4" />
              New Request
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6 border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {Object.keys(waterRequests).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab
                    ? 'border-[#FF6500] text-[#FF6500]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab} 
                <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  {waterRequests[tab].length} Table{waterRequests[tab].length !== 1 ? 's' : ''}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        {filteredRequests.length === 0 ? (
          <div className="text-center py-16">
            <Droplets className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No water requests found</h3>
            <p className="text-gray-500">
              {searchTerm || filterStatus !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : 'No water request found in this area.'}
            </p>
          </div>
        ) : (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
            : 'space-y-4'
          }>
            {filteredRequests.map((request) => (
              <div key={request.id} className={`bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow ${
                viewMode === 'list' ? 'p-6' : 'p-4'
              }`}>
                {/* Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="bg-[#FF6500] text-white px-2 py-1 rounded text-sm font-medium">
                      {request.id}
                    </span>
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(request.status)}`}>
                      {getStatusIcon(request.status)}
                      {request.status.replace('-', ' ')}
                    </span>
                  </div>
                  <div className="flex items-center gap-1 text-gray-500 text-sm">
                    <Clock className="w-4 h-4" />
                    {request.timeAgo}
                  </div>
                </div>

                {/* Location */}
                <div className="flex items-start gap-2 mb-3">
                  <MapPin className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{request.location}</span>
                </div>

                {/* Description */}
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">{request.description}</p>

                {/* Details */}
                <div className="space-y-2 text-xs text-gray-500">
                  <div className="flex items-center gap-2">
                    <User className="w-3 h-3" />
                    <span>Requested by: {request.requestedBy}</span>
                  </div>
                  {request.assignedTo && (
                    <div className="flex items-center gap-2">
                      <User className="w-3 h-3" />
                      <span>Assigned to: {request.assignedTo}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <AlertCircle className={`w-3 h-3 ${getPriorityColor(request.priority)}`} />
                    <span className={getPriorityColor(request.priority)}>
                      Priority: {request.priority}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="mt-4 pt-4 border-t border-gray-100 flex gap-2">
                  {request.status === 'pending' && (
                    <button className="flex-1 px-3 py-2 bg-[#FF6500] text-white text-sm rounded hover:bg-[#e55a00] transition-colors">
                      Assign
                    </button>
                  )}
                  {request.status === 'in-progress' && (
                    <button className="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors">
                      Mark Complete
                    </button>
                  )}
                  <button className="px-3 py-2 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50 transition-colors">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Stats Footer */}
        <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">Pending</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {Object.values(waterRequests).flat().filter(r => r.status === 'pending').length}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 bg-[#FF6500] rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">In Progress</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {Object.values(waterRequests).flat().filter(r => r.status === 'in-progress').length}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700">Completed</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {Object.values(waterRequests).flat().filter(r => r.status === 'attended').length}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WaiterRequestGrid;