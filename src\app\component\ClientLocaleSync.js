'use client';

import { useEffect } from 'react';
import { useI18n } from '@/context/translate-api';

const ClientLocaleSync = () => {
  const { locale } = useI18n();

  useEffect(() => {
    // Update HTML attributes when locale changes
    document.documentElement.lang = locale;
    document.documentElement.dir = locale === "ar" ? "rtl" : "ltr";
    
    // Update body classes
    document.body.className = document.body.className.replace(/\b(rtl|ltr)\b/g, '');
    document.body.className += ` ${locale === "ar" ? "rtl" : "ltr"}`;
  }, [locale]);

  return null; // This component doesn't render anything
};

export default ClientLocaleSync;