"use client"
import React, { useState } from 'react';
import { Search, Plus, Edit2, Trash2, Eye, Filter, Download, Upload } from 'lucide-react';

const UnitsGrid = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUnits, setSelectedUnits] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingUnit, setEditingUnit] = useState(null);
  const [newUnit, setNewUnit] = useState({ name: '', symbol: '' });

  // Sample units data
  const [units, setUnits] = useState([
    { id: 1, name: 'Kilogram', symbol: 'kg' },
    { id: 2, name: 'Gram', symbol: 'g' },
    { id: 3, name: 'Liter', symbol: 'L' },
    { id: 4, name: 'Milliliter', symbol: 'ml' },
    { id: 5, name: 'Piece', symbol: 'pc' },
    { id: 6, name: 'Box', symbol: 'box' },
    { id: 7, name: 'Dozen', symbol: 'dz' },
    { id: 8, name: 'Bo<PERSON>', symbol: 'btl' },
    { id: 9, name: 'Package', symbol: 'pkg' },
    { id: 10, name: 'Meter', symbol: 'm' },
    { id: 11, name: 'Centimeter', symbol: 'cm' },
    { id: 12, name: 'Inch', symbol: 'in' },
    { id: 13, name: 'Pound', symbol: 'lb' },
    { id: 14, name: 'Ounce', symbol: 'oz' },
    { id: 15, name: 'Gallon', symbol: 'gal' }
  ]);

  const filteredUnits = units.filter(unit =>
    unit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    unit.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectUnit = (unitId) => {
    setSelectedUnits(prev =>
      prev.includes(unitId)
        ? prev.filter(id => id !== unitId)
        : [...prev, unitId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUnits.length === filteredUnits.length) {
      setSelectedUnits([]);
    } else {
      setSelectedUnits(filteredUnits.map(unit => unit.id));
    }
  };

  const handleAddUnit = () => {
    if (newUnit.name && newUnit.symbol) {
      const newId = Math.max(...units.map(u => u.id)) + 1;
      setUnits([...units, { id: newId, ...newUnit }]);
      setNewUnit({ name: '', symbol: '' });
      setShowAddModal(false);
    }
  };

  const handleUpdateUnit = () => {
    if (editingUnit && editingUnit.name && editingUnit.symbol) {
      setUnits(units.map(unit =>
        unit.id === editingUnit.id ? editingUnit : unit
      ));
      setEditingUnit(null);
    }
  };

  const handleDeleteUnit = (unitId) => {
    setUnits(units.filter(unit => unit.id !== unitId));
    setSelectedUnits(selectedUnits.filter(id => id !== unitId));
  };

  const handleBulkDelete = () => {
    setUnits(units.filter(unit => !selectedUnits.includes(unit.id)));
    setSelectedUnits([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Units</h1>
          <p className="text-gray-600">Manage measurement units for your inventory</p>
        </div>

        {/* Controls */}
        <div className="mb-6 space-y-4 lg:space-y-0 lg:flex lg:items-center lg:justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search Unit by Name or Symbol"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none text-gray-700"
            />
          </div>

          {/* Actions */}
          <div className="flex items-center gap-3">
            {selectedUnits.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {selectedUnits.length} selected
                </span>
                <button
                  onClick={handleBulkDelete}
                  className="px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors flex items-center gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  Delete
                </button>
              </div>
            )}
            
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export
            </button>
            
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Import
            </button>
            
            <button
              onClick={() => setShowAddModal(true)}
              className="px-6 py-2 bg-[#3B82F6] text-white rounded-lg hover:bg-[#2563EB] transition-colors flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Unit
            </button>
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Table Header */}
          <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div className="grid grid-cols-12 gap-4 items-center">
              <div className="col-span-1">
                <input
                  type="checkbox"
                  checked={selectedUnits.length === filteredUnits.length && filteredUnits.length > 0}
                  onChange={handleSelectAll}
                  className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
                />
              </div>
              <div className="col-span-5 text-sm font-semibold text-gray-700 uppercase tracking-wide">
                UNIT NAME
              </div>
              <div className="col-span-4 text-sm font-semibold text-gray-700 uppercase tracking-wide">
                UNIT SYMBOL
              </div>
              <div className="col-span-2 text-sm font-semibold text-gray-700 uppercase tracking-wide text-right">
                ACTION
              </div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-gray-200">
            {filteredUnits.length === 0 ? (
              <div className="px-6 py-12 text-center">
                <div className="text-gray-400 mb-2">
                  <Search className="w-12 h-12 mx-auto mb-4" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No units found</h3>
                <p className="text-gray-500">
                  {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by adding your first unit.'}
                </p>
              </div>
            ) : (
              filteredUnits.map((unit) => (
                <div key={unit.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                  {editingUnit && editingUnit.id === unit.id ? (
                    // Edit Mode
                    <div className="grid grid-cols-12 gap-4 items-center">
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={selectedUnits.includes(unit.id)}
                          onChange={() => handleSelectUnit(unit.id)}
                          className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
                        />
                      </div>
                      <div className="col-span-5">
                        <input
                          type="text"
                          value={editingUnit.name}
                          onChange={(e) => setEditingUnit({...editingUnit, name: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
                        />
                      </div>
                      <div className="col-span-4">
                        <input
                          type="text"
                          value={editingUnit.symbol}
                          onChange={(e) => setEditingUnit({...editingUnit, symbol: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
                        />
                      </div>
                      <div className="col-span-2 text-right space-x-2">
                        <button
                          onClick={handleUpdateUnit}
                          className="px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors text-sm"
                        >
                          Save
                        </button>
                        <button
                          onClick={() => setEditingUnit(null)}
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors text-sm"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    // View Mode
                    <div className="grid grid-cols-12 gap-4 items-center">
                      <div className="col-span-1">
                        <input
                          type="checkbox"
                          checked={selectedUnits.includes(unit.id)}
                          onChange={() => handleSelectUnit(unit.id)}
                          className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
                        />
                      </div>
                      <div className="col-span-5">
                        <span className="text-gray-900 font-medium">{unit.name}</span>
                      </div>
                      <div className="col-span-4">
                        <span className="text-gray-700">{unit.symbol}</span>
                      </div>
                      <div className="col-span-2 text-right">
                        <div className="flex items-center justify-end gap-2">
                          <button
                            onClick={() => setEditingUnit(unit)}
                            className="p-2 text-gray-500 hover:text-[#FF6500] hover:bg-[#FF6500]/10 rounded-lg transition-colors group"
                            title="Update"
                          >
                            <Edit2 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteUnit(unit.id)}
                            className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors group"
                            title="Delete"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Pagination */}
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing <span className="font-medium">{filteredUnits.length}</span> of{' '}
            <span className="font-medium">{units.length}</span> units
          </div>
          <div className="flex items-center space-x-2">
            <button className="px-3 py-2 border border-gray-300 text-gray-500 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Previous
            </button>
            <button className="px-3 py-2 bg-[#FF6500] text-white rounded-lg hover:bg-[#e55a00] transition-colors">
              1
            </button>
            <button className="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              2
            </button>
            <button className="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              Next
            </button>
          </div>
        </div>

        {/* Add Unit Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Add New Unit</h3>
              </div>
              <div className="px-6 py-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Unit Name
                  </label>
                  <input
                    type="text"
                    value={newUnit.name}
                    onChange={(e) => setNewUnit({...newUnit, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
                    placeholder="Enter unit name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Unit Symbol
                  </label>
                  <input
                    type="text"
                    value={newUnit.symbol}
                    onChange={(e) => setNewUnit({...newUnit, symbol: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
                    placeholder="Enter unit symbol"
                  />
                </div>
              </div>
              <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-end gap-3">
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    setNewUnit({ name: '', symbol: '' });
                  }}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddUnit}
                  disabled={!newUnit.name || !newUnit.symbol}
                  className="px-4 py-2 bg-[#3B82F6] text-white rounded-lg hover:bg-[#2563EB] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Add Unit
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UnitsGrid;