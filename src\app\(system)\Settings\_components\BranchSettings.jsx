import React, { useState } from 'react';
import { MapPin, Edit, Trash2, Plus, Building, AlertCircle, CheckCircle } from 'lucide-react';

const BranchSettings = () => {
  const [branches, setBranches] = useState([
    {
      id: 1,
      name: 'DEG<PERSON>',
      address: 'DEGLA PALM COMPOUND MALL06',
      isMain: true
    },
    {
      id: 2,
      name: 'nasr city',
      address: 'abbas el akad',
      isMain: false
    }
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingBranch, setEditingBranch] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    address: ''
  });

  const handleAddBranch = () => {
    setShowAddForm(true);
    setEditingBranch(null);
    setFormData({ name: '', address: '' });
  };

  const handleEditBranch = (branch) => {
    setEditingBranch(branch.id);
    setFormData({
      name: branch.name,
      address: branch.address
    });
    setShowAddForm(true);
  };

  const handleDeleteBranch = (branchId) => {
    const branch = branches.find(b => b.id === branchId);
    if (branch && branch.isMain) {
      alert('Cannot delete the main branch');
      return;
    }
    setBranches(branches.filter(b => b.id !== branchId));
  };

  const handleSaveBranch = () => {
    if (!formData.name.trim() || !formData.address.trim()) {
      alert('Please fill in all fields');
      return;
    }

    if (editingBranch) {
      setBranches(branches.map(branch => 
        branch.id === editingBranch 
          ? { ...branch, name: formData.name, address: formData.address }
          : branch
      ));
    } else {
      const newBranch = {
        id: Math.max(...branches.map(b => b.id)) + 1,
        name: formData.name,
        address: formData.address,
        isMain: false
      };
      setBranches([...branches, newBranch]);
    }

    setShowAddForm(false);
    setEditingBranch(null);
    setFormData({ name: '', address: '' });
  };

  const handleCancel = () => {
    setShowAddForm(false);
    setEditingBranch(null);
    setFormData({ name: '', address: '' });
  };

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-2xl p-8 border border-green-200">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-r from-[#FF6500] to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
              <MapPin className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Branch Settings</h2>
              <p className="text-gray-600 mt-1">Manage your restaurant branches and locations</p>
            </div>
          </div>
          <button
            onClick={handleAddBranch}
            className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-orange-600 text-white rounded-xl font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2"
          >
            <Plus className="w-5 h-5" />
            Add Branch
          </button>
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <div className="bg-white rounded-2xl p-6 border-2 border-gray-100 shadow-sm mb-6">
            <div className="flex items-center gap-3 mb-4">
              <Building className="w-6 h-6 text-[#FF6500]" />
              <h3 className="text-lg font-bold text-gray-900">
                {editingBranch ? 'Edit Branch' : 'Add New Branch'}
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Branch Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm"
                  placeholder="Enter branch name"
                />
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Branch Address
                </label>
                <input
                  type="text"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm"
                  placeholder="Enter branch address"
                />
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <button
                onClick={handleCancel}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg font-semibold hover:bg-gray-200 transition-all duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveBranch}
                className="px-6 py-2 bg-gradient-to-r from-[#FF6500] to-orange-600 text-white rounded-lg font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-200 shadow-lg"
              >
                {editingBranch ? 'Update' : 'Save'}
              </button>
            </div>
          </div>
        )}

        {/* Branches Table */}
        <div className="bg-white rounded-2xl border-2 border-gray-100 shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-4 px-6 text-sm font-bold text-gray-700 uppercase tracking-wider">
                    Branch Name
                  </th>
                  <th className="text-left py-4 px-6 text-sm font-bold text-gray-700 uppercase tracking-wider">
                    Branch Address
                  </th>
                  <th className="text-center py-4 px-6 text-sm font-bold text-gray-700 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="text-center py-4 px-6 text-sm font-bold text-gray-700 uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {branches.map((branch) => (
                  <tr key={branch.id} className="hover:bg-gray-50 transition-colors duration-200">
                    <td className="py-4 px-6">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-[#FF6500] to-orange-600 rounded-lg flex items-center justify-center">
                          <Building className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-gray-900">{branch.name}</div>
                          {branch.isMain && (
                            <div className="flex items-center gap-1 mt-1">
                              <CheckCircle className="w-3 h-3 text-green-500" />
                              <span className="text-xs text-green-600 font-medium">Main Branch</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-900">{branch.address}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-center">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                        branch.isMain 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {branch.isMain ? 'Main' : 'Branch'}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center justify-center gap-2">
                        <button
                          onClick={() => handleEditBranch(branch)}
                          className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors duration-200 flex items-center gap-1"
                          title="Update"
                        >
                          <Edit className="w-4 h-4" />
                          <span className="text-xs font-medium">Update</span>
                        </button>
                        {!branch.isMain && (
                          <button
                            onClick={() => handleDeleteBranch(branch.id)}
                            className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors duration-200"
                            title="Delete"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        )}
                        {branch.isMain && (
                          <div className="flex items-center gap-1 text-xs text-gray-500 px-2">
                            <AlertCircle className="w-3 h-3" />
                            <span>Cannot delete current branch</span>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Branch Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                <Building className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{branches.length}</div>
                <div className="text-sm text-gray-600">Total Branches</div>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{branches.filter(b => b.isMain).length}</div>
                <div className="text-sm text-gray-600">Main Branch</div>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <MapPin className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{branches.filter(b => !b.isMain).length}</div>
                <div className="text-sm text-gray-600">Sub Branches</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BranchSettings;