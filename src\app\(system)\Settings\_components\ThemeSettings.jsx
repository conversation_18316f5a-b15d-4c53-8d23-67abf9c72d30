"use client"
import React, { useState } from 'react';
import { Upload, Image, Palette, Check } from 'lucide-react';

const ThemeSettings = () => {
  const [selectedColor, setSelectedColor] = useState('#FF6500');
  const [uploadedLogos, setUploadedLogos] = useState({});

  const logoOptions = [
    { id: 'android_192', title: 'Android Chrome (192x192)px', size: '192x192' },
    { id: 'android_512', title: 'Android Chrome (512x512)px', size: '512x512' },
    { id: 'apple_touch', title: 'Apple Touch Icon', size: '180x180' },
    { id: 'favicon_16', title: 'Favicon (16x16)px', size: '16x16' },
    { id: 'favicon_32', title: 'Favicon (32x32)px', size: '32x32' },
    { id: 'favicon_ico', title: 'Favicon (.ico)', size: 'Multiple' }
  ];

  const professionalColors = [
    '#2563eb', '#10b981', '#8b5cf6', '#06b6d4', '#f97316', '#6366f1', '#14b8a6', '#a855f7'
  ];

  const pastelColors = [
    '#93c5fd', '#86efac', '#fde047', '#fdba74', '#c4b5fd', '#7dd3fc', '#fca5a5', '#a5b4fc'
  ];

  const warmColors = [
    '#f97316', '#ef4444', '#d946ef', '#ec4899', '#f43f5e', '#fb923c', '#e11d48', '#f59e0b'
  ];

  const handleFileUpload = (logoId, event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedLogos(prev => ({
          ...prev,
          [logoId]: e.target.result
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const ColorPalette = ({ colors, title }) => (
    <div className="mb-6">
      <h4 className="text-sm font-medium text-gray-700 mb-3">{title}</h4>
      <div className="flex flex-wrap gap-2">
        {colors.map((color) => (
          <button
            key={color}
            onClick={() => setSelectedColor(color)}
            className={`w-10 h-10 rounded-full border-2 transition-all duration-200 ${
              selectedColor === color 
                ? 'border-gray-400 scale-110 shadow-lg' 
                : 'border-gray-200 hover:border-gray-300'
            }`}
            style={{ backgroundColor: color }}
          >
            {selectedColor === color && (
              <Check className="w-4 h-4 text-white mx-auto" />
            )}
          </button>
        ))}
      </div>
    </div>
  );

  const LogoUploadCard = ({ logo }) => (
    <div className="border-2 border-dashed border-gray-200 rounded-lg p-6 text-center hover:border-orange-400 transition-colors group">
      <div className="mb-4">
        {uploadedLogos[logo.id] ? (
          <img 
            src={uploadedLogos[logo.id]} 
            alt="Uploaded logo" 
            className="w-16 h-16 mx-auto object-contain rounded"
          />
        ) : (
          <div 
            className="w-16 h-16 mx-auto rounded flex items-center justify-center group-hover:bg-orange-50 transition-colors"
            style={{ backgroundColor: `${selectedColor}15` }}
          >
            <Image 
              className="w-8 h-8 group-hover:text-orange-500 transition-colors" 
              style={{ color: selectedColor }}
            />
          </div>
        )}
      </div>
      
      <h3 className="font-medium text-gray-900 mb-1">{logo.title}</h3>
      <p className="text-sm text-gray-500 mb-4">{logo.size}</p>
      
      <label className="inline-flex items-center gap-2 px-4 py-2 bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-md cursor-pointer transition-colors">
        <Upload className="w-4 h-4" />
        <span className="text-sm font-medium">Upload</span>
        <input
          type="file"
          accept="image/*"
          onChange={(e) => handleFileUpload(logo.id, e)}
          className="hidden"
        />
      </label>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gray-50 min-h-screen">
      {/* Logo Upload Section */}
      <div className="bg-white rounded-xl shadow-sm p-8 mb-6">
        <div className="flex items-center gap-3 mb-2">
          <Image className="w-6 h-6" style={{ color: selectedColor }} />
          <h2 className="text-xl font-semibold text-gray-900">Favicons</h2>
        </div>
        <p className="text-gray-600 text-sm mb-6">
          Upload a favicon for your site. <span className="text-blue-600 hover:underline cursor-pointer">Generate Favicon →</span>
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {logoOptions.map((logo) => (
            <LogoUploadCard key={logo.id} logo={logo} />
          ))}
        </div>
      </div>

      {/* Theme Color Section */}
      <div className="bg-white rounded-xl shadow-sm p-8">
        <div className="flex items-center gap-3 mb-2">
          <Palette className="w-6 h-6" style={{ color: selectedColor }} />
          <h2 className="text-xl font-semibold text-gray-900">Theme Color</h2>
        </div>
        <p className="text-gray-600 text-sm mb-6">
          Select the theme color for your restaurant.
        </p>

        {/* Current Color Display */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-4">
            <div 
              className="w-12 h-12 rounded-lg border-2 border-gray-200"
              style={{ backgroundColor: selectedColor }}
            ></div>
            <div>
              <p className="font-medium text-gray-900">Selected Color</p>
              <p className="text-sm text-gray-500 font-mono">{selectedColor}</p>
            </div>
          </div>
        </div>

        <ColorPalette colors={professionalColors} title="Professional" />
        <ColorPalette colors={pastelColors} title="Pastel" />
        <ColorPalette colors={warmColors} title="Warm" />

        {/* Custom Color Input */}
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Custom Color</h4>
          <div className="flex items-center gap-3">
            <input
              type="color"
              value={selectedColor}
              onChange={(e) => setSelectedColor(e.target.value)}
              className="w-12 h-12 rounded border-2 border-gray-200 cursor-pointer"
            />
            <input
              type="text"
              value={selectedColor}
              onChange={(e) => setSelectedColor(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md font-mono text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder="#FF6500"
            />
          </div>
        </div>
      </div>

      {/* Apply Button */}
      <div className="mt-6 flex justify-end">
        <button 
          className="px-6 py-3 text-white font-medium rounded-lg transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
          style={{ backgroundColor: selectedColor }}
        >
          Apply Theme Settings
        </button>
      </div>
    </div>
  );
};

export default ThemeSettings;