import React, { useState } from 'react';

const EmailSettings = () => {
  const [settings, setSettings] = useState({
    newOrderReceived: true,
    reservationConfirmation: true,
    newReservationReceived: true,
    orderBill: true,
    staffWelcomeEmail: true
  });

  const handleToggle = (setting) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const ToggleSwitch = ({ enabled, onToggle }) => (
    <button
      onClick={onToggle}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
        enabled ? 'bg-[#FF6500]' : 'bg-gray-300'
      }`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
          enabled ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-2xl font-semibold text-gray-900 mb-8">Notification Settings</h1>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="space-y-8">
            {/* New Order Received */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">New Order Received</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Restaurant admin will receive an email when a new order is placed by the customer.
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.newOrderReceived}
                onToggle={() => handleToggle('newOrderReceived')}
              />
            </div>

            {/* Reservation Confirmation */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Reservation Confirmation</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Customer will receive an email after making the reservation.
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.reservationConfirmation}
                onToggle={() => handleToggle('reservationConfirmation')}
              />
            </div>

            {/* New Reservation Received */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">New Reservation Received</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Restaurant admin will receive an email when a new reservation is made by the customer.
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.newReservationReceived}
                onToggle={() => handleToggle('newReservationReceived')}
              />
            </div>

            {/* Order Bill */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Order Bill</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Customer will receive the order bill via email.
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.orderBill}
                onToggle={() => handleToggle('orderBill')}
              />
            </div>

            {/* Staff Welcome Email */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Staff Welcome Email</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Staff Member will welcome email when you add a new staff member.
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.staffWelcomeEmail}
                onToggle={() => handleToggle('staffWelcomeEmail')}
              />
            </div>
          </div>

          {/* Save Button */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <button className="bg-[#FF6500] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#e55a00] transition-colors">
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailSettings;