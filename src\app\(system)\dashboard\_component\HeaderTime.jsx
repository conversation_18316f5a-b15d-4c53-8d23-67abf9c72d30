// "use client"
// import { Calendar } from 'lucide-react';
// import React, { useEffect, useState } from 'react'

// const HeaderTime = () => {
//     const [dateTime, setDateTime] = useState("");

//   useEffect(() => {
//     const arabicDays = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"];
//     const arabicMonths = [
//       "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
//       "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
//     ];
//     function formatDate() {
//       const now = new Date();
//       const dayName = arabicDays[now.getDay()];
//       const day = now.getDate();
//       const month = arabicMonths[now.getMonth()];
//       let hours = now.getHours();
//       const minutes = now.getMinutes().toString().padStart(2, '0');
//       const isPM = hours >= 12;
//       const period = isPM ? 'م' : 'ص';
//       hours = hours % 12;
//       if (hours === 0) hours = 12;
//       return `${dayName}، ${day} ${month}، ${hours}:${minutes} ${period}`;
//     }
//     setDateTime(formatDate());
//     const interval = setInterval(() => {
//       setDateTime(formatDate());
//     }, 60000);
//     return () => clearInterval(interval);
//   }, []);
//   return (
//    <div className="flex items-center justify-between">
//         <div>
//           <h1 className="text-3xl font-bold text-gray-900">لوحة القيادة</h1>
//           <p className="text-gray-600 mt-1">مرحباً بك، إليك ملخص أعمالك اليوم</p>
//         </div>
//         <div className="flex items-center space-x-3">
//           <span className="flex items-center text-gray-800 text-md">
//             <Calendar className="h-4 w-4 ml-2 inline" />
//             {dateTime}
//           </span>
//         </div>
//       </div>
//   )
// }

// export default HeaderTime
"use client"
import { Calendar } from 'lucide-react';
import React, { useEffect, useState } from 'react'

const HeaderTime = () => {
  const [dateTime, setDateTime] = useState("");

  useEffect(() => {
    const englishDays = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    const englishMonths = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    function formatDate() {
      const now = new Date();
      const dayName = englishDays[now.getDay()];
      const day = now.getDate();
      const month = englishMonths[now.getMonth()];
      let hours = now.getHours();
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const isPM = hours >= 12;
      const period = isPM ? 'PM' : 'AM';
      hours = hours % 12;
      if (hours === 0) hours = 12;
      return `${dayName}, ${day} ${month}, ${hours}:${minutes} ${period}`;
    }

    setDateTime(formatDate());
    const interval = setInterval(() => {
      setDateTime(formatDate());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-1">Welcome, here's your business summary for today</p>
      </div>
      <div className="flex items-center space-x-3">
        <span className="flex items-center text-gray-800 text-md">
          <Calendar className="h-4 w-4 ml-2 inline" />
          {dateTime}
        </span>
      </div>
    </div>
  )
}

export default HeaderTime