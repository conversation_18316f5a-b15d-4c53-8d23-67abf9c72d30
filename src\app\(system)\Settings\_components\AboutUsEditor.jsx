"use client"
import React, { useState } from 'react';
import { Bold, Italic, Link, Type, Quote, List, ListOrdered, Save } from 'lucide-react';

const AboutUsEditor = () => {
  const [content, setContent] = useState(`Welcome to our restaurant, where great food and good vibes come together! We're a local, family-owned spot that loves bringing people together over delicious meals and unforgettable moments. Whether you're here for a quick bite, a family dinner, or a celebration, we're all about making your time with us special.

Our menu is packed with dishes made from fresh, quality ingredients because we believe food should taste as good as it makes you feel. From our signature dishes to seasonal specials, there's always something to excite your taste buds.

But we're not just about the food—we're about community. We love seeing familiar faces and welcoming new ones. Our team is a fun, friendly bunch dedicated to serving you with a smile and making sure every visit feels like coming home.

So, come on in, grab a seat, and let us take care of the rest. We can't wait to share our love of food with you!

See you soon! 🍽️ ✨`);

  const [activeFormats, setActiveFormats] = useState({
    bold: false,
    italic: false,
    underline: false,
    strikethrough: false
  });

  const toolbarButtons = [
    { icon: Bold, label: 'Bold', key: 'bold' },
    { icon: Italic, label: 'Italic', key: 'italic' },
    { icon: Link, label: 'Link', key: 'link' },
    { icon: Type, label: 'Text Format', key: 'textFormat' },
    { icon: Quote, label: 'Quote', key: 'quote' },
    { icon: List, label: 'Bullet List', key: 'bulletList' },
    { icon: ListOrdered, label: 'Numbered List', key: 'numberedList' }
  ];

  const handleToolbarClick = (key) => {
    if (key === 'bold' || key === 'italic') {
      setActiveFormats(prev => ({
        ...prev,
        [key]: !prev[key]
      }));
    }
    // In a real implementation, you would handle the formatting here
    console.log(`Format applied: ${key}`);
  };

  const handleSave = () => {
    console.log('Content saved:', content);
    // In a real implementation, this would save to a backend
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900">About Us</h1>
        </div>
        
        <div className="p-6">
          {/* Toolbar */}
          <div className="flex items-center space-x-2 mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
            {toolbarButtons.map(({ icon: Icon, label, key }) => (
              <button
                key={key}
                onClick={() => handleToolbarClick(key)}
                className={`p-2 rounded-md transition-colors hover:bg-gray-200 ${
                  activeFormats[key] 
                    ? 'bg-[#FF6500] text-white hover:bg-[#E55A00]' 
                    : 'text-gray-600 hover:text-gray-800'
                }`}
                title={label}
              >
                <Icon className="w-4 h-4" />
              </button>
            ))}
          </div>

          {/* Text Editor */}
          <div className="relative">
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full h-96 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent text-gray-700 leading-relaxed"
              placeholder="Write your about us content here..."
              style={{
                fontFamily: 'system-ui, -apple-system, sans-serif',
                fontSize: '16px',
                lineHeight: '1.6'
              }}
            />
          </div>

          {/* Character Count */}
          <div className="mt-2 text-sm text-gray-500 text-right">
            {content.length} characters
          </div>

          {/* Save Button */}
          <div className="mt-6 flex justify-start">
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-6 py-3 bg-[#FF6500] text-white font-medium rounded-lg hover:bg-[#E55A00] transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>Save</span>
            </button>
          </div>
        </div>
      </div>

      {/* Preview Section */}
      <div className="mt-6 bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Preview</h2>
        </div>
        
        <div className="p-6">
          <div className="prose prose-gray max-w-none">
            {content.split('\n\n').map((paragraph, index) => (
              <p key={index} className="mb-4 text-gray-700 leading-relaxed">
                {paragraph}
              </p>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutUsEditor;