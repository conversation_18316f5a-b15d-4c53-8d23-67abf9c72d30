import React from 'react';
import { Gift, MoreHorizontal, Check, Sparkles } from 'lucide-react';

const BillingPlanDetails = () => {
  const features = [
    'Change Branch',
    'Export Report',
    'Table Reservation',
    'Payment Gateway Integration',
    'Theme Setting'
  ];

  return (
    <div className="max-w-[95%] mx-auto bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-[#FF6500] rounded-lg flex items-center justify-center">
            <Gift className="w-5 h-5 text-white" />
          </div>
          <h2 className="text-lg font-semibold text-gray-900">Plan Details</h2>
        </div>
        <button className="p-1 hover:bg-gray-100 rounded-full transition-colors">
          <MoreHorizontal className="w-5 h-5 text-gray-500" />
        </button>
      </div>

      {/* Current Plan Name */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 mb-2">Current Plan Name</p>
        <h3 className="text-xl font-semibold text-[#FF6500]">Enterprise plan</h3>
      </div>

      {/* Current Plan Type */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 mb-2">Current Plan Type</p>
        <h3 className="text-xl font-semibold text-[#FF6500]">Standard (Annual)</h3>
      </div>

      {/* License Expire Date */}
      <div className="mb-8">
        <p className="text-sm text-gray-600 mb-2">License Expire On</p>
        <div className="flex items-center gap-2">
          <h3 className="text-xl font-semibold text-[#FF6500]">08 August, 2025</h3>
          <span className="text-sm text-gray-500 font-medium">(30 Days Left)</span>
        </div>
      </div>

      {/* Additional Features */}
      <div className="mb-8">
        <h4 className="text-sm font-medium text-gray-700 mb-4">Additional Features</h4>
        <div className="space-y-3">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                <Check className="w-3 h-3 text-green-600" />
              </div>
              <span className="text-gray-700 text-sm">{feature}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Upgrade Button */}
      <button className="w-full bg-[#FF6500] hover:bg-[#e55a00] text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
        <Sparkles className="w-4 h-4" />
        Upgrade Plan
      </button>
    </div>
  );
};

export default BillingPlanDetails;