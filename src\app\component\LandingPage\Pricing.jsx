import React from "react";
import pricingImage from "../../../../public/images/fatora.png";
import ReusabelLink from "../ReusabelComponent/ReusabelLink";

const Pricing = () => {
  return (
    <div className="relative  rounded-4xl mx-4  max-w-7xl md:mx-auto bg-gradient-to-br from-[#FFD5C7] via-[#FCB190]/30 to-[#FFD5C7] overflow-hidden">
   

      <div className="relative   p-8 ">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6 order-2 lg:order-1">
            <h1 className="font-bold text-2xl  leading-tight text-gray-900">
              نجاح مطعمك يبدأ بالاختيار الصائب لنقطة البيع ونظام إدارة المطاعم
            </h1>
            <p className="text-lg md:text-xl text-gray-700 leading-relaxed text-justify">
              نقطة بيع إيبيسيس تمكّن جميع أنواع المطاعم من إدارة الطلبات وقبول
              المدفوعات وتحسين العمليات اليومية بكل سهولة
            </p>
            <div className="pt-4">
              <ReusabelLink text={"اعرف المزيد"} href={"/"} background={true} />
            </div>

          </div>
          <div className="flex justify-center order-1 lg:order-2">
            <div className="relative">
              <div className="relative  p-8 ">
                <img
                  src={pricingImage.src}
                  alt="Pricing"
                  className="relative rounded-2xl w-full max-w-md h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
