// // // // import React, { useState } from 'react';
// // // // import { ShoppingCart, Minus, Plus, Trash2, Users, Truck, Percent, User, Phone, MapPin, CreditCard, X } from 'lucide-react';

// // // // const OrderSummary = () => {
// // // //   const [orderType, setOrderType] = useState('Dine In');
// // // //   const [pax, setPax] = useState(1);
// // // //   const [deliveryTable, setDeliveryTable] = useState('');
// // // //   const [discount, setDiscount] = useState(0);
// // // //   const [discountType, setDiscountType] = useState('percentage');
// // // //   const [showTableModal, setShowTableModal] = useState(false);
// // // //   const [showDiscountModal, setShowDiscountModal] = useState(false);
// // // //   const [selectedTable, setSelectedTable] = useState('');
// // // //   const [cashier, setCashier] = useState('أحمد محمد');
// // // //   const [waiter, setWaiter] = useState('محمد علي');
// // // //   const [customerData, setCustomerData] = useState({
// // // //     name: '',
// // // //     phone: '',
// // // //     address: '',
// // // //     paymentMethod: 'cash'
// // // //   });

// // // //   const [cart, setCart] = useState([
// // // //     { id: 1, name: 'Flat White', size: 'Small', price: 50.00, quantity: 1 },
// // // //     { id: 2, name: 'Cappuccino', size: 'Large', price: 65.00, quantity: 2 }
// // // //   ]);

// // // //   const tables = [
// // // //     { id: 1, name: 'Table 1', status: 'available' },
// // // //     { id: 2, name: 'Table 2', status: 'occupied' },
// // // //     { id: 3, name: 'Table 3', status: 'reserved' },
// // // //     { id: 4, name: 'Table 4', status: 'available' },
// // // //     { id: 5, name: 'Table 5', status: 'available' },
// // // //     { id: 6, name: 'Table 6', status: 'occupied' }
// // // //   ];

// // // //   const getStatusColor = (status) => {
// // // //     switch(status) {
// // // //       case 'available': return 'bg-green-100 text-green-800 border-green-200';
// // // //       case 'occupied': return 'bg-red-100 text-red-800 border-red-200';
// // // //       case 'reserved': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
// // // //       default: return 'bg-gray-100 text-gray-800 border-gray-200';
// // // //     }
// // // //   };

// // // //   const getStatusText = (status) => {
// // // //     switch(status) {
// // // //       case 'available': return 'متاح';
// // // //       case 'occupied': return 'مشغول';
// // // //       case 'reserved': return 'محجوز';
// // // //       default: return 'غير متاح';
// // // //     }
// // // //   };

// // // //   const addToCart = (item) => {
// // // //     setCart(prev => prev.map(cartItem =>
// // // //       cartItem.id === item.id
// // // //         ? { ...cartItem, quantity: cartItem.quantity + 1 }
// // // //         : cartItem
// // // //     ));
// // // //   };

// // // //   const removeFromCart = (itemId) => {
// // // //     setCart(prev => prev.map(item =>
// // // //       item.id === itemId && item.quantity > 1
// // // //         ? { ...item, quantity: item.quantity - 1 }
// // // //         : item
// // // //     ));
// // // //   };

// // // //   const deleteFromCart = (itemId) => {
// // // //     setCart(prev => prev.filter(item => item.id !== itemId));
// // // //   };

// // // //   const getTotalItems = () => cart.reduce((total, item) => total + item.quantity, 0);
// // // //   const getTotalPrice = () => cart.reduce((total, item) => total + (item.price * item.quantity), 0);
// // // //   const getDiscountAmount = () => {
// // // //     const total = getTotalPrice();
// // // //     return discountType === 'percentage' ? (total * discount) / 100 : discount;
// // // //   };
// // // //   const getTax = () => {
// // // //     const afterDiscount = getTotalPrice() - getDiscountAmount();
// // // //     return afterDiscount * 0.14;
// // // //   };
// // // //   const getFinalTotal = () => getTotalPrice() - getDiscountAmount() + getTax();

// // // //   const applyDiscount = (amount, type) => {
// // // //     setDiscount(amount);
// // // //     setDiscountType(type);
// // // //     setShowDiscountModal(false);
// // // //   };

// // // //   const TableModal = () => (
// // // //     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
// // // //       <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// // // //         <div className="flex justify-between items-center mb-4">
// // // //           <h3 className="text-lg font-bold">اختر طاولة</h3>
// // // //           <button
// // // //             onClick={() => setShowTableModal(false)}
// // // //             className="text-gray-500 hover:text-gray-700"
// // // //           >
// // // //             <X className="w-5 h-5" />
// // // //           </button>
// // // //         </div>
// // // //         <div className="grid grid-cols-2 gap-3">
// // // //           {tables.map(table => (
// // // //             <button
// // // //               key={table.id}
// // // //               onClick={() => {
// // // //                 if (table.status === 'available') {
// // // //                   setSelectedTable(table.name);
// // // //                   setShowTableModal(false);
// // // //                 }
// // // //               }}
// // // //               disabled={table.status !== 'available'}
// // // //               className={`p-3 rounded border-2 text-sm font-medium transition-all ${
// // // //                 table.status === 'available'
// // // //                   ? 'hover:bg-orange-50 hover:border-orange-300 cursor-pointer'
// // // //                   : 'cursor-not-allowed opacity-60'
// // // //               } ${getStatusColor(table.status)}`}
// // // //             >
// // // //               <div className="font-semibold">{table.name}</div>
// // // //               <div className="text-xs">{getStatusText(table.status)}</div>
// // // //             </button>
// // // //           ))}
// // // //         </div>
// // // //       </div>
// // // //     </div>
// // // //   );

// // // //   const DiscountModal = () => {
// // // //     const [tempDiscount, setTempDiscount] = useState(0);
// // // //     const [tempType, setTempType] = useState('percentage');

// // // //     return (
// // // //       <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
// // // //         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// // // //           <div className="flex justify-between items-center mb-4">
// // // //             <h3 className="text-lg font-bold">إضافة خصم</h3>
// // // //             <button
// // // //               onClick={() => setShowDiscountModal(false)}
// // // //               className="text-gray-500 hover:text-gray-700"
// // // //             >
// // // //               <X className="w-5 h-5" />
// // // //             </button>
// // // //           </div>

// // // //           <div className="space-y-4">
// // // //             <div className="flex gap-2 mb-4">
// // // //               <button
// // // //                 onClick={() => setTempType('percentage')}
// // // //                 className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
// // // //                   tempType === 'percentage'
// // // //                     ? 'bg-[#FF6500] text-white'
// // // //                     : 'bg-white text-[#FF6500] border border-[#FF6500]'
// // // //                 }`}
// // // //               >
// // // //                 نسبة مئوية %
// // // //               </button>
// // // //               <button
// // // //                 onClick={() => setTempType('fixed')}
// // // //                 className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
// // // //                   tempType === 'fixed'
// // // //                     ? 'bg-[#FF6500] text-white'
// // // //                     : 'bg-white text-[#FF6500] border border-[#FF6500]'
// // // //                 }`}
// // // //               >
// // // //                 مبلغ ثابت
// // // //               </button>
// // // //             </div>

// // // //             <div>
// // // //               <label className="block text-sm font-medium mb-2">
// // // //                 {tempType === 'percentage' ? 'النسبة المئوية' : 'المبلغ (ج.م)'}
// // // //               </label>
// // // //               <input
// // // //                 type="number"
// // // //                 value={tempDiscount}
// // // //                 onChange={(e) => setTempDiscount(Number(e.target.value))}
// // // //                 className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // // //                 placeholder={tempType === 'percentage' ? '10' : '50'}
// // // //               />
// // // //             </div>

// // // //             <div className="flex gap-2">
// // // //               <button
// // // //                 onClick={() => setShowDiscountModal(false)}
// // // //                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
// // // //               >
// // // //                 إلغاء
// // // //               </button>
// // // //               <button
// // // //                 onClick={() => applyDiscount(tempDiscount, tempType)}
// // // //                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
// // // //               >
// // // //                 تطبيق
// // // //               </button>
// // // //             </div>
// // // //           </div>
// // // //         </div>
// // // //       </div>
// // // //     );
// // // //   };

// // // //   return (
// // // //     <div className="lg:col-span-1">
// // // //       <div className="bg-white rounded-lg shadow-lg p-4 sticky top-8 text-sm">
// // // //         {/* Order Type Buttons */}
// // // //         <div className="flex justify-center mb-4 gap-2">
// // // //           {['Dine In', 'Delivery', 'Pickup'].map((type) => (
// // // //             <button
// // // //               key={type}
// // // //               onClick={() => setOrderType(type)}
// // // //               className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
// // // //                 orderType === type
// // // //                   ? 'bg-[#FF6500] text-white shadow-md'
// // // //                   : 'bg-white text-[#FF6500] border border-[#FF6500] hover:bg-orange-50'
// // // //               }`}
// // // //             >
// // // //               {type}
// // // //             </button>
// // // //           ))}
// // // //         </div>

// // // //         {/* Order Header */}
// // // //         <div className="flex items-center justify-between mb-4 pb-2 border-b">
// // // //           <h2 className="text-lg font-bold text-gray-800">Order #113</h2>
// // // //         </div>

// // // //         {/* Staff Info for Dine In */}
// // // //         {orderType === 'Dine In' && (
// // // //           <div className="mb-4 p-3 bg-gray-50 rounded-lg">
// // // //             <div className="flex items-center gap-2 mb-2">
// // // //               <User className="w-4 h-4 text-gray-500" />
// // // //               <span className="text-sm font-medium">الكاشير: {cashier}</span>
// // // //             </div>
// // // //             <div className="flex items-center gap-2">
// // // //               <User className="w-4 h-4 text-gray-500" />
// // // //               <span className="text-sm font-medium">الويتر: {waiter}</span>
// // // //             </div>
// // // //           </div>
// // // //         )}

// // // //         {/* Customer Data for Pickup/Delivery */}
// // // //         {(orderType === 'Pickup' || orderType === 'Delivery') && (
// // // //           <div className="mb-4 p-3 bg-gray-50 rounded-lg space-y-3">
// // // //             <h3 className="font-medium text-gray-800">بيانات العميل</h3>

// // // //             <div className="relative">
// // // //               <User className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// // // //               <input
// // // //                 type="text"
// // // //                 placeholder="اسم العميل"
// // // //                 value={customerData.name}
// // // //                 onChange={(e) => setCustomerData({...customerData, name: e.target.value})}
// // // //                 className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // // //               />
// // // //             </div>

// // // //             <div className="relative">
// // // //               <Phone className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// // // //               <input
// // // //                 type="tel"
// // // //                 placeholder="رقم الهاتف"
// // // //                 value={customerData.phone}
// // // //                 onChange={(e) => setCustomerData({...customerData, phone: e.target.value})}
// // // //                 className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // // //               />
// // // //             </div>

// // // //             {orderType === 'Delivery' && (
// // // //               <div className="relative">
// // // //                 <MapPin className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// // // //                 <input
// // // //                   type="text"
// // // //                   placeholder="العنوان"
// // // //                   value={customerData.address}
// // // //                   onChange={(e) => setCustomerData({...customerData, address: e.target.value})}
// // // //                   className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // // //                 />
// // // //               </div>
// // // //             )}

// // // //             <div>
// // // //               <label className="block text-sm font-medium mb-2">طريقة الدفع</label>
// // // //               <select
// // // //                 value={customerData.paymentMethod}
// // // //                 onChange={(e) => setCustomerData({...customerData, paymentMethod: e.target.value})}
// // // //                 className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // // //               >
// // // //                 <option value="cash">كاش</option>
// // // //                 <option value="vodafone">فودافون كاش</option>
// // // //                 <option value="instapay">إنستاباي</option>
// // // //               </select>
// // // //             </div>
// // // //           </div>
// // // //         )}

// // // //         {/* Order Items Table */}
// // // //         <div className="mb-4">
// // // //           <div className="grid grid-cols-6 gap-2 text-xs font-medium text-gray-600 mb-2 px-2">
// // // //             <div className="col-span-2">ITEM NAME</div>
// // // //             <div className="text-center">QTY</div>
// // // //             <div className="text-center">PRICE</div>
// // // //             <div className="text-center">AMOUNT</div>
// // // //             <div className="text-center">ACTION</div>
// // // //           </div>

// // // //           {cart.length === 0 ? (
// // // //             <div className="text-center py-6 text-gray-500">
// // // //               <ShoppingCart className="w-8 h-8 mx-auto mb-2 text-gray-300" />
// // // //               <p className="text-xs">No items in cart</p>
// // // //             </div>
// // // //           ) : (
// // // //             <div className="space-y-2">
// // // //               {cart.map((item) => (
// // // //                 <div key={item.id} className="grid grid-cols-6 gap-2 items-center p-3 bg-gray-50 rounded-lg text-xs border border-gray-200">
// // // //                   <div className="col-span-2">
// // // //                     <div className="font-medium text-gray-800">{item.name}</div>
// // // //                     {item.size && <div className="text-gray-500">{item.size}</div>}
// // // //                   </div>
// // // //                   <div className="text-center">
// // // //                     <div className="flex items-center justify-center gap-1">
// // // //                       <button
// // // //                         onClick={() => removeFromCart(item.id)}
// // // //                         className="w-6 h-6 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors border border-gray-300"
// // // //                       >
// // // //                         <Minus className="w-3 h-3" />
// // // //                       </button>
// // // //                       <span className="w-8 text-center font-medium">{item.quantity}</span>
// // // //                       <button
// // // //                         onClick={() => addToCart(item)}
// // // //                         className="w-6 h-6 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#E55A00] transition-colors"
// // // //                       >
// // // //                         <Plus className="w-3 h-3" />
// // // //                       </button>
// // // //                     </div>
// // // //                   </div>
// // // //                   <div className="text-center font-medium">{item.price.toFixed(2)} L.E</div>
// // // //                   <div className="text-center font-medium text-gray-800">{(item.price * item.quantity).toFixed(2)} L.E</div>
// // // //                   <div className="text-center">
// // // //                     <button
// // // //                       onClick={() => deleteFromCart(item.id)}
// // // //                       className="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
// // // //                     >
// // // //                       <Trash2 className="w-3 h-3" />
// // // //                     </button>
// // // //                   </div>
// // // //                 </div>
// // // //               ))}
// // // //             </div>
// // // //           )}
// // // //         </div>

// // // //         {/* Dine In Specific Fields */}
// // // //         {orderType === 'Dine In' && (
// // // //           <div className="mb-4 space-y-3">
// // // //             <div className="flex items-center gap-2">
// // // //               <Users className="w-4 h-4 text-gray-400" />
// // // //               <span className="text-sm font-medium">عدد الأشخاص:</span>
// // // //               <div className="flex items-center gap-1 ml-auto">
// // // //                 <button
// // // //                   onClick={() => setPax(Math.max(1, pax - 1))}
// // // //                   className="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
// // // //                 >
// // // //                   <Minus className="w-3 h-3" />
// // // //                 </button>
// // // //                 <span className="w-8 text-center font-medium">{pax}</span>
// // // //                 <button
// // // //                   onClick={() => setPax(pax + 1)}
// // // //                   className="w-6 h-6 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#E55A00] transition-colors"
// // // //                 >
// // // //                   <Plus className="w-3 h-3" />
// // // //                 </button>
// // // //               </div>
// // // //             </div>

// // // //             <button
// // // //               onClick={() => setShowTableModal(true)}
// // // //               className="w-full p-3 bg-white border border-[#FF6500] text-[#FF6500] rounded-lg hover:bg-orange-50 transition-colors font-medium"
// // // //             >
// // // //               {selectedTable ? `الطاولة: ${selectedTable}` : 'اختر طاولة'}
// // // //             </button>
// // // //           </div>
// // // //         )}

// // // //         {/* Add Discount Button */}
// // // //         <button
// // // //           onClick={() => setShowDiscountModal(true)}
// // // //           className="w-full mb-4 py-3 px-4 bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 rounded-lg text-sm hover:from-gray-200 hover:to-gray-300 transition-all flex items-center justify-center gap-2 font-medium"
// // // //         >
// // // //           <Percent className="w-4 h-4" />
// // // //           إضافة خصم
// // // //         </button>

// // // //         {/* Order Summary */}
// // // //         <div className="space-y-2 mb-4 p-3 bg-gray-50 rounded-lg">
// // // //           <div className="flex justify-between text-sm">
// // // //             <span>عدد الأصناف</span>
// // // //             <span className="font-medium">{getTotalItems()}</span>
// // // //           </div>
// // // //           <div className="flex justify-between text-sm">
// // // //             <span>المجموع الفرعي</span>
// // // //             <span className="font-medium">{getTotalPrice().toFixed(2)} L.E</span>
// // // //           </div>
// // // //           {discount > 0 && (
// // // //             <div className="flex justify-between text-sm text-green-600">
// // // //               <span>خصم ({discountType === 'percentage' ? `${discount}%` : `${discount} L.E`})</span>
// // // //               <span className="font-medium">-{getDiscountAmount().toFixed(2)} L.E</span>
// // // //             </div>
// // // //           )}
// // // //           <div className="flex justify-between text-sm">
// // // //             <span>قيمة مضافة (14%)</span>
// // // //             <span className="font-medium">{getTax().toFixed(2)} L.E</span>
// // // //           </div>
// // // //           <div className="flex justify-between text-lg font-bold border-t pt-2 text-gray-800">
// // // //             <span>الإجمالي</span>
// // // //             <span>{getFinalTotal().toFixed(2)} L.E</span>
// // // //           </div>
// // // //         </div>

// // // //         {/* Action Buttons */}
// // // //         <div className="space-y-2">
// // // //           <div className="grid grid-cols-2 gap-2">
// // // //             <button className="bg-gray-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-gray-700 transition-colors font-medium">
// // // //               KOT
// // // //             </button>
// // // //             <button className="bg-gray-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-gray-700 transition-colors font-medium">
// // // //               KOT & Print
// // // //             </button>
// // // //           </div>

// // // //           <button className="w-full bg-blue-600 text-white py-3 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors font-medium">
// // // //             إصدار الفاتورة
// // // //           </button>

// // // //           <div className="grid grid-cols-2 gap-2">
// // // //             <button className="bg-green-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-green-700 transition-colors font-medium">
// // // //               فاتورة ودفع
// // // //             </button>
// // // //             <button className="bg-blue-400 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-500 transition-colors font-medium">
// // // //               فاتورة وطباعة
// // // //             </button>
// // // //           </div>
// // // //         </div>
// // // //       </div>

// // // //       {/* Modals */}
// // // //       {showTableModal && <TableModal />}
// // // //       {showDiscountModal && <DiscountModal />}
// // // //     </div>
// // // //   );
// // // // };

// // // // export default OrderSummary;
// // // import React, { useState } from 'react';
// // // import { ShoppingCart, Minus, Plus, Trash2, Users, Truck, Percent, User, Phone, MapPin, CreditCard, X, MessageSquare, Edit3 } from 'lucide-react';

// // // const OrderSummary = ({ cart, setCart, addToCart, removeFromCart, deleteFromCart }) => {
// // //   const [orderType, setOrderType] = useState('Dine In');
// // //   const [pax, setPax] = useState(1);
// // //   const [deliveryTable, setDeliveryTable] = useState('');
// // //   const [discount, setDiscount] = useState(0);
// // //   const [discountType, setDiscountType] = useState('percentage');
// // //   const [showTableModal, setShowTableModal] = useState(false);
// // //   const [showDiscountModal, setShowDiscountModal] = useState(false);
// // //   const [showCommentModal, setShowCommentModal] = useState(false);
// // //   const [selectedTable, setSelectedTable] = useState('');
// // //   const [selectedItemForComment, setSelectedItemForComment] = useState(null);
// // //   const [cashier, setCashier] = useState('Ahmed Mohamed');
// // //   const [waiter, setWaiter] = useState('Mohamed Ali');
// // //   const [customerData, setCustomerData] = useState({
// // //     name: '',
// // //     phone: '',
// // //     address: '',
// // //     paymentMethod: 'cash'
// // //   });

// // // //   const [cart, setCart] = useState([
// // // //     {
// // // //       id: 1,
// // // //       name: 'Flat White',
// // // //       size: 'Small',
// // // //       price: 50.00,
// // // //       quantity: 1,
// // // //       image: 'https://images.unsplash.com/photo-1570968915860-54d5c301fa9f?w=100&h=100&fit=crop&crop=center',
// // // //       comment: ''
// // // //     },
// // // //     {
// // // //       id: 2,
// // // //       name: 'Cappuccino',
// // // //       size: 'Large',
// // // //       price: 65.00,
// // // //       quantity: 2,
// // // //       image: 'https://images.unsplash.com/photo-1534778101976-62847782c213?w=100&h=100&fit=crop&crop=center',
// // // //       comment: ''
// // // //     }
// // // //   ]);

// // //   const tables = [
// // //     { id: 1, name: 'Table 1', number: '01', status: 'available', icon: '🍽️' },
// // //     { id: 2, name: 'Table 2', number: '02', status: 'occupied', icon: '🍽️' },
// // //     { id: 3, name: 'Table 3', number: '03', status: 'reserved', icon: '🍽️' },
// // //     { id: 4, name: 'Table 4', number: '04', status: 'available', icon: '🍽️' },
// // //     { id: 5, name: 'Table 5', number: '05', status: 'available', icon: '🍽️' },
// // //     { id: 6, name: 'Table 6', number: '06', status: 'occupied', icon: '🍽️' }
// // //   ];

// // //   const getStatusColor = (status) => {
// // //     switch(status) {
// // //       case 'available': return 'bg-green-100 text-green-800 border-green-200';
// // //       case 'occupied': return 'bg-red-100 text-red-800 border-red-200';
// // //       case 'reserved': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
// // //       default: return 'bg-gray-100 text-gray-800 border-gray-200';
// // //     }
// // //   };

// // //   const getStatusText = (status) => {
// // //     switch(status) {
// // //       case 'available': return 'Available';
// // //       case 'occupied': return 'Occupied';
// // //       case 'reserved': return 'Reserved';
// // //       default: return 'Unavailable';
// // //     }
// // //   };

// // // //   const addToCart = (item) => {
// // // //     setCart(prev => prev.map(cartItem =>
// // // //       cartItem.id === item.id
// // // //         ? { ...cartItem, quantity: cartItem.quantity + 1 }
// // // //         : cartItem
// // // //     ));
// // // //   };

// // // //   const removeFromCart = (itemId) => {
// // // //     setCart(prev => prev.map(item =>
// // // //       item.id === itemId && item.quantity > 1
// // // //         ? { ...item, quantity: item.quantity - 1 }
// // // //         : item
// // // //     ));
// // // //   };

// // // //   const deleteFromCart = (itemId) => {
// // // //     setCart(prev => prev.filter(item => item.id !== itemId));
// // // //   };

// // //   const updateItemComment = (itemId, comment) => {
// // //     setCart(prev => prev.map(item =>
// // //       item.id === itemId
// // //         ? { ...item, comment: comment }
// // //         : item
// // //     ));
// // //   };

// // //   const getTotalItems = () => cart.reduce((total, item) => total + item.quantity, 0);
// // //   const getTotalPrice = () => cart.reduce((total, item) => total + (item.price * item.quantity), 0);
// // //   const getDiscountAmount = () => {
// // //     const total = getTotalPrice();
// // //     return discountType === 'percentage' ? (total * discount) / 100 : discount;
// // //   };
// // //   const getTax = () => {
// // //     const afterDiscount = getTotalPrice() - getDiscountAmount();
// // //     return afterDiscount * 0.14;
// // //   };
// // //   const getFinalTotal = () => getTotalPrice() - getDiscountAmount() + getTax();

// // //   const applyDiscount = (amount, type) => {
// // //     setDiscount(amount);
// // //     setDiscountType(type);
// // //     setShowDiscountModal(false);
// // //   };

// // //   const openCommentModal = (item) => {
// // //     setSelectedItemForComment(item);
// // //     setShowCommentModal(true);
// // //   };

// // //   const CommentModal = () => {
// // //     const [tempComment, setTempComment] = useState(selectedItemForComment?.comment || '');

// // //     const handleSaveComment = () => {
// // //       if (selectedItemForComment) {
// // //         updateItemComment(selectedItemForComment.id, tempComment);
// // //         setShowCommentModal(false);
// // //         setSelectedItemForComment(null);
// // //       }
// // //     };

// // //     return (
// // //       <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
// // //         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// // //           <div className="flex justify-between items-center mb-4">
// // //             <h3 className="text-lg font-bold">Add Comment</h3>
// // //             <button
// // //               onClick={() => {
// // //                 setShowCommentModal(false);
// // //                 setSelectedItemForComment(null);
// // //               }}
// // //               className="text-gray-500 hover:text-gray-700"
// // //             >
// // //               <X className="w-5 h-5" />
// // //             </button>
// // //           </div>

// // //           <div className="space-y-4">
// // //             <div>
// // //               <label className="block text-sm font-medium mb-2">
// // //                 Comment for {selectedItemForComment?.name}
// // //               </label>
// // //               <textarea
// // //                 value={tempComment}
// // //                 onChange={(e) => setTempComment(e.target.value)}
// // //                 className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none resize-none"
// // //                 rows="4"
// // //                 placeholder="Enter your comment here..."
// // //               />
// // //             </div>

// // //             <div className="flex gap-2">
// // //               <button
// // //                 onClick={() => {
// // //                   setShowCommentModal(false);
// // //                   setSelectedItemForComment(null);
// // //                 }}
// // //                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
// // //               >
// // //                 Cancel
// // //               </button>
// // //               <button
// // //                 onClick={handleSaveComment}
// // //                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
// // //               >
// // //                 Save
// // //               </button>
// // //             </div>
// // //           </div>
// // //         </div>
// // //       </div>
// // //     );
// // //   };

// // //   const TableModal = () => (
// // //     <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
// // //       <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// // //         <div className="flex justify-between items-center mb-4">
// // //           <h3 className="text-lg font-bold">Choose Table</h3>
// // //           <button
// // //             onClick={() => setShowTableModal(false)}
// // //             className="text-gray-500 hover:text-gray-700"
// // //           >
// // //             <X className="w-5 h-5" />
// // //           </button>
// // //         </div>
// // //         <div className="grid grid-cols-2 gap-3">
// // //           {tables.map(table => (
// // //             <button
// // //               key={table.id}
// // //               onClick={() => {
// // //                 if (table.status === 'available') {
// // //                   setSelectedTable(table.name);
// // //                   setShowTableModal(false);
// // //                 }
// // //               }}
// // //               disabled={table.status !== 'available'}
// // //               className={`p-4 rounded border-2 text-sm font-medium transition-all ${
// // //                 table.status === 'available'
// // //                   ? 'hover:bg-orange-50 hover:border-orange-300 cursor-pointer'
// // //                   : 'cursor-not-allowed opacity-60'
// // //               } ${getStatusColor(table.status)}`}
// // //             >
// // //               <div className="flex flex-col items-center space-y-2">
// // //                 <div className="text-2xl">{table.icon}</div>
// // //                 <div className="font-semibold">{table.name}</div>
// // //                 <div className="text-xl font-bold">{table.number}</div>
// // //                 <div className="text-xs">{getStatusText(table.status)}</div>
// // //               </div>
// // //             </button>
// // //           ))}
// // //         </div>
// // //       </div>
// // //     </div>
// // //   );

// // //   const DiscountModal = () => {
// // //     const [tempDiscount, setTempDiscount] = useState(0);
// // //     const [tempType, setTempType] = useState('percentage');

// // //     return (
// // //       <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
// // //         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// // //           <div className="flex justify-between items-center mb-4">
// // //             <h3 className="text-lg font-bold">Add Discount</h3>
// // //             <button
// // //               onClick={() => setShowDiscountModal(false)}
// // //               className="text-gray-500 hover:text-gray-700"
// // //             >
// // //               <X className="w-5 h-5" />
// // //             </button>
// // //           </div>

// // //           <div className="space-y-4">
// // //             <div className="flex gap-2 mb-4">
// // //               <button
// // //                 onClick={() => setTempType('percentage')}
// // //                 className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
// // //                   tempType === 'percentage'
// // //                     ? 'bg-[#FF6500] text-white'
// // //                     : 'bg-white text-[#FF6500] border border-[#FF6500]'
// // //                 }`}
// // //               >
// // //                 Percentage %
// // //               </button>
// // //               <button
// // //                 onClick={() => setTempType('fixed')}
// // //                 className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
// // //                   tempType === 'fixed'
// // //                     ? 'bg-[#FF6500] text-white'
// // //                     : 'bg-white text-[#FF6500] border border-[#FF6500]'
// // //                 }`}
// // //               >
// // //                 Fixed Amount
// // //               </button>
// // //             </div>

// // //             <div>
// // //               <label className="block text-sm font-medium mb-2">
// // //                 {tempType === 'percentage' ? 'Percentage' : 'Amount (L.E)'}
// // //               </label>
// // //               <input
// // //                 type="number"
// // //                 value={tempDiscount}
// // //                 onChange={(e) => setTempDiscount(Number(e.target.value))}
// // //                 className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // //                 placeholder={tempType === 'percentage' ? '10' : '50'}
// // //               />
// // //             </div>

// // //             <div className="flex gap-2">
// // //               <button
// // //                 onClick={() => setShowDiscountModal(false)}
// // //                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
// // //               >
// // //                 Cancel
// // //               </button>
// // //               <button
// // //                 onClick={() => applyDiscount(tempDiscount, tempType)}
// // //                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
// // //               >
// // //                 Apply
// // //               </button>
// // //             </div>
// // //           </div>
// // //         </div>
// // //       </div>
// // //     );
// // //   };

// // //   return (
// // //     <div className="lg:col-span-1">
// // //       <div className="bg-white rounded-lg shadow-lg p-4 sticky top-8 text-sm">
// // //         {/* Order Type Buttons */}
// // //         <div className="flex justify-center mb-4 gap-2">
// // //           {['Dine In', 'Delivery', 'Pickup'].map((type) => (
// // //             <button
// // //               key={type}
// // //               onClick={() => setOrderType(type)}
// // //               className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
// // //                 orderType === type
// // //                   ? 'bg-[#FF6500] text-white shadow-md'
// // //                   : 'bg-white text-[#FF6500] border border-[#FF6500] hover:bg-orange-50'
// // //               }`}
// // //             >
// // //               {type}
// // //             </button>
// // //           ))}
// // //         </div>

// // //         {/* Order Header */}
// // //         <div className="flex items-center justify-between mb-4 pb-2 border-b">
// // //           <h2 className="text-lg font-bold text-gray-800">Order #113</h2>
// // //         </div>

// // //         {/* Staff Info for Dine In */}
// // //         {orderType === 'Dine In' && (
// // //           <div className="mb-4 p-3 bg-gray-50 rounded-lg">
// // //             <div className="flex items-center gap-2 mb-2">
// // //               <User className="w-4 h-4 text-gray-500" />
// // //               <span className="text-sm font-medium">Cashier: {cashier}</span>
// // //             </div>
// // //             <div className="flex items-center gap-2">
// // //               <User className="w-4 h-4 text-gray-500" />
// // //               <span className="text-sm font-medium">Waiter: {waiter}</span>
// // //             </div>
// // //           </div>
// // //         )}

// // //         {/* Customer Data for Pickup/Delivery */}
// // //         {(orderType === 'Pickup' || orderType === 'Delivery') && (
// // //           <div className="mb-4 p-3 bg-gray-50 rounded-lg space-y-3">
// // //             <h3 className="font-medium text-gray-800">Customer Data</h3>

// // //             <div className="relative">
// // //               <User className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// // //               <input
// // //                 type="text"
// // //                 placeholder="Customer Name"
// // //                 value={customerData.name}
// // //                 onChange={(e) => setCustomerData({...customerData, name: e.target.value})}
// // //                 className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // //               />
// // //             </div>

// // //             <div className="relative">
// // //               <Phone className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// // //               <input
// // //                 type="tel"
// // //                 placeholder="Phone Number"
// // //                 value={customerData.phone}
// // //                 onChange={(e) => setCustomerData({...customerData, phone: e.target.value})}
// // //                 className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // //               />
// // //             </div>

// // //             {orderType === 'Delivery' && (
// // //               <div className="relative">
// // //                 <MapPin className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// // //                 <input
// // //                   type="text"
// // //                   placeholder="Address"
// // //                   value={customerData.address}
// // //                   onChange={(e) => setCustomerData({...customerData, address: e.target.value})}
// // //                   className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // //                 />
// // //               </div>
// // //             )}

// // //             <div>
// // //               <label className="block text-sm font-medium mb-2">Payment Method</label>
// // //               <select
// // //                 value={customerData.paymentMethod}
// // //                 onChange={(e) => setCustomerData({...customerData, paymentMethod: e.target.value})}
// // //                 className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// // //               >
// // //                 <option value="cash">Cash</option>
// // //                 <option value="vodafone">Vodafone Cash</option>
// // //                 <option value="instapay">InstaPay</option>
// // //               </select>
// // //             </div>
// // //           </div>
// // //         )}

// // //         {/* Order Items Table */}
// // //         <div className="mb-4">
// // //           <div className="grid grid-cols-7 gap-2 text-xs font-medium text-gray-600 mb-2 px-2">
// // //             <div className="col-span-2">ITEM NAME</div>
// // //             <div className="text-center">QTY</div>
// // //             <div className="text-center">PRICE</div>
// // //             <div className="text-center">AMOUNT</div>
// // //             <div className="text-center">COMMENT</div>
// // //             <div className="text-center">ACTION</div>
// // //           </div>

// // //           {cart.length === 0 ? (
// // //             <div className="text-center py-6 text-gray-500">
// // //               <ShoppingCart className="w-8 h-8 mx-auto mb-2 text-gray-300" />
// // //               <p className="text-xs">No items in cart</p>
// // //             </div>
// // //           ) : (
// // //             <div className="space-y-2">
// // //               {cart.map((item) => (
// // //                 <div key={item.id} className="grid grid-cols-7 gap-2 items-center p-3 bg-gray-50 rounded-lg text-xs border border-gray-200">
// // //                   <div className="col-span-2 flex items-center gap-2">
// // //                     <img
// // //                       src={item.image}
// // //                       alt={item.name}
// // //                       className="w-10 h-10 rounded-full object-cover border-2 border-gray-200"
// // //                     />
// // //                     <div>
// // //                       <div className="font-medium text-gray-800">{item.name}</div>
// // //                       {item.size && <div className="text-gray-500">{item.size}</div>}
// // //                       {item.comment && <div className="text-orange-600 text-xs italic">"{item.comment}"</div>}
// // //                     </div>
// // //                   </div>
// // //                   <div className="text-center">
// // //                     <div className="flex items-center justify-center gap-1">
// // //                       <button
// // //                         onClick={() => removeFromCart(item.id)}
// // //                         className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors border border-gray-300"
// // //                       >
// // //                         <Minus className="w-4 h-4" />
// // //                       </button>
// // //                       <span className="w-8 text-center font-medium">{item.quantity}</span>
// // //                       <button
// // //                         onClick={() => addToCart(item)}
// // //                         className="w-8 h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#E55A00] transition-colors"
// // //                       >
// // //                         <Plus className="w-4 h-4" />
// // //                       </button>
// // //                     </div>
// // //                   </div>
// // //                   <div className="text-center font-medium">{item.price.toFixed(2)} L.E</div>
// // //                   <div className="text-center font-medium text-gray-800">{(item.price * item.quantity).toFixed(2)} L.E</div>
// // //                   <div className="text-center">
// // //                     <button
// // //                       onClick={() => openCommentModal(item)}
// // //                       className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
// // //                         item.comment
// // //                           ? 'bg-orange-500 text-white hover:bg-orange-600'
// // //                           : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
// // //                       }`}
// // //                     >
// // //                       <MessageSquare className="w-4 h-4" />
// // //                     </button>
// // //                   </div>
// // //                   <div className="text-center">
// // //                     <button
// // //                       onClick={() => deleteFromCart(item.id)}
// // //                       className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
// // //                     >
// // //                       <Trash2 className="w-4 h-4" />
// // //                     </button>
// // //                   </div>
// // //                 </div>
// // //               ))}
// // //             </div>
// // //           )}
// // //         </div>

// // //         {/* Dine In Specific Fields */}
// // //         {orderType === 'Dine In' && (
// // //           <div className="mb-4 space-y-3">
// // //             <button
// // //               onClick={() => setShowTableModal(true)}
// // //               className="w-full p-3 bg-white border border-[#FF6500] text-[#FF6500] rounded-lg hover:bg-orange-50 transition-colors font-medium"
// // //             >
// // //               {selectedTable ? `Table: ${selectedTable}` : 'Choose Table'}
// // //             </button>
// // //           </div>
// // //         )}

// // //         {/* Add Discount Button */}
// // //         <button
// // //           onClick={() => setShowDiscountModal(true)}
// // //           className="w-full mb-4 py-3 px-4 bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 rounded-lg text-sm hover:from-gray-200 hover:to-gray-300 transition-all flex items-center justify-center gap-2 font-medium"
// // //         >
// // //           <Percent className="w-4 h-4" />
// // //           Add Discount
// // //         </button>

// // //         {/* Order Summary */}
// // //         <div className="space-y-2 mb-4 p-3 bg-gray-50 rounded-lg">
// // //           <div className="flex justify-between text-sm">
// // //             <span>Items Count</span>
// // //             <span className="font-medium">{getTotalItems()}</span>
// // //           </div>
// // //           <div className="flex justify-between text-sm">
// // //             <span>Subtotal</span>
// // //             <span className="font-medium">{getTotalPrice().toFixed(2)} L.E</span>
// // //           </div>
// // //           {discount > 0 && (
// // //             <div className="flex justify-between text-sm text-green-600">
// // //               <span>Discount ({discountType === 'percentage' ? `${discount}%` : `${discount} L.E`})</span>
// // //               <span className="font-medium">-{getDiscountAmount().toFixed(2)} L.E</span>
// // //             </div>
// // //           )}
// // //           <div className="flex justify-between text-sm">
// // //             <span>VAT (14%)</span>
// // //             <span className="font-medium">{getTax().toFixed(2)} L.E</span>
// // //           </div>
// // //           <div className="flex justify-between text-lg font-bold border-t pt-2 text-gray-800">
// // //             <span>Total</span>
// // //             <span>{getFinalTotal().toFixed(2)} L.E</span>
// // //           </div>
// // //         </div>

// // //         {/* Action Buttons */}
// // //         <div className="space-y-2">
// // //           <div className="grid grid-cols-2 gap-2">
// // //             <button className="bg-gray-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-gray-700 transition-colors font-medium">
// // //               KOT
// // //             </button>
// // //             <button className="bg-gray-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-gray-700 transition-colors font-medium">
// // //               KOT & Print
// // //             </button>
// // //           </div>

// // //           <button className="w-full bg-blue-600 text-white py-3 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors font-medium">
// // //             Generate Invoice
// // //           </button>

// // //           <div className="grid grid-cols-2 gap-2">
// // //             <button className="bg-green-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-green-700 transition-colors font-medium">
// // //               Invoice & Pay
// // //             </button>
// // //             <button className="bg-blue-400 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-500 transition-colors font-medium">
// // //               Invoice & Print
// // //             </button>
// // //           </div>
// // //         </div>
// // //       </div>

// // //       {/* Modals */}
// // //       {showTableModal && <TableModal />}
// // //       {showDiscountModal && <DiscountModal />}
// // //       {showCommentModal && <CommentModal />}
// // //     </div>
// // //   );
// // // };

// // // export default OrderSummary;
// // import React, { useState } from "react";
// // import {
// //   ShoppingCart,
// //   Minus,
// //   Plus,
// //   Trash2,
// //   Users,
// //   Truck,
// //   Percent,
// //   User,
// //   Phone,
// //   MapPin,
// //   CreditCard,
// //   X,
// //   MessageSquare,
// //   Edit3,
// // } from "lucide-react";

// // const OrderSummary = ({
// //   cart,
// //   setCart,
// //   addToCart,
// //   removeFromCart,
// //   deleteFromCart,
// // }) => {
// //   const [orderType, setOrderType] = useState("Dine In");
// //   const [pax, setPax] = useState(1);
// //   const [deliveryTable, setDeliveryTable] = useState("");
// //   const [discount, setDiscount] = useState(0);
// //   const [discountType, setDiscountType] = useState("percentage");
// //   const [showTableModal, setShowTableModal] = useState(false);
// //   const [showDiscountModal, setShowDiscountModal] = useState(false);
// //   const [showCommentModal, setShowCommentModal] = useState(false);
// //   const [showCustomerModal, setShowCustomerModal] = useState(false);
// //   const [selectedTable, setSelectedTable] = useState("");
// //   const [selectedItemForComment, setSelectedItemForComment] = useState(null);
// //   const [cashier, setCashier] = useState("Ahmed Mohamed");
// //   const [waiter, setWaiter] = useState("Mohamed Ali");
// //   const [customerData, setCustomerData] = useState({
// //     name: "",
// //     phone: "",
// //     address: "",
// //     paymentMethod: "cash",
// //     paymentPhone: "", // الحقل الجديد لرقم تليفون الدفع
// //   });

// //   const tables = [
// //     { id: 1, name: "Table 1", number: "01", status: "available", icon: "🍽️" },
// //     { id: 2, name: "Table 2", number: "02", status: "occupied", icon: "🍽️" },
// //     { id: 3, name: "Table 3", number: "03", status: "reserved", icon: "🍽️" },
// //     { id: 4, name: "Table 4", number: "04", status: "available", icon: "🍽️" },
// //     { id: 5, name: "Table 5", number: "05", status: "available", icon: "🍽️" },
// //     { id: 6, name: "Table 6", number: "06", status: "occupied", icon: "🍽️" },
// //   ];

// //   const getStatusColor = (status) => {
// //     switch (status) {
// //       case "available":
// //         return "bg-green-100 text-green-800 border-green-200";
// //       case "occupied":
// //         return "bg-red-100 text-red-800 border-red-200";
// //       case "reserved":
// //         return "bg-yellow-100 text-yellow-800 border-yellow-200";
// //       default:
// //         return "bg-gray-100 text-gray-800 border-gray-200";
// //     }
// //   };

// //   const getStatusText = (status) => {
// //     switch (status) {
// //       case "available":
// //         return "Available";
// //       case "occupied":
// //         return "Occupied";
// //       case "reserved":
// //         return "Reserved";
// //       default:
// //         return "Unavailable";
// //     }
// //   };

// //   const updateItemComment = (itemId, comment) => {
// //     setCart((prev) =>
// //       prev.map((item) =>
// //         item.id === itemId ? { ...item, comment: comment } : item
// //       )
// //     );
// //   };

// //   const getTotalItems = () =>
// //     cart.reduce((total, item) => total + item.quantity, 0);
// //   const getTotalPrice = () =>
// //     cart.reduce((total, item) => total + item.price * item.quantity, 0);
// //   const getDiscountAmount = () => {
// //     const total = getTotalPrice();
// //     return discountType === "percentage" ? (total * discount) / 100 : discount;
// //   };
// //   const getTax = () => {
// //     const afterDiscount = getTotalPrice() - getDiscountAmount();
// //     return afterDiscount * 0.14;
// //   };
// //   const getFinalTotal = () => getTotalPrice() - getDiscountAmount() + getTax();

// //   const applyDiscount = (amount, type) => {
// //     setDiscount(amount);
// //     setDiscountType(type);
// //     setShowDiscountModal(false);
// //   };

// //   const openCommentModal = (item) => {
// //     setSelectedItemForComment(item);
// //     setShowCommentModal(true);
// //   };
// //   // const handleKOTGeneration = () => {
// //   //   if (cart.length === 0) return;

// //   //   // التحقق من بيانات العميل
// //   //   if (!customerData.name.trim()) {
// //   //     alert("يجب إدخال اسم العميل أولاً");
// //   //     setShowCustomerModal(true);
// //   //     return;
// //   //   }

// //   //   // التحقق من اختيار الطاولة للـ Dine In
// //   //   if (orderType === "Dine In" && !selectedTable) {
// //   //     alert("يجب اختيار طاولة أولاً");
// //   //     setShowTableModal(true);
// //   //     return;
// //   //   }

// //   //   // التحقق من العنوان للـ Delivery
// //   //   if (orderType === "Delivery" && !customerData.address.trim()) {
// //   //     alert("يجب إدخال عنوان التوصيل أولاً");
// //   //     setShowCustomerModal(true);
// //   //     return;
// //   //   }

// //   //   // التحقق من رقم الهاتف للدفع الإلكتروني
// //   //   if (
// //   //     (customerData.paymentMethod === "vodafone" ||
// //   //       customerData.paymentMethod === "instapay") &&
// //   //     !customerData.paymentPhone.trim()
// //   //   ) {
// //   //     alert("يجب إدخال رقم الهاتف للدفع الإلكتروني");
// //   //     setShowCustomerModal(true);
// //   //     return;
// //   //   }

// //   //   const kotData = {
// //   //     id: Math.floor(Math.random() * 1000) + 200,
// //   //     orderId: 113,
// //   //     date: new Date().toLocaleDateString("en-US", {
// //   //       month: "long",
// //   //       day: "2-digit",
// //   //       hour: "2-digit",
// //   //       minute: "2-digit",
// //   //       hour12: true,
// //   //     }),
// //   //     customerName: customerData.name,
// //   //     table: selectedTable || `${orderType} Order`,
// //   //     orderType: orderType,
// //   //     status: "pending",
// //   //     items: cart.map((item) => ({
// //   //       id: item.id,
// //   //       name: `${item.quantity} x ${item.name}${
// //   //         item.size ? ` (${item.size})` : ""
// //   //       }`,
// //   //       type: "single",
// //   //       status: "pending",
// //   //       comment: item.comment || "",
// //   //       addons: item.addons || [],
// //   //     })),
// //   //   };

// //   //   // حفظ البيانات في localStorage
// //   //   const existingKOTs = JSON.parse(localStorage.getItem("kotItems") || "[]");
// //   //   const updatedKOTs = [...existingKOTs, kotData];
// //   //   localStorage.setItem("kotItems", JSON.stringify(updatedKOTs));

// //   //   // مسح الـ cart
// //   //   setCart([]);

// //   //   // إعادة تعيين البيانات
// //   //   setSelectedTable("");
// //   //   setCustomerData({
// //   //     name: "",
// //   //     phone: "",
// //   //     address: "",
// //   //     paymentMethod: "cash",
// //   //     paymentPhone: "",
// //   //   });

// //   //   // إظهار رسالة نجاح
// //   //   alert("KOT generated successfully!");
// //   // };
// //   const handleKOTGeneration = () => {
// //   if (cart.length === 0) return;

// //   // التحقق من بيانات العميل
// //   if (!customerData.name.trim()) {
// //     alert("يجب إدخال اسم العميل أولاً");
// //     setShowCustomerModal(true);
// //     return;
// //   }

// //   // التحقق من اختيار الطاولة للـ Dine In
// //   if (orderType === "Dine In" && !selectedTable) {
// //     alert("يجب اختيار طاولة أولاً");
// //     setShowTableModal(true);
// //     return;
// //   }

// //   // التحقق من العنوان للـ Delivery
// //   if (orderType === "Delivery" && !customerData.address.trim()) {
// //     alert("يجب إدخال عنوان التوصيل أولاً");
// //     setShowCustomerModal(true);
// //     return;
// //   }

// //   // التحقق من رقم الهاتف للدفع الإلكتروني
// //   if (
// //     (customerData.paymentMethod === "vodafone" ||
// //       customerData.paymentMethod === "instapay") &&
// //     !customerData.paymentPhone.trim()
// //   ) {
// //     alert("يجب إدخال رقم الهاتف للدفع الإلكتروني");
// //     setShowCustomerModal(true);
// //     return;
// //   }

// //   // إعداد المنتجات للـ KOT مع تفصيل كل قطعة
// //   const kotItems = [];
  
// //   cart.forEach((cartItem) => {
// //     // إنشاء منتج منفصل لكل قطعة في الكمية
// //     for (let i = 0; i < cartItem.quantity; i++) {
// //       kotItems.push({
// //         id: `${cartItem.id}-${i}`, // معرف فريد لكل قطعة
// //         originalId: cartItem.id, // المعرف الأصلي للمنتج
// //         name: cartItem.name,
// //         size: cartItem.size || '',
// //         image: cartItem.image,
// //         basePrice: cartItem.originalPrice,
// //         totalPrice: cartItem.price, // السعر مع الإضافات
// //         addons: cartItem.addons || [],
// //         comment: cartItem.comment || '',
// //         status: 'pending',
// //         cookingStartTime: null,
// //         cookingEndTime: null,
// //         quantity: 1 // كل item يمثل قطعة واحدة
// //       });
// //     }
// //   });

// //   const kotData = {
// //     id: Math.floor(Math.random() * 1000) + 200,
// //     orderId: 113,
// //     date: new Date().toLocaleDateString("en-US", {
// //       month: "long",
// //       day: "2-digit",
// //       hour: "2-digit",
// //       minute: "2-digit",
// //       hour12: true,
// //     }),
// //     customerName: customerData.name,
// //     table: selectedTable || `${orderType} Order`,
// //     orderType: orderType,
// //     status: "pending",
// //     items: kotItems
// //   };

// //   // حفظ البيانات في localStorage
// //   const existingKOTs = JSON.parse(localStorage.getItem("kotItems") || "[]");
// //   const updatedKOTs = [...existingKOTs, kotData];
// //   localStorage.setItem("kotItems", JSON.stringify(updatedKOTs));

// //   // مسح الـ cart
// //   setCart([]);

// //   // إعادة تعيين البيانات
// //   setSelectedTable("");
// //   setCustomerData({
// //     name: "",
// //     phone: "",
// //     address: "",
// //     paymentMethod: "cash",
// //     paymentPhone: "",
// //   });

// //   // إظهار رسالة نجاح
// //   alert("KOT generated successfully!");
// // };
// //   const isKOTReady = () => {
// //     if (cart.length === 0) return false;
// //     if (!customerData.name.trim()) return false;
// //     if (orderType === "Dine In" && !selectedTable) return false;
// //     if (orderType === "Delivery" && !customerData.address.trim()) return false;
// //     if (
// //       (customerData.paymentMethod === "vodafone" ||
// //         customerData.paymentMethod === "instapay") &&
// //       !customerData.paymentPhone.trim()
// //     )
// //       return false;
// //     return true;
// //   };
// //   const CustomerModal = () => {
// //     const [tempCustomerData, setTempCustomerData] = useState({
// //       ...customerData,
// //     });

// //     const handleSaveCustomer = () => {
// //       setCustomerData(tempCustomerData);
// //       setShowCustomerModal(false);
// //     };

// //     return (
// //       <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
// //         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// //           <div className="flex justify-between items-center mb-4">
// //             <h3 className="text-lg font-bold">Customer Data</h3>
// //             <button
// //               onClick={() => setShowCustomerModal(false)}
// //               className="text-gray-500 hover:text-gray-700"
// //             >
// //               <X className="w-5 h-5" />
// //             </button>
// //           </div>

// //           <div className="space-y-4">
// //             <div className="relative">
// //               <User className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// //               <input
// //                 type="text"
// //                 placeholder="Customer Name"
// //                 value={tempCustomerData.name}
// //                 onChange={(e) =>
// //                   setTempCustomerData({
// //                     ...tempCustomerData,
// //                     name: e.target.value,
// //                   })
// //                 }
// //                 className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// //               />
// //             </div>

// //             <div className="relative">
// //               <Phone className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// //               <input
// //                 type="tel"
// //                 placeholder="Phone Number"
// //                 value={tempCustomerData.phone}
// //                 onChange={(e) =>
// //                   setTempCustomerData({
// //                     ...tempCustomerData,
// //                     phone: e.target.value,
// //                   })
// //                 }
// //                 className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// //               />
// //             </div>

// //             {orderType === "Delivery" && (
// //               <div className="relative">
// //                 <MapPin className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// //                 <input
// //                   type="text"
// //                   placeholder="Address"
// //                   value={tempCustomerData.address}
// //                   onChange={(e) =>
// //                     setTempCustomerData({
// //                       ...tempCustomerData,
// //                       address: e.target.value,
// //                     })
// //                   }
// //                   className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// //                 />
// //               </div>
// //             )}

// //             {/* حقل رقم تليفون الدفع - يظهر فقط مع فودافون كاش أو انستاي باي */}
// //             {(tempCustomerData.paymentMethod === "vodafone" ||
// //               tempCustomerData.paymentMethod === "instapay") && (
// //               <div className="relative">
// //                 <Phone className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
// //                 <input
// //                   type="tel"
// //                   placeholder={`${
// //                     tempCustomerData.paymentMethod === "vodafone"
// //                       ? "Vodafone Cash"
// //                       : "InstaPay"
// //                   } Phone Number`}
// //                   value={tempCustomerData.paymentPhone}
// //                   onChange={(e) =>
// //                     setTempCustomerData({
// //                       ...tempCustomerData,
// //                       paymentPhone: e.target.value,
// //                     })
// //                   }
// //                   className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// //                 />
// //               </div>
// //             )}

// //             <div className="flex gap-2">
// //               <button
// //                 onClick={() => setShowCustomerModal(false)}
// //                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
// //               >
// //                 Cancel
// //               </button>
// //               <button
// //                 onClick={handleSaveCustomer}
// //                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
// //               >
// //                 Save
// //               </button>
// //             </div>
// //           </div>
// //         </div>
// //       </div>
// //     );
// //   };

// //   const CommentModal = () => {
// //     const [tempComment, setTempComment] = useState(
// //       selectedItemForComment?.comment || ""
// //     );

// //     const handleSaveComment = () => {
// //       if (selectedItemForComment) {
// //         updateItemComment(selectedItemForComment.id, tempComment);
// //         setShowCommentModal(false);
// //         setSelectedItemForComment(null);
// //       }
// //     };

// //     return (
// //       <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
// //         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// //           <div className="flex justify-between items-center mb-4">
// //             <h3 className="text-lg font-bold">Add Comment</h3>
// //             <button
// //               onClick={() => {
// //                 setShowCommentModal(false);
// //                 setSelectedItemForComment(null);
// //               }}
// //               className="text-gray-500 hover:text-gray-700"
// //             >
// //               <X className="w-5 h-5" />
// //             </button>
// //           </div>

// //           <div className="space-y-4">
// //             <div>
// //               <label className="block text-sm font-medium mb-2">
// //                 Comment for {selectedItemForComment?.name}
// //               </label>
// //               <textarea
// //                 value={tempComment}
// //                 onChange={(e) => setTempComment(e.target.value)}
// //                 className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none resize-none"
// //                 rows="4"
// //                 placeholder="Enter your comment here..."
// //               />
// //             </div>

// //             <div className="flex gap-2">
// //               <button
// //                 onClick={() => {
// //                   setShowCommentModal(false);
// //                   setSelectedItemForComment(null);
// //                 }}
// //                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
// //               >
// //                 Cancel
// //               </button>
// //               <button
// //                 onClick={handleSaveComment}
// //                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
// //               >
// //                 Save
// //               </button>
// //             </div>
// //           </div>
// //         </div>
// //       </div>
// //     );
// //   };

// //   const TableModal = () => (
// //     <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
// //       <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// //         <div className="flex justify-between items-center mb-4">
// //           <h3 className="text-lg font-bold">Choose Table</h3>
// //           <button
// //             onClick={() => setShowTableModal(false)}
// //             className="text-gray-500 hover:text-gray-700"
// //           >
// //             <X className="w-5 h-5" />
// //           </button>
// //         </div>
// //         <div className="grid grid-cols-2 gap-3">
// //           {tables.map((table) => (
// //             <button
// //               key={table.id}
// //               onClick={() => {
// //                 if (table.status === "available") {
// //                   setSelectedTable(table.name);
// //                   setShowTableModal(false);
// //                 }
// //               }}
// //               disabled={table.status !== "available"}
// //               className={`p-4 rounded border-2 text-sm font-medium transition-all ${
// //                 table.status === "available"
// //                   ? "hover:bg-orange-50 hover:border-orange-300 cursor-pointer"
// //                   : "cursor-not-allowed opacity-60"
// //               } ${getStatusColor(table.status)}`}
// //             >
// //               <div className="flex flex-col items-center space-y-2">
// //                 <div className="text-2xl">{table.icon}</div>
// //                 <div className="font-semibold">{table.name}</div>
// //                 <div className="text-xl font-bold">{table.number}</div>
// //                 <div className="text-xs">{getStatusText(table.status)}</div>
// //               </div>
// //             </button>
// //           ))}
// //         </div>
// //       </div>
// //     </div>
// //   );

// //   const DiscountModal = () => {
// //     const [tempDiscount, setTempDiscount] = useState(0);
// //     const [tempType, setTempType] = useState("percentage");

// //     return (
// //       <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
// //         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
// //           <div className="flex justify-between items-center mb-4">
// //             <h3 className="text-lg font-bold">Add Discount</h3>
// //             <button
// //               onClick={() => setShowDiscountModal(false)}
// //               className="text-gray-500 hover:text-gray-700"
// //             >
// //               <X className="w-5 h-5" />
// //             </button>
// //           </div>

// //           <div className="space-y-4">
// //             <div className="flex gap-2 mb-4">
// //               <button
// //                 onClick={() => setTempType("percentage")}
// //                 className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
// //                   tempType === "percentage"
// //                     ? "bg-[#FF6500] text-white"
// //                     : "bg-white text-[#FF6500] border border-[#FF6500]"
// //                 }`}
// //               >
// //                 Percentage %
// //               </button>
// //               <button
// //                 onClick={() => setTempType("fixed")}
// //                 className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
// //                   tempType === "fixed"
// //                     ? "bg-[#FF6500] text-white"
// //                     : "bg-white text-[#FF6500] border border-[#FF6500]"
// //                 }`}
// //               >
// //                 Fixed Amount
// //               </button>
// //             </div>

// //             <div>
// //               <label className="block text-sm font-medium mb-2">
// //                 {tempType === "percentage" ? "Percentage" : "Amount (L.E)"}
// //               </label>
// //               <input
// //                 type="number"
// //                 value={tempDiscount}
// //                 onChange={(e) => setTempDiscount(Number(e.target.value))}
// //                 className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
// //                 placeholder={tempType === "percentage" ? "10" : "50"}
// //               />
// //             </div>

// //             <div className="flex gap-2">
// //               <button
// //                 onClick={() => setShowDiscountModal(false)}
// //                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
// //               >
// //                 Cancel
// //               </button>
// //               <button
// //                 onClick={() => applyDiscount(tempDiscount, tempType)}
// //                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
// //               >
// //                 Apply
// //               </button>
// //             </div>
// //           </div>
// //         </div>
// //       </div>
// //     );
// //   };

// //   return (
// //     <div className="lg:col-span-1">
// //       <div className="bg-white rounded-lg shadow-lg p-4 sticky top-8 text-sm">
// //         {/* Order Type Buttons */}
// //         <div className="flex justify-center mb-4 gap-2">
// //           {["Dine In", "Delivery", "Pickup"].map((type) => (
// //             <button
// //               key={type}
// //               onClick={() => setOrderType(type)}
// //               className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
// //                 orderType === type
// //                   ? "bg-[#FF6500] text-white shadow-md"
// //                   : "bg-white text-[#FF6500] border border-[#FF6500] hover:bg-orange-50"
// //               }`}
// //             >
// //               {type}
// //             </button>
// //           ))}
// //         </div>

// //         {/* Order Header */}
// //         <div className="flex items-center justify-between mb-4 pb-2 border-b">
// //           <h2 className="text-lg font-bold text-gray-800">Order #113</h2>
// //         </div>

// //         {/* Staff Info for Dine In */}
// //         {orderType === "Dine In" && (
// //           <div className="mb-4 p-3 bg-gray-50 rounded-lg">
// //             <div className="flex items-center gap-2 mb-2">
// //               <User className="w-4 h-4 text-gray-500" />
// //               <span className="text-sm font-medium">Cashier: {cashier}</span>
// //             </div>
// //             <div className="flex items-center gap-2">
// //               <User className="w-4 h-4 text-gray-500" />
// //               <span className="text-sm font-medium">Waiter: {waiter}</span>
// //             </div>
// //           </div>
// //         )}

// //         {/* Customer Data Button - يظهر لجميع أنواع الطلبات */}
// //         <button
// //           onClick={() => setShowCustomerModal(true)}
// //           className="w-full mb-4 p-3 bg-white border border-[#FF6500] text-[#FF6500] rounded-lg hover:bg-orange-50 transition-colors font-medium flex items-center justify-center gap-2"
// //         >
// //           <User className="w-4 h-4" />
// //           {customerData.name
// //             ? `Customer: ${customerData.name}`
// //             : "Add Customer Data"}
// //         </button>

// //         {/* Order Items Table */}
// //         <div className="mb-4">
// //           <div className="grid grid-cols-6 md:grid-cols-7 gap-1 md:gap-2 text-xs font-medium text-gray-600 mb-2 px-2">
// //             <div className="col-span-2">ITEM NAME</div>
// //             <div className="text-center">QTY</div>
// //             <div className="text-center">PRICE</div>
// //             <div className="text-center">AMOUNT</div>
// //             <div className="text-center">COMMENT</div>
// //             <div className="text-center hidden md:block">ACTION</div>
// //           </div>

// //           {cart.length === 0 ? (
// //             <div className="text-center py-6 text-gray-500">
// //               <ShoppingCart className="w-8 h-8 mx-auto mb-2 text-gray-300" />
// //               <p className="text-xs">No items in cart</p>
// //             </div>
// //           ) : (
// //             // في الجزء الخاص بعرض items في الـ cart
// //             <div className="space-y-2">
// //               {cart.map((item) => (
// //                 <div
// //                   key={item.id}
// //                   className="grid grid-cols-6 md:grid-cols-7 gap-1 md:gap-2 items-start p-2 md:p-3 bg-gray-50 rounded-lg text-xs border border-gray-200"
// //                 >
// //                   {/* اسم المنتج والصورة */}
// //                   <div className="col-span-2 md:col-span-2 flex items-start gap-1 md:gap-2">
// //                     <img
// //                       src={item.image}
// //                       alt={item.name}
// //                       className="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover border-2 border-gray-200 flex-shrink-0"
// //                     />
// //                     <div className="flex-1 min-w-0">
// //                       <div className="font-medium text-gray-800 text-xs md:text-sm truncate">
// //                         {item.name}
// //                       </div>
// //                       {item.size && (
// //                         <div className="text-gray-500 text-xs">{item.size}</div>
// //                       )}

// //                       {/* عرض الـ addons */}
// //                       {item.addons && item.addons.length > 0 && (
// //                         <div className="mt-1 space-y-1">
// //                           {item.addons.map((addon, index) => (
// //                             <div
// //                               key={index}
// //                               className="text-xs text-blue-600 flex justify-between"
// //                             >
// //                               <span className="truncate pr-1">
// //                                 + {addon.name} x{addon.quantity}
// //                               </span>
// //                               <span className="flex-shrink-0">
// //                                 {(addon.price * addon.quantity).toFixed(2)} L.E
// //                               </span>
// //                             </div>
// //                           ))}
// //                         </div>
// //                       )}

// //                       {item.comment && (
// //                         <div className="text-orange-600 text-xs italic mt-1 truncate">
// //                           "{item.comment}"
// //                         </div>
// //                       )}
// //                     </div>
// //                   </div>

// //                   {/* الكمية */}
// //                   <div className="text-center">
// //                     <div className="flex items-center justify-center gap-1">
// //                       <button
// //                         onClick={() => removeFromCart(item.id)}
// //                         className="w-6 h-6 md:w-8 md:h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors border border-gray-300"
// //                       >
// //                         <Minus className="w-3 h-3 md:w-4 md:h-4" />
// //                       </button>
// //                       <span className="w-6 md:w-8 text-center font-medium text-xs md:text-sm">
// //                         {item.quantity}
// //                       </span>
// //                       <button
// //                         onClick={() => addToCart(item)}
// //                         className="w-6 h-6 md:w-8 md:h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#E55A00] transition-colors"
// //                       >
// //                         <Plus className="w-3 h-3 md:w-4 md:h-4" />
// //                       </button>
// //                     </div>
// //                   </div>

// //                   {/* السعر - بس السعر الأساسي بدون إضافات منفصلة */}
// //                   <div className="text-center">
// //                     <div className="font-medium text-xs md:text-sm">
// //                       {item.originalPrice.toFixed(2)} L.E
// //                     </div>
// //                   </div>

// //                   {/* المبلغ الإجمالي - يشمل السعر الأساسي + الإضافات */}
// //                   <div className="text-center font-medium text-gray-800 text-xs md:text-sm">
// //                     {(item.price * item.quantity).toFixed(2)} L.E
// //                   </div>

// //                   {/* زر التعليق */}
// //                   <div className="text-center">
// //                     <button
// //                       onClick={() => openCommentModal(item)}
// //                       className={`w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center transition-colors ${
// //                         item.comment
// //                           ? "bg-orange-500 text-white hover:bg-orange-600"
// //                           : "bg-gray-200 text-gray-600 hover:bg-gray-300"
// //                       }`}
// //                     >
// //                       <MessageSquare className="w-3 h-3 md:w-4 md:h-4" />
// //                     </button>
// //                   </div>

// //                   {/* زر الحذف - يختفي في الموبايل ويظهر في التابلت والديسكتوب */}
// //                   <div className="text-center hidden md:block">
// //                     <button
// //                       onClick={() => deleteFromCart(item.id)}
// //                       className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
// //                     >
// //                       <Trash2 className="w-4 h-4" />
// //                     </button>
// //                   </div>
// //                 </div>
// //               ))}
// //             </div>
// //           )}
// //         </div>

// //         {/* Dine In Specific Fields */}
// //         {orderType === "Dine In" && (
// //           <div className="mb-4 space-y-3">
// //             <button
// //               onClick={() => setShowTableModal(true)}
// //               className="w-full p-3 bg-white border border-[#FF6500] text-[#FF6500] rounded-lg hover:bg-orange-50 transition-colors font-medium"
// //             >
// //               {selectedTable ? `Table: ${selectedTable}` : "Choose Table"}
// //             </button>
// //           </div>
// //         )}

// //         {/* Add Discount Button */}
// //         <button
// //           onClick={() => setShowDiscountModal(true)}
// //           className="w-full mb-4 py-3 px-4 bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 rounded-lg text-sm hover:from-gray-200 hover:to-gray-300 transition-all flex items-center justify-center gap-2 font-medium"
// //         >
// //           <Percent className="w-4 h-4" />
// //           Add Discount
// //         </button>

// //         {/* Order Summary */}
// //         <div className="space-y-2 mb-4 p-3 bg-gray-50 rounded-lg">
// //           <div className="flex justify-between text-sm">
// //             <span>Items Count</span>
// //             <span className="font-medium">{getTotalItems()}</span>
// //           </div>
// //           <div className="flex justify-between text-sm">
// //             <span>Subtotal</span>
// //             <span className="font-medium">
// //               {getTotalPrice().toFixed(2)} L.E
// //             </span>
// //           </div>
// //           {discount > 0 && (
// //             <div className="flex justify-between text-sm text-green-600">
// //               <span>
// //                 Discount (
// //                 {discountType === "percentage"
// //                   ? `${discount}%`
// //                   : `${discount} L.E`}
// //                 )
// //               </span>
// //               <span className="font-medium">
// //                 -{getDiscountAmount().toFixed(2)} L.E
// //               </span>
// //             </div>
// //           )}
// //           <div className="flex justify-between text-sm">
// //             <span>VAT (14%)</span>
// //             <span className="font-medium">{getTax().toFixed(2)} L.E</span>
// //           </div>
// //           <div className="flex justify-between text-lg font-bold border-t pt-2 text-gray-800">
// //             <span>Total</span>
// //             <span>{getFinalTotal().toFixed(2)} L.E</span>
// //           </div>
// //         </div>

// //         {/* Action Buttons */}
// //         <div className="space-y-2">
// //           <div className="grid grid-cols-2 gap-2">
// //             <button
// //               onClick={handleKOTGeneration}
// //               disabled={!isKOTReady()}
// //               className={`${
// //                 !isKOTReady()
// //                   ? "bg-gray-400 cursor-not-allowed"
// //                   : "bg-gray-600 hover:bg-gray-700"
// //               } text-white py-2 px-3 rounded-lg text-sm transition-colors font-medium`}
// //             >
// //               KOT
// //             </button>
// //             <button className="bg-gray-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-gray-700 transition-colors font-medium">
// //               KOT & Print
// //             </button>
// //           </div>

// //           <button className="w-full bg-blue-600 text-white py-3 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors font-medium">
// //             Generate Invoice
// //           </button>

// //           <div className="grid grid-cols-2 gap-2">
// //             <button className="bg-green-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-green-700 transition-colors font-medium">
// //               Invoice & Pay
// //             </button>
// //             <button className="bg-blue-400 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-500 transition-colors font-medium">
// //               Invoice & Print
// //             </button>
// //           </div>
// //         </div>
// //       </div>

// //       {/* Modals */}
// //       {showTableModal && <TableModal />}
// //       {showDiscountModal && <DiscountModal />}
// //       {showCommentModal && <CommentModal />}
// //       {showCustomerModal && <CustomerModal />}
// //     </div>
// //   );
// // };

// // export default OrderSummary;
// "use client"
// import { useState } from "react"
// import { ShoppingCart, Minus, Plus, Trash2, Percent, User, Phone, MapPin, X, MessageSquare } from "lucide-react"

// const OrderSummary = ({ cart, setCart, addToCart, removeFromCart, deleteFromCart }) => {
//   const [orderType, setOrderType] = useState("Dine In")
//   const [pax, setPax] = useState(1)
//   const [deliveryTable, setDeliveryTable] = useState("")
//   const [discount, setDiscount] = useState(0)
//   const [discountType, setDiscountType] = useState("percentage")
//   const [showTableModal, setShowTableModal] = useState(false)
//   const [showDiscountModal, setShowDiscountModal] = useState(false)
//   const [showCommentModal, setShowCommentModal] = useState(false)
//   const [showCustomerModal, setShowCustomerModal] = useState(false)
//   const [selectedTable, setSelectedTable] = useState("")
//   const [selectedItemForComment, setSelectedItemForComment] = useState(null)
//   const [cashier, setCashier] = useState("Ahmed Mohamed")
//   const [waiter, setWaiter] = useState("Mohamed Ali")
//   const [customerData, setCustomerData] = useState({
//     name: "",
//     phone: "",
//     address: "",
//     paymentMethod: "cash",
//     paymentPhone: "", // الحقل الجديد لرقم تليفون الدفع
//   })
//   const tables = [
//     { id: 1, name: "Table 1", number: "01", status: "available", icon: "🍽️" },
//     { id: 2, name: "Table 2", number: "02", status: "occupied", icon: "🍽️" },
//     { id: 3, name: "Table 3", number: "03", status: "reserved", icon: "🍽️" },
//     { id: 4, name: "Table 4", number: "04", status: "available", icon: "🍽️" },
//     { id: 5, name: "Table 5", number: "05", status: "available", icon: "🍽️" },
//     { id: 6, name: "Table 6", number: "06", status: "occupied", icon: "🍽️" },
//   ]
//   const getStatusColor = (status) => {
//     switch (status) {
//       case "available":
//         return "bg-green-100 text-green-800 border-green-200"
//       case "occupied":
//         return "bg-red-100 text-red-800 border-red-200"
//       case "reserved":
//         return "bg-yellow-100 text-yellow-800 border-yellow-200"
//       default:
//         return "bg-gray-100 text-gray-800 border-gray-200"
//     }
//   }
//   const getStatusText = (status) => {
//     switch (status) {
//       case "available":
//         return "Available"
//       case "occupied":
//         return "Occupied"
//       case "reserved":
//         return "Reserved"
//       default:
//         return "Unavailable"
//     }
//   }
//   const updateItemComment = (itemId, comment) => {
//     setCart((prev) => prev.map((item) => (item.id === itemId ? { ...item, comment: comment } : item)))
//   }
//   const getTotalItems = () => cart.reduce((total, item) => total + item.quantity, 0)
//   const getTotalPrice = () => cart.reduce((total, item) => total + item.price * item.quantity, 0)
//   const getDiscountAmount = () => {
//     const total = getTotalPrice()
//     return discountType === "percentage" ? (total * discount) / 100 : discount
//   }
//   const getTax = () => {
//     const afterDiscount = getTotalPrice() - getDiscountAmount()
//     return afterDiscount * 0.14
//   }
//   const getFinalTotal = () => getTotalPrice() - getDiscountAmount() + getTax()
//   const applyDiscount = (amount, type) => {
//     setDiscount(amount)
//     setDiscountType(type)
//     setShowDiscountModal(false)
//   }
//   const openCommentModal = (item) => {
//     setSelectedItemForComment(item)
//     setShowCommentModal(true)
//   }
//   const handleKOTGeneration = () => {
//     if (cart.length === 0) return
//     // التحقق من بيانات العميل
//     if (!customerData.name.trim()) {
//       alert("يجب إدخال اسم العميل أولاً")
//       setShowCustomerModal(true)
//       return
//     }
//     // التحقق من اختيار الطاولة للـ Dine In
//     if (orderType === "Dine In" && !selectedTable) {
//       alert("يجب اختيار طاولة أولاً")
//       setShowTableModal(true)
//       return
//     }
//     // التحقق من العنوان للـ Delivery
//     if (orderType === "Delivery" && !customerData.address.trim()) {
//       alert("يجب إدخال عنوان التوصيل أولاً")
//       setShowCustomerModal(true)
//       return
//     }
//     // التحقق من رقم الهاتف للدفع الإلكتروني
//     if (
//       (customerData.paymentMethod === "vodafone" || customerData.paymentMethod === "instapay") &&
//       !customerData.paymentPhone.trim()
//     ) {
//       alert("يجب إدخال رقم الهاتف للدفع الإلكتروني")
//       setShowCustomerModal(true)
//       return
//     }
//     // إعداد المنتجات للـ KOT مع الاحتفاظ بالكمية الأصلية لكل منتج
//     // هذا هو الجزء الذي تم تصحيحه لتجميع المنتجات
//     const kotItems = cart.map((cartItem) => ({
//       id: cartItem.id, // استخدام معرف المنتج الأصلي
//       name: cartItem.name,
//       arabicName: cartItem.arabicName,
//       quantity: cartItem.quantity, // الاحتفاظ بالكمية الأصلية للمنتج
//       price: cartItem.price, // السعر مع الإضافات (لكل وحدة)
//       originalPrice: cartItem.originalPrice, // السعر الأساسي للوحدة
//       image: cartItem.image,
//       category: cartItem.category,
//       notes: cartItem.comment || "", // استخدام comment كـ notes
//       addons: cartItem.addons || [], // الإضافات
//       status: "pending", // الحالة الأولية لعنصر KOT
//       cookingStartTime: null,
//       cookingEndTime: null,
//     }))

//     const kotData = {
//       id: Math.floor(Math.random() * 1000) + 200,
//       orderId: 113, // يمكن توليد هذا بشكل ديناميكي
//       date: new Date().toLocaleDateString("en-US", {
//         month: "long",
//         day: "2-digit",
//         hour: "2-digit",
//         minute: "2-digit",
//         hour12: true,
//       }),
//       customerName: customerData.name,
//       tableNumber: selectedTable || `${orderType} Order`, // تغيير table إلى tableNumber
//       orderType: orderType,
//       priority: "normal", // يمكن جعل هذا ديناميكيًا
//       status: "pending",
//       items: kotItems,
//     }
//     // حفظ البيانات في localStorage
//     const existingKOTs = JSON.parse(localStorage.getItem("kotItems") || "[]")
//     const updatedKOTs = [...existingKOTs, kotData]
//     localStorage.setItem("kotItems", JSON.stringify(updatedKOTs))
//     // مسح الـ cart
//     setCart([])
//     // إعادة تعيين البيانات
//     setSelectedTable("")
//     setCustomerData({
//       name: "",
//       phone: "",
//       address: "",
//       paymentMethod: "cash",
//       paymentPhone: "",
//     })
//     // إظهار رسالة نجاح
//     alert("KOT generated successfully!")
//   }
//   const isKOTReady = () => {
//     if (cart.length === 0) return false
//     if (!customerData.name.trim()) return false
//     if (orderType === "Dine In" && !selectedTable) return false
//     if (orderType === "Delivery" && !customerData.address.trim()) return false
//     if (
//       (customerData.paymentMethod === "vodafone" || customerData.paymentMethod === "instapay") &&
//       !customerData.paymentPhone.trim()
//     )
//       return false
//     return true
//   }
//   const CustomerModal = () => {
//     const [tempCustomerData, setTempCustomerData] = useState({
//       ...customerData,
//     })
//     const handleSaveCustomer = () => {
//       setCustomerData(tempCustomerData)
//       setShowCustomerModal(false)
//     }
//     return (
//       <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
//         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
//           <div className="flex justify-between items-center mb-4">
//             <h3 className="text-lg font-bold">Customer Data</h3>
//             <button onClick={() => setShowCustomerModal(false)} className="text-gray-500 hover:text-gray-700">
//               <X className="w-5 h-5" />
//             </button>
//           </div>
//           <div className="space-y-4">
//             <div className="relative">
//               <User className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
//               <input
//                 type="text"
//                 placeholder="Customer Name"
//                 value={tempCustomerData.name}
//                 onChange={(e) =>
//                   setTempCustomerData({
//                     ...tempCustomerData,
//                     name: e.target.value,
//                   })
//                 }
//                 className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
//               />
//             </div>
//             <div className="relative">
//               <Phone className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
//               <input
//                 type="tel"
//                 placeholder="Phone Number"
//                 value={tempCustomerData.phone}
//                 onChange={(e) =>
//                   setTempCustomerData({
//                     ...tempCustomerData,
//                     phone: e.target.value,
//                   })
//                 }
//                 className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
//               />
//             </div>
//             {orderType === "Delivery" && (
//               <div className="relative">
//                 <MapPin className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
//                 <input
//                   type="text"
//                   placeholder="Address"
//                   value={tempCustomerData.address}
//                   onChange={(e) =>
//                     setTempCustomerData({
//                       ...tempCustomerData,
//                       address: e.target.value,
//                     })
//                   }
//                   className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
//                 />
//               </div>
//             )}
//             {/* حقل رقم تليفون الدفع - يظهر فقط مع فودافون كاش أو انستاي باي */}
//             {(tempCustomerData.paymentMethod === "vodafone" || tempCustomerData.paymentMethod === "instapay") && (
//               <div className="relative">
//                 <Phone className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
//                 <input
//                   type="tel"
//                   placeholder={`${
//                     tempCustomerData.paymentMethod === "vodafone" ? "Vodafone Cash" : "InstaPay"
//                   } Phone Number`}
//                   value={tempCustomerData.paymentPhone}
//                   onChange={(e) =>
//                     setTempCustomerData({
//                       ...tempCustomerData,
//                       paymentPhone: e.target.value,
//                     })
//                   }
//                   className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
//                 />
//               </div>
//             )}
//             <div className="flex gap-2">
//               <button
//                 onClick={() => setShowCustomerModal(false)}
//                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={handleSaveCustomer}
//                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
//               >
//                 Save
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>
//     )
//   }
//   const CommentModal = () => {
//     const [tempComment, setTempComment] = useState(selectedItemForComment?.comment || "")
//     const handleSaveComment = () => {
//       if (selectedItemForComment) {
//         updateItemComment(selectedItemForComment.id, tempComment)
//         setShowCommentModal(false)
//         setSelectedItemForComment(null)
//       }
//     }
//     return (
//       <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
//         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
//           <div className="flex justify-between items-center mb-4">
//             <h3 className="text-lg font-bold">Add Comment</h3>
//             <button
//               onClick={() => {
//                 setShowCommentModal(false)
//                 setSelectedItemForComment(null)
//               }}
//               className="text-gray-500 hover:text-gray-700"
//             >
//               <X className="w-5 h-5" />
//             </button>
//           </div>
//           <div className="space-y-4">
//             <div>
//               <label className="block text-sm font-medium mb-2">Comment for {selectedItemForComment?.name}</label>
//               <textarea
//                 value={tempComment}
//                 onChange={(e) => setTempComment(e.target.value)}
//                 className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none resize-none"
//                 rows="4"
//                 placeholder="Enter your comment here..."
//               />
//             </div>
//             <div className="flex gap-2">
//               <button
//                 onClick={() => {
//                   setShowCommentModal(false)
//                   setSelectedItemForComment(null)
//                 }}
//                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={handleSaveComment}
//                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
//               >
//                 Save
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>
//     )
//   }
//   const TableModal = () => (
//     <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
//       <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
//         <div className="flex justify-between items-center mb-4">
//           <h3 className="text-lg font-bold">Choose Table</h3>
//           <button onClick={() => setShowTableModal(false)} className="text-gray-500 hover:text-gray-700">
//             <X className="w-5 h-5" />
//           </button>
//         </div>
//         <div className="grid grid-cols-2 gap-3">
//           {tables.map((table) => (
//             <button
//               key={table.id}
//               onClick={() => {
//                 if (table.status === "available") {
//                   setSelectedTable(table.name)
//                   setShowTableModal(false)
//                 }
//               }}
//               disabled={table.status !== "available"}
//               className={`p-4 rounded border-2 text-sm font-medium transition-all ${
//                 table.status === "available"
//                   ? "hover:bg-orange-50 hover:border-orange-300 cursor-pointer"
//                   : "cursor-not-allowed opacity-60"
//               } ${getStatusColor(table.status)}`}
//             >
//               <div className="flex flex-col items-center space-y-2">
//                 <div className="text-2xl">{table.icon}</div>
//                 <div className="font-semibold">{table.name}</div>
//                 <div className="text-xl font-bold">{table.number}</div>
//                 <div className="text-xs">{getStatusText(table.status)}</div>
//               </div>
//             </button>
//           ))}
//         </div>
//       </div>
//     </div>
//   )
//   const DiscountModal = () => {
//     const [tempDiscount, setTempDiscount] = useState(0)
//     const [tempType, setTempType] = useState("percentage")
//     return (
//       <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
//         <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
//           <div className="flex justify-between items-center mb-4">
//             <h3 className="text-lg font-bold">Add Discount</h3>
//             <button onClick={() => setShowDiscountModal(false)} className="text-gray-500 hover:text-gray-700">
//               <X className="w-5 h-5" />
//             </button>
//           </div>
//           <div className="space-y-4">
//             <div className="flex gap-2 mb-4">
//               <button
//                 onClick={() => setTempType("percentage")}
//                 className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
//                   tempType === "percentage"
//                     ? "bg-[#FF6500] text-white"
//                     : "bg-white text-[#FF6500] border border-[#FF6500]"
//                 }`}
//               >
//                 Percentage %
//               </button>
//               <button
//                 onClick={() => setTempType("fixed")}
//                 className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
//                   tempType === "fixed" ? "bg-[#FF6500] text-white" : "bg-white text-[#FF6500] border border-[#FF6500]"
//                 }`}
//               >
//                 Fixed Amount
//               </button>
//             </div>
//             <div>
//               <label className="block text-sm font-medium mb-2">
//                 {tempType === "percentage" ? "Percentage" : "Amount (L.E)"}
//               </label>
//               <input
//                 type="number"
//                 value={tempDiscount}
//                 onChange={(e) => setTempDiscount(Number(e.target.value))}
//                 className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
//                 placeholder={tempType === "percentage" ? "10" : "50"}
//               />
//             </div>
//             <div className="flex gap-2">
//               <button
//                 onClick={() => setShowDiscountModal(false)}
//                 className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={() => applyDiscount(tempDiscount, tempType)}
//                 className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
//               >
//                 Apply
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>
//     )
//   }
//   return (
//     <div className="lg:col-span-1">
//       <div className="bg-white rounded-lg shadow-lg p-4 sticky top-8 text-sm">
//         {/* Order Type Buttons */}
//         <div className="flex justify-center mb-4 gap-2">
//           {["Dine In", "Delivery", "Pickup"].map((type) => (
//             <button
//               key={type}
//               onClick={() => setOrderType(type)}
//               className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
//                 orderType === type
//                   ? "bg-[#FF6500] text-white shadow-md"
//                   : "bg-white text-[#FF6500] border border-[#FF6500] hover:bg-orange-50"
//               }`}
//             >
//               {type}
//             </button>
//           ))}
//         </div>
//         {/* Order Header */}
//         <div className="flex items-center justify-between mb-4 pb-2 border-b">
//           <h2 className="text-lg font-bold text-gray-800">Order #113</h2>
//         </div>
//         {/* Staff Info for Dine In */}
//         {orderType === "Dine In" && (
//           <div className="mb-4 p-3 bg-gray-50 rounded-lg">
//             <div className="flex items-center gap-2 mb-2">
//               <User className="w-4 h-4 text-gray-500" />
//               <span className="text-sm font-medium">Cashier: {cashier}</span>
//             </div>
//             <div className="flex items-center gap-2">
//               <User className="w-4 h-4 text-gray-500" />
//               <span className="text-sm font-medium">Waiter: {waiter}</span>
//             </div>
//           </div>
//         )}
//         {/* Customer Data Button - يظهر لجميع أنواع الطلبات */}
//         <button
//           onClick={() => setShowCustomerModal(true)}
//           className="w-full mb-4 p-3 bg-white border border-[#FF6500] text-[#FF6500] rounded-lg hover:bg-orange-50 transition-colors font-medium flex items-center justify-center gap-2"
//         >
//           <User className="w-4 h-4" />
//           {customerData.name ? `Customer: ${customerData.name}` : "Add Customer Data"}
//         </button>
//         {/* Order Items Table */}
//         <div className="mb-4">
//           <div className="grid grid-cols-6 md:grid-cols-7 gap-1 md:gap-2 text-xs font-medium text-gray-600 mb-2 px-2">
//             <div className="col-span-2">ITEM NAME</div>
//             <div className="text-center">QTY</div>
//             <div className="text-center">PRICE</div>
//             <div className="text-center">AMOUNT</div>
//             <div className="text-center">COMMENT</div>
//             <div className="text-center hidden md:block">ACTION</div>
//           </div>
//           {cart.length === 0 ? (
//             <div className="text-center py-6 text-gray-500">
//               <ShoppingCart className="w-8 h-8 mx-auto mb-2 text-gray-300" />
//               <p className="text-xs">No items in cart</p>
//             </div>
//           ) : (
//             // في الجزء الخاص بعرض items في الـ cart
//             <div className="space-y-2">
//               {cart.map((item) => (
//                 <div
//                   key={item.id}
//                   className="grid grid-cols-6 md:grid-cols-7 gap-1 md:gap-2 items-start p-2 md:p-3 bg-gray-50 rounded-lg text-xs border border-gray-200"
//                 >
//                   {/* اسم المنتج والصورة */}
//                   <div className="col-span-2 md:col-span-2 flex items-start gap-1 md:gap-2">
//                     <img
//                       src={item.image || "/placeholder.svg"}
//                       alt={item.name}
//                       className="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover border-2 border-gray-200 flex-shrink-0"
//                     />
//                     <div className="flex-1 min-w-0">
//                       <div className="font-medium text-gray-800 text-xs md:text-sm truncate">{item.name}</div>
//                       {item.size && <div className="text-gray-500 text-xs">{item.size}</div>}
//                       {/* عرض الـ addons */}
//                       {item.addons && item.addons.length > 0 && (
//                         <div className="mt-1 space-y-1">
//                           {item.addons.map((addon, index) => (
//                             <div key={index} className="text-xs text-blue-600 flex justify-between">
//                               <span className="truncate pr-1">
//                                 + {addon.name} x{addon.quantity}
//                               </span>
//                               <span className="flex-shrink-0">{(addon.price * addon.quantity).toFixed(2)} L.E</span>
//                             </div>
//                           ))}
//                         </div>
//                       )}
//                       {item.comment && (
//                         <div className="text-orange-600 text-xs italic mt-1 truncate">"{item.comment}"</div>
//                       )}
//                     </div>
//                   </div>
//                   {/* الكمية */}
//                   <div className="text-center">
//                     <div className="flex items-center justify-center gap-1">
//                       <button
//                         onClick={() => removeFromCart(item.id)}
//                         className="w-6 h-6 md:w-8 md:h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors border border-gray-300"
//                       >
//                         <Minus className="w-3 h-3 md:w-4 md:h-4" />
//                       </button>
//                       <span className="w-6 md:w-8 text-center font-medium text-xs md:text-sm">{item.quantity}</span>
//                       <button
//                         onClick={() => addToCart(item)}
//                         className="w-6 h-6 md:w-8 md:h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#E55A00] transition-colors"
//                       >
//                         <Plus className="w-3 h-3 md:w-4 md:h-4" />
//                       </button>
//                     </div>
//                   </div>
//                   {/* السعر - بس السعر الأساسي بدون إضافات منفصلة */}
//                   <div className="text-center">
//                     <div className="font-medium text-xs md:text-sm">{item.originalPrice.toFixed(2)} L.E</div>
//                   </div>
//                   {/* المبلغ الإجمالي - يشمل السعر الأساسي + الإضافات */}
//                   <div className="text-center font-medium text-gray-800 text-xs md:text-sm">
//                     {(item.price * item.quantity).toFixed(2)} L.E
//                   </div>
//                   {/* زر التعليق */}
//                   <div className="text-center">
//                     <button
//                       onClick={() => openCommentModal(item)}
//                       className={`w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center transition-colors ${
//                         item.comment
//                           ? "bg-orange-500 text-white hover:bg-orange-600"
//                           : "bg-gray-200 text-gray-600 hover:bg-gray-300"
//                       }`}
//                     >
//                       <MessageSquare className="w-3 h-3 md:w-4 md:h-4" />
//                     </button>
//                   </div>
//                   {/* زر الحذف - يختفي في الموبايل ويظهر في التابلت والديسكتوب */}
//                   <div className="text-center hidden md:block">
//                     <button
//                       onClick={() => deleteFromCart(item.id)}
//                       className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
//                     >
//                       <Trash2 className="w-4 h-4" />
//                     </button>
//                   </div>
//                 </div>
//               ))}
//             </div>
//           )}
//         </div>
//         {/* Dine In Specific Fields */}
//         {orderType === "Dine In" && (
//           <div className="mb-4 space-y-3">
//             <button
//               onClick={() => setShowTableModal(true)}
//               className="w-full p-3 bg-white border border-[#FF6500] text-[#FF6500] rounded-lg hover:bg-orange-50 transition-colors font-medium"
//             >
//               {selectedTable ? `Table: ${selectedTable}` : "Choose Table"}
//             </button>
//           </div>
//         )}
//         {/* Add Discount Button */}
//         <button
//           onClick={() => setShowDiscountModal(true)}
//           className="w-full mb-4 py-3 px-4 bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 rounded-lg text-sm hover:from-gray-200 hover:to-gray-300 transition-all flex items-center justify-center gap-2 font-medium"
//         >
//           <Percent className="w-4 h-4" />
//           Add Discount
//         </button>
//         {/* Order Summary */}
//         <div className="space-y-2 mb-4 p-3 bg-gray-50 rounded-lg">
//           <div className="flex justify-between text-sm">
//             <span>Items Count</span>
//             <span className="font-medium">{getTotalItems()}</span>
//           </div>
//           <div className="flex justify-between text-sm">
//             <span>Subtotal</span>
//             <span className="font-medium">{getTotalPrice().toFixed(2)} L.E</span>
//           </div>
//           {discount > 0 && (
//             <div className="flex justify-between text-sm text-green-600">
//               <span>Discount ({discountType === "percentage" ? `${discount}%` : `${discount} L.E`})</span>
//               <span className="font-medium">-{getDiscountAmount().toFixed(2)} L.E</span>
//             </div>
//           )}
//           <div className="flex justify-between text-sm">
//             <span>VAT (14%)</span>
//             <span className="font-medium">{getTax().toFixed(2)} L.E</span>
//           </div>
//           <div className="flex justify-between text-lg font-bold border-t pt-2 text-gray-800">
//             <span>Total</span>
//             <span>{getFinalTotal().toFixed(2)} L.E</span>
//           </div>
//         </div>
//         {/* Action Buttons */}
//         <div className="space-y-2">
//           <div className="grid grid-cols-2 gap-2">
//             <button
//               onClick={handleKOTGeneration}
//               disabled={!isKOTReady()}
//               className={`${
//                 !isKOTReady() ? "bg-gray-400 cursor-not-allowed" : "bg-gray-600 hover:bg-gray-700"
//               } text-white py-2 px-3 rounded-lg text-sm transition-colors font-medium`}
//             >
//               KOT
//             </button>
//             <button className="bg-gray-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-gray-700 transition-colors font-medium">
//               KOT & Print
//             </button>
//           </div>
//           <button className="w-full bg-blue-600 text-white py-3 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors font-medium">
//             Generate Invoice
//           </button>
//           <div className="grid grid-cols-2 gap-2">
//             <button className="bg-green-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-green-700 transition-colors font-medium">
//               Invoice & Pay
//             </button>
//             <button className="bg-blue-400 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-500 transition-colors font-medium">
//               Invoice & Print
//             </button>
//           </div>
//         </div>
//       </div>
//       {/* Modals */}
//       {showTableModal && <TableModal />}
//       {showDiscountModal && <DiscountModal />}
//       {showCommentModal && <CommentModal />}
//       {showCustomerModal && <CustomerModal />}
//     </div>
//   )
// }

// export default OrderSummary
"use client"
import { useState } from "react"
import { ShoppingCart, Minus, Plus, Trash2, Percent, User, Phone, MapPin, X, MessageSquare } from "lucide-react"

const OrderSummary = ({ cart, setCart, addToCart, removeFromCart, deleteFromCart }) => {
  const [orderType, setOrderType] = useState("Dine In")
  const [pax, setPax] = useState(1) // حالة لعدد الأشخاص
  const [deliveryTable, setDeliveryTable] = useState("")
  const [discount, setDiscount] = useState(0)
  const [discountType, setDiscountType] = useState("percentage")
  const [showTableModal, setShowTableModal] = useState(false)
  const [showDiscountModal, setShowDiscountModal] = useState(false)
  const [showCommentModal, setShowCommentModal] = useState(false)
  const [showCustomerModal, setShowCustomerModal] = useState(false)
  const [selectedTable, setSelectedTable] = useState("")
  const [selectedItemForComment, setSelectedItemForComment] = useState(null)
  const [cashier, setCashier] = useState("Ahmed Mohamed")
  const [waiter, setWaiter] = useState("Mohamed Ali")
  const [customerData, setCustomerData] = useState({
    name: "",
    phone: "",
    address: "",
    paymentMethod: "cash",
    paymentPhone: "", // الحقل الجديد لرقم تليفون الدفع
  })
  const tables = [
    { id: 1, name: "Table 1", number: "01", status: "available", icon: "🍽️" },
    { id: 2, name: "Table 2", number: "02", status: "occupied", icon: "🍽️" },
    { id: 3, name: "Table 3", number: "03", status: "reserved", icon: "🍽️" },
    { id: 4, name: "Table 4", number: "04", status: "available", icon: "🍽️" },
    { id: 5, name: "Table 5", number: "05", status: "available", icon: "🍽️" },
    { id: 6, name: "Table 6", number: "06", status: "occupied", icon: "🍽️" },
  ]
  const getStatusColor = (status) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800 border-green-200"
      case "occupied":
        return "bg-red-100 text-red-800 border-red-200"
      case "reserved":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }
  const getStatusText = (status) => {
    switch (status) {
      case "available":
        return "Available"
      case "occupied":
        return "Occupied"
      case "reserved":
        return "Reserved"
      default:
        return "Unavailable"
    }
  }
  const updateItemComment = (itemId, comment) => {
    setCart((prev) => prev.map((item) => (item.id === itemId ? { ...item, comment: comment } : item)))
  }
  const getTotalItems = () => cart.reduce((total, item) => total + item.quantity, 0)
  const getTotalPrice = () => cart.reduce((total, item) => total + item.price * item.quantity, 0)
  const getDiscountAmount = () => {
    const total = getTotalPrice()
    return discountType === "percentage" ? (total * discount) / 100 : discount
  }
  const getTax = () => {
    const afterDiscount = getTotalPrice() - getDiscountAmount()
    return afterDiscount * 0.14
  }
  const getFinalTotal = () => getTotalPrice() - getDiscountAmount() + getTax()
  const applyDiscount = (amount, type) => {
    setDiscount(amount)
    setDiscountType(type)
    setShowDiscountModal(false)
  }
  const openCommentModal = (item) => {
    setSelectedItemForComment(item)
    setShowCommentModal(true)
  }
  const handleKOTGeneration = () => {
    if (cart.length === 0) return
    // التحقق من بيانات العميل
    if (!customerData.name.trim()) {
      alert("يجب إدخال اسم العميل أولاً")
      setShowCustomerModal(true)
      return
    }
    // التحقق من اختيار الطاولة للـ Dine In
    if (orderType === "Dine In" && !selectedTable) {
      alert("يجب اختيار طاولة أولاً")
      setShowTableModal(true)
      return
    }
    // التحقق من العنوان للـ Delivery
    if (orderType === "Delivery" && !customerData.address.trim()) {
      alert("يجب إدخال عنوان التوصيل أولاً")
      setShowCustomerModal(true)
      return
    }
    // التحقق من رقم الهاتف للدفع الإلكتروني
    if (
      (customerData.paymentMethod === "vodafone" || customerData.paymentMethod === "instapay") &&
      !customerData.paymentPhone.trim()
    ) {
      alert("يجب إدخال رقم الهاتف للدفع الإلكتروني")
      setShowCustomerModal(true)
      return
    }
    // إعداد المنتجات للـ KOT مع الاحتفاظ بالكمية الأصلية لكل منتج
    const kotItems = cart.map((cartItem) => ({
      id: cartItem.id, // استخدام معرف المنتج الأصلي
      name: cartItem.name, // هذا يجب أن يكون اسم المنتج فقط (مثل "Turkish Coffee")
      arabicName: cartItem.arabicName,
      quantity: cartItem.quantity, // الاحتفاظ بالكمية الأصلية للمنتج
      size: cartItem.size || "", // إضافة الحجم إذا كان موجودًا
      price: cartItem.price, // السعر مع الإضافات (لكل وحدة)
      originalPrice: cartItem.originalPrice, // السعر الأساسي للوحدة
      image: cartItem.image,
      category: cartItem.category,
      notes: cartItem.comment || "", // استخدام comment كـ notes
      addons: cartItem.addons || [], // التأكد من تمرير الإضافات
      status: "pending", // الحالة الأولية لعنصر KOT
      cookingStartTime: null,
      cookingEndTime: null,
      servedTime: null, // إضافة حقل جديد لوقت التقديم
    }))

    const kotData = {
      id: Math.floor(Math.random() * 1000) + 200,
      orderId: 113, // يمكن توليد هذا بشكل ديناميكي
      date: new Date().toLocaleDateString("en-US", {
        month: "long",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }),
      customerName: customerData.name,
      tableNumber: selectedTable || `${orderType} Order`, // تغيير table إلى tableNumber
      orderType: orderType,
      priority: "normal", // يمكن جعل هذا ديناميكيًا
      status: "pending",
      items: kotItems,
      cashier: cashier, // إضافة الكاشير
      waiter: waiter, // إضافة النادل
      pax: orderType === "Dine In" ? pax : null, // إضافة عدد الأشخاص فقط للـ Dine In
      cookingStartTime: null, // وقت بدء الطبخ للـ KOT بالكامل
      cookingEndTime: null, // وقت انتهاء الطبخ للـ KOT بالكامل
      servedTime: null, // وقت التقديم للـ KOT بالكامل
    }
    // حفظ البيانات في localStorage
    const existingKOTs = JSON.parse(localStorage.getItem("kotItems") || "[]")
    const updatedKOTs = [...existingKOTs, kotData]
    localStorage.setItem("kotItems", JSON.stringify(updatedKOTs))
    // مسح الـ cart
    setCart([])
    // إعادة تعيين البيانات
    setSelectedTable("")
    setCustomerData({
      name: "",
      phone: "",
      address: "",
      paymentMethod: "cash",
      paymentPhone: "",
    })
    // إظهار رسالة نجاح
    alert("KOT generated successfully!")
  }
  const isKOTReady = () => {
    if (cart.length === 0) return false
    if (!customerData.name.trim()) return false
    if (orderType === "Dine In" && !selectedTable) return false
    if (orderType === "Delivery" && !customerData.address.trim()) return false
    if (
      (customerData.paymentMethod === "vodafone" || customerData.paymentMethod === "instapay") &&
      !customerData.paymentPhone.trim()
    )
      return false
    return true
  }
  const CustomerModal = () => {
    const [tempCustomerData, setTempCustomerData] = useState({
      ...customerData,
    })
    const handleSaveCustomer = () => {
      setCustomerData(tempCustomerData)
      setShowCustomerModal(false)
    }
    return (
      <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Customer Data</h3>
            <button onClick={() => setShowCustomerModal(false)} className="text-gray-500 hover:text-gray-700">
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="space-y-4">
            <div className="relative">
              <User className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Customer Name"
                value={tempCustomerData.name}
                onChange={(e) =>
                  setTempCustomerData({
                    ...tempCustomerData,
                    name: e.target.value,
                  })
                }
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
              />
            </div>
            <div className="relative">
              <Phone className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
              <input
                type="tel"
                placeholder="Phone Number"
                value={tempCustomerData.phone}
                onChange={(e) =>
                  setTempCustomerData({
                    ...tempCustomerData,
                    phone: e.target.value,
                  })
                }
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
              />
            </div>
            {orderType === "Delivery" && (
              <div className="relative">
                <MapPin className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Address"
                  value={tempCustomerData.address}
                  onChange={(e) =>
                    setTempCustomerData({
                      ...tempCustomerData,
                      address: e.target.value,
                    })
                  }
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
                />
              </div>
            )}
            {/* حقل رقم تليفون الدفع - يظهر فقط مع فودافون كاش أو انستاي باي */}
            {(tempCustomerData.paymentMethod === "vodafone" || tempCustomerData.paymentMethod === "instapay") && (
              <div className="relative">
                <Phone className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
                <input
                  type="tel"
                  placeholder={`${
                    tempCustomerData.paymentMethod === "vodafone" ? "Vodafone Cash" : "InstaPay"
                  } Phone Number`}
                  value={tempCustomerData.paymentPhone}
                  onChange={(e) =>
                    setTempCustomerData({
                      ...tempCustomerData,
                      paymentPhone: e.target.value,
                    })
                  }
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
                />
              </div>
            )}
            <div className="flex gap-2">
              <button
                onClick={() => setShowCustomerModal(false)}
                className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveCustomer}
                className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }
  const CommentModal = () => {
    const [tempComment, setTempComment] = useState(selectedItemForComment?.comment || "")
    const handleSaveComment = () => {
      if (selectedItemForComment) {
        updateItemComment(selectedItemForComment.id, tempComment)
        setShowCommentModal(false)
        setSelectedItemForComment(null)
      }
    }
    return (
      <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Add Comment</h3>
            <button
              onClick={() => {
                setShowCommentModal(false)
                setSelectedItemForComment(null)
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Comment for {selectedItemForComment?.name}</label>
              <textarea
                value={tempComment}
                onChange={(e) => setTempComment(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none resize-none"
                rows="4"
                placeholder="Enter your comment here..."
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setShowCommentModal(false)
                  setSelectedItemForComment(null)
                }}
                className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveComment}
                className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }
  const TableModal = () => (
    <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">Choose Table</h3>
          <button onClick={() => setShowTableModal(false)} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>
        <div className="grid grid-cols-2 gap-3">
          {tables.map((table) => (
            <button
              key={table.id}
              onClick={() => {
                if (table.status === "available") {
                  setSelectedTable(table.name)
                  setShowTableModal(false)
                }
              }}
              disabled={table.status !== "available"}
              className={`p-4 rounded border-2 text-sm font-medium transition-all ${
                table.status === "available"
                  ? "hover:bg-orange-50 hover:border-orange-300 cursor-pointer"
                  : "cursor-not-allowed opacity-60"
              } ${getStatusColor(table.status)}`}
            >
              <div className="flex flex-col items-center space-y-2">
                <div className="text-2xl">{table.icon}</div>
                <div className="font-semibold">{table.name}</div>
                <div className="text-xl font-bold">{table.number}</div>
                <div className="text-xs">{getStatusText(table.status)}</div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
  const DiscountModal = () => {
    const [tempDiscount, setTempDiscount] = useState(0)
    const [tempType, setTempType] = useState("percentage")
    return (
      <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full m-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold">Add Discount</h3>
            <button onClick={() => setShowDiscountModal(false)} className="text-gray-500 hover:text-gray-700">
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="space-y-4">
            <div className="flex gap-2 mb-4">
              <button
                onClick={() => setTempType("percentage")}
                className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
                  tempType === "percentage"
                    ? "bg-[#FF6500] text-white"
                    : "bg-white text-[#FF6500] border border-[#FF6500]"
                }`}
              >
                Percentage %
              </button>
              <button
                onClick={() => setTempType("fixed")}
                className={`flex-1 py-2 px-4 rounded text-sm font-medium transition-colors ${
                  tempType === "fixed" ? "bg-[#FF6500] text-white" : "bg-white text-[#FF6500] border border-[#FF6500]"
                }`}
              >
                Fixed Amount
              </button>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                {tempType === "percentage" ? "Percentage" : "Amount (L.E)"}
              </label>
              <input
                type="number"
                value={tempDiscount}
                onChange={(e) => setTempDiscount(Number(e.target.value))}
                className="w-full p-3 border border-gray-300 rounded focus:ring-2 focus:ring-[#FF6500] focus:border-transparent outline-none"
                placeholder={tempType === "percentage" ? "10" : "50"}
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setShowDiscountModal(false)}
                className="flex-1 py-2 px-4 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => applyDiscount(tempDiscount, tempType)}
                className="flex-1 py-2 px-4 bg-[#FF6500] text-white rounded hover:bg-[#E55A00] transition-colors"
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }
  return (
    <div className="lg:col-span-1">
      <div className="bg-white rounded-lg shadow-lg p-4 sticky top-8 text-sm">
        {/* Order Type Buttons */}
        <div className="flex justify-center mb-4 gap-2">
          {["Dine In", "Delivery", "Pickup"].map((type) => (
            <button
              key={type}
              onClick={() => setOrderType(type)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                orderType === type
                  ? "bg-[#FF6500] text-white shadow-md"
                  : "bg-white text-[#FF6500] border border-[#FF6500] hover:bg-orange-50"
              }`}
            >
              {type}
            </button>
          ))}
        </div>
        {/* Order Header */}
        <div className="flex items-center justify-between mb-4 pb-2 border-b">
          <h2 className="text-lg font-bold text-gray-800">Order #113</h2>
        </div>
        {/* Staff Info for Dine In */}
        {orderType === "Dine In" && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium">Cashier: {cashier}</span>
            </div>
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium">Waiter: {waiter}</span>
            </div>
            <div className="flex items-center gap-2 mt-2">
              <span className="text-sm font-medium text-gray-500">Pax:</span>
              <input
                type="number"
                value={pax}
                onChange={(e) => setPax(Number(e.target.value))}
                min="1"
                className="w-16 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-[#FF6500] outline-none"
              />
            </div>
          </div>
        )}
        {/* Customer Data Button - يظهر لجميع أنواع الطلبات */}
        <button
          onClick={() => setShowCustomerModal(true)}
          className="w-full mb-4 p-3 bg-white border border-[#FF6500] text-[#FF6500] rounded-lg hover:bg-orange-50 transition-colors font-medium flex items-center justify-center gap-2"
        >
          <User className="w-4 h-4" />
          {customerData.name ? `Customer: ${customerData.name}` : "Add Customer Data"}
        </button>
        {/* Order Items Table */}
        <div className="mb-4">
          <div className="grid grid-cols-6 md:grid-cols-7 gap-1 md:gap-2 text-xs font-medium text-gray-600 mb-2 px-2">
            <div className="col-span-2">ITEM NAME</div>
            <div className="text-center">QTY</div>
            <div className="text-center">PRICE</div>
            <div className="text-center">AMOUNT</div>
            <div className="text-center">COMMENT</div>
            <div className="text-center hidden md:block">ACTION</div>
          </div>
          {cart.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              <ShoppingCart className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p className="text-xs">No items in cart</p>
            </div>
          ) : (
            // في الجزء الخاص بعرض items في الـ cart
            <div className="space-y-2">
              {cart.map((item) => (
                <div
                  key={item.id}
                  className="grid grid-cols-6 md:grid-cols-7 gap-1 md:gap-2 items-start p-2 md:p-3 bg-gray-50 rounded-lg text-xs border border-gray-200"
                >
                  {/* اسم المنتج والصورة */}
                  <div className="col-span-2 md:col-span-2 flex items-start gap-1 md:gap-2">
                    <img
                      src={item.image || "/placeholder.svg"}
                      alt={item.name}
                      className="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover border-2 border-gray-200 flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-800 text-xs md:text-sm truncate">{item.name}</div>
                      {item.size && <div className="text-gray-500 text-xs">{item.size}</div>}
                      {/* عرض الـ addons */}
                      {item.addons && item.addons.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {item.addons.map((addon, index) => (
                            <div key={index} className="text-xs text-blue-600 flex justify-between">
                              <span className="truncate pr-1">
                                + {addon.name} x{addon.quantity}
                              </span>
                              <span className="flex-shrink-0">{(addon.price * addon.quantity).toFixed(2)} L.E</span>
                            </div>
                          ))}
                        </div>
                      )}
                      {item.comment && (
                        <div className="text-orange-600 text-xs italic mt-1 truncate">"{item.comment}"</div>
                      )}
                    </div>
                  </div>
                  {/* الكمية */}
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1">
                      <button
                        onClick={() => removeFromCart(item.id)}
                        className="w-6 h-6 md:w-8 md:h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors border border-gray-300"
                      >
                        <Minus className="w-3 h-3 md:w-4 md:h-4" />
                      </button>
                      <span className="w-6 md:w-8 text-center font-medium text-xs md:text-sm">{item.quantity}</span>
                      <button
                        onClick={() => addToCart(item)}
                        className="w-6 h-6 md:w-8 md:h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#E55A00] transition-colors"
                      >
                        <Plus className="w-3 h-3 md:w-4 md:h-4" />
                      </button>
                    </div>
                  </div>
                  {/* السعر - بس السعر الأساسي بدون إضافات منفصلة */}
                  <div className="text-center">
                    <div className="font-medium text-xs md:text-sm">{item.originalPrice.toFixed(2)} L.E</div>
                  </div>
                  {/* المبلغ الإجمالي - يشمل السعر الأساسي + الإضافات */}
                  <div className="text-center font-medium text-gray-800 text-xs md:text-sm">
                    {(item.price * item.quantity).toFixed(2)} L.E
                  </div>
                  {/* زر التعليق */}
                  <div className="text-center">
                    <button
                      onClick={() => openCommentModal(item)}
                      className={`w-6 h-6 md:w-8 md:h-8 rounded-full flex items-center justify-center transition-colors ${
                        item.comment
                          ? "bg-orange-500 text-white hover:bg-orange-600"
                          : "bg-gray-200 text-gray-600 hover:bg-gray-300"
                      }`}
                    >
                      <MessageSquare className="w-3 h-3 md:w-4 md:h-4" />
                    </button>
                  </div>
                  {/* زر الحذف - يختفي في الموبايل ويظهر في التابلت والديسكتوب */}
                  <div className="text-center hidden md:block">
                    <button
                      onClick={() => deleteFromCart(item.id)}
                      className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Dine In Specific Fields */}
        {orderType === "Dine In" && (
          <div className="mb-4 space-y-3">
            <button
              onClick={() => setShowTableModal(true)}
              className="w-full p-3 bg-white border border-[#FF6500] text-[#FF6500] rounded-lg hover:bg-orange-50 transition-colors font-medium"
            >
              {selectedTable ? `Table: ${selectedTable}` : "Choose Table"}
            </button>
          </div>
        )}
        {/* Add Discount Button */}
        <button
          onClick={() => setShowDiscountModal(true)}
          className="w-full mb-4 py-3 px-4 bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 rounded-lg text-sm hover:from-gray-200 hover:to-gray-300 transition-all flex items-center justify-center gap-2 font-medium"
        >
          <Percent className="w-4 h-4" />
          Add Discount
        </button>
        {/* Order Summary */}
        <div className="space-y-2 mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex justify-between text-sm">
            <span>Items Count</span>
            <span className="font-medium">{getTotalItems()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Subtotal</span>
            <span className="font-medium">{getTotalPrice().toFixed(2)} L.E</span>
          </div>
          {discount > 0 && (
            <div className="flex justify-between text-sm text-green-600">
              <span>Discount ({discountType === "percentage" ? `${discount}%` : `${discount} L.E`})</span>
              <span className="font-medium">-{getDiscountAmount().toFixed(2)} L.E</span>
            </div>
          )}
          <div className="flex justify-between text-sm">
            <span>VAT (14%)</span>
            <span className="font-medium">{getTax().toFixed(2)} L.E</span>
          </div>
          <div className="flex justify-between text-lg font-bold border-t pt-2 text-gray-800">
            <span>Total</span>
            <span>{getFinalTotal().toFixed(2)} L.E</span>
          </div>
        </div>
        {/* Action Buttons */}
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={handleKOTGeneration}
              disabled={!isKOTReady()}
              className={`${
                !isKOTReady() ? "bg-gray-400 cursor-not-allowed" : "bg-gray-600 hover:bg-gray-700"
              } text-white py-2 px-3 rounded-lg text-sm transition-colors font-medium`}
            >
              KOT
            </button>
            <button className="bg-gray-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-gray-700 transition-colors font-medium">
              KOT & Print
            </button>
          </div>
          <button className="w-full bg-blue-600 text-white py-3 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors font-medium">
            Generate Invoice
          </button>
          <div className="grid grid-cols-2 gap-2">
            <button className="bg-green-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-green-700 transition-colors font-medium">
              Invoice & Pay
            </button>
            <button className="bg-blue-400 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-500 transition-colors font-medium">
              Invoice & Print
            </button>
          </div>
        </div>
      </div>
      {/* Modals */}
      {showTableModal && <TableModal />}
      {showDiscountModal && <DiscountModal />}
      {showCommentModal && <CommentModal />}
      {showCustomerModal && <CustomerModal />}
    </div>
  )
}

export default OrderSummary
