// import React from 'react'
// import StatCard from './StatCard'
// import { CreditCard, Package, ShoppingCart, Users } from 'lucide-react'

// const Stats = () => {
//   return (
//     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
//         <StatCard
//           title="اوامر اليوم"
//           value="L.E 1,234,567"
//           change="+12.5%"
//           changeType="up"
//           icon={CreditCard}
//           color="bg-gradient-to-br from-blue-500 to-blue-600"
//           bgColor="bg-gradient-to-br from-blue-50 to-indigo-50"
//         />
//         <StatCard
//           title=" أرباح اليوم"
//           value="2,847"
//           change="+8.2%"
//           changeType="up"
//           icon={ShoppingCart}
//           color="bg-gradient-to-br from-green-500 to-green-600"
//           bgColor="bg-gradient-to-br from-green-50 to-emerald-50"
//         />
//         <StatCard
//           title="العملاء الجدد"
//           value="1,429"
//           change="+18.7%"
//           changeType="up"
//           icon={Users}
//           color="bg-gradient-to-br from-purple-500 to-purple-600"
//           bgColor="bg-gradient-to-br from-purple-50 to-violet-50"
//         />
//         <StatCard
//           title="متوسط الارباح اليوميه"
//           value="8,941"
//           change="-2.4%"
//           changeType="down"
//           icon={Package}
//           color="bg-gradient-to-br from-orange-500 to-orange-600"
//           bgColor="bg-gradient-to-br from-orange-50 to-amber-50"
//         />
//       </div>
//   )
// }

// export default Stats
import React from 'react'
import StatCard from './StatCard'
import { CreditCard, Package, ShoppingCart, Users } from 'lucide-react'

const Stats = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Today's Orders"
          value="L.E 1,234,567"
          change="+12.5%"
          changeType="up"
          icon={CreditCard}
          color="bg-gradient-to-br from-blue-500 to-blue-600"
          bgColor="bg-gradient-to-br from-blue-50 to-indigo-50"
        />
        <StatCard
          title="Today's Profits"
          value="2,847"
          change="+8.2%"
          changeType="up"
          icon={ShoppingCart}
          color="bg-gradient-to-br from-green-500 to-green-600"
          bgColor="bg-gradient-to-br from-green-50 to-emerald-50"
        />
        <StatCard
          title="New Customers"
          value="1,429"
          change="+18.7%"
          changeType="up"
          icon={Users}
          color="bg-gradient-to-br from-purple-500 to-purple-600"
          bgColor="bg-gradient-to-br from-purple-50 to-violet-50"
        />
        <StatCard
          title="Daily Average Profits"
          value="8,941"
          change="-2.4%"
          changeType="down"
          icon={Package}
          color="bg-gradient-to-br from-orange-500 to-orange-600"
          bgColor="bg-gradient-to-br from-orange-50 to-amber-50"
        />
      </div>
  )
}

export default Stats