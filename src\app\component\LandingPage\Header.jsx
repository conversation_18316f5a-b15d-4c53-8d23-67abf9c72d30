"use client"
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import { LanguageSwitcher } from "../LanguageSwitcher";
import { useI18n } from "@/context/translate-api";
import Cookies from "js-cookie";


const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
   const {t } = useI18n()
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  // const {t}=useI18n()
  console.log(t)
  const lang = Cookies.get("lang")
  console.log(lang)

  return (
    <header className="bg-white border border-red-500 ">
      <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo Section */}
          <div className="flex items-center ">
            <div className="  p-2 rounded-xl w-20 h-12 flex items-center ">
               <Link href={"/dashboard"} className="text-2xl font-bold text-[#FF6500]">
                EPISYS
                {/* {t.navigation.home} */}
              </Link>
              {/* <LanguageSwitcher/> */}
              <Image
                src="/images/EPISYS.png"
                alt="EPISYS"
                width={120}
                height={120}
                className="filter "
              />
            </div>
            {/* <div className="hidden sm:block">
              <h1 className="text-2xl font-bold bg-[#FF6500]  bg-clip-text text-transparent">
                EPISYS
                
              </h1>
            </div> */}
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <ul className="flex items-center space-x-6 text-gray-700">
              <li>
                <a
                  href="#"
                  className="hover:text-[#FF6500] transition-colors duration-300 font-medium text-sm xl:text-base border-b-2 border-transparent hover:border-[#FF6500] pb-1"
                >
                  حلول إيبيسيس
                 {/* <LanguageSwitcher/> */}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-[#FF6500] transition-colors duration-300 font-medium text-sm xl:text-base border-b-2 border-transparent hover:border-[#FF6500] pb-1"
                >
                  إيبيسيس انتربرايز
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-[#FF6500] transition-colors duration-300 font-medium text-sm xl:text-base border-b-2 border-transparent hover:border-[#FF6500] pb-1"
                >
                  متجر التطبيقات
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-[#FF6500] transition-colors duration-300 font-medium text-sm xl:text-base border-b-2 border-transparent hover:border-[#FF6500] pb-1"
                >
                  أنواع المطاعم
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-[#FF6500] transition-colors duration-300 font-medium text-sm xl:text-base border-b-2 border-transparent hover:border-[#FF6500] pb-1"
                >
                  كن شريكاً لنا
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-[#FF6500] transition-colors duration-300 font-medium text-sm xl:text-base border-b-2 border-transparent hover:border-[#FF6500] pb-1"
                >
                  مصادر
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-[#FF6500] transition-colors duration-300 font-medium text-sm xl:text-base border-b-2 border-transparent hover:border-[#FF6500] pb-1"
                >
                  الأسعار
                </a>
              </li>
            </ul>
          </nav>

          {/* CTA Buttons - Desktop */}
          <div className="hidden lg:flex items-center space-x-4">
            <button className="bg-[#FF6500]  text-white px-6 py-2.5 rounded-lg font-semibold text-sm hover:from-[#e55a00] hover:to-[#e5a082] transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
              اطلب عرض النظام
            </button>
            <Link
              href="/login"
              className="text-[#FF6500] border-2 border-[#FF6500] px-6 py-2.5 rounded-lg font-semibold text-sm hover:bg-[#FF6500] hover:text-white transition-all duration-300 shadow-sm hover:shadow-md"
            >
              تسجيل الدخول
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              onClick={toggleMenu}
              className="text-gray-700 hover:text-[#FF6500] transition-colors duration-300 p-2"
              aria-label="Toggle menu"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <div
          className={`lg:hidden transition-all duration-300 ease-in-out ${
            isMenuOpen
              ? "max-h-screen opacity-100 pb-6"
              : "max-h-0 opacity-0 overflow-hidden"
          }`}
        >
          <div className="bg-gradient-to-br from-[#FFD5C7] to-white rounded-xl mt-4 p-6 shadow-lg">
            <nav className="space-y-4">
              <a
                href="#"
                className="block text-gray-700 hover:text-[#FF6500] transition-colors duration-300 font-medium py-2 border-b border-[#FFD5C7] last:border-b-0"
              >
                حلول إيبيسيس
              </a>
              <a
                href="#"
                className="block text-gray-700 hover:text-[#FF6500] transition-colors duration-300 font-medium py-2 border-b border-[#FFD5C7] last:border-b-0"
              >
                إيبيسيس انتربرايز
              </a>
              <a
                href="#"
                className="block text-gray-700 hover:text-[#FF6500] transition-colors duration-300 font-medium py-2 border-b border-[#FFD5C7] last:border-b-0"
              >
                متجر التطبيقات
              </a>
              <a
                href="#"
                className="block text-gray-700 hover:text-[#FF6500] transition-colors duration-300 font-medium py-2 border-b border-[#FFD5C7] last:border-b-0"
              >
                أنواع المطاعم
              </a>
              <a
                href="#"
                className="block text-gray-700 hover:text-[#FF6500] transition-colors duration-300 font-medium py-2 border-b border-[#FFD5C7] last:border-b-0"
              >
                كن شريكاً لنا
              </a>
              <a
                href="#"
                className="block text-gray-700 hover:text-[#FF6500] transition-colors duration-300 font-medium py-2 border-b border-[#FFD5C7] last:border-b-0"
              >
                مصادر
              </a>
              <a
                href="#"
                className="block text-gray-700 hover:text-[#FF6500] transition-colors duration-300 font-medium py-2 border-b border-[#FFD5C7] last:border-b-0"
              >
                الأسعار
              </a>
            </nav>

            {/* Mobile CTA Buttons */}
            <div className="mt-6 space-y-3">
              <button className="w-full bg-gradient-to-r from-[#FF6500] to-[#FCB190] text-white px-6 py-3 rounded-lg font-semibold hover:from-[#e55a00] hover:to-[#e5a082] transition-all duration-300 shadow-md">
                اطلب عرض النظام
              </button>
              <Link
                href="/login"
                className="block w-full text-center text-[#FF6500] border-2 border-[#FF6500] px-6 py-3 rounded-lg font-semibold hover:bg-[#FF6500] hover:text-white transition-all duration-300"
              >
                تسجيل الدخول
              </Link>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;