"use server"

import axios from "axios"

export async function fetchCategories ()  {
     try {
         const res = await axios.get("https://fakestoreapi.com/products/categories")
         console.log(res.data)
         return res.data
     } catch (error) {
        
     }
}
export async function fetchProducts ()  {
     try {
         const res = await axios.get("https://fakestoreapi.com/products")
        
         return res.data
     } catch (error) {
        
     }
}

// export async function fetchProductsFilter (searchParams)  {
//      try {
//          const params = {
//       ...JSON.parse(searchParams),
//     };
//     console.log("searchParams",params)
//          const res = await axios.get("https://fakestoreapi.com/products" ,  { params })
         
//          return res.data
//      } catch (error) {
        
//      }
// }
export async function fetchProductsFilter (searchParams)  {
     try {
      
   
         const res = await axios.get(`https://fakestoreapi.com/products/category/${searchParams}`)
         
         return res.data
     } catch (error) {
        
     }
}