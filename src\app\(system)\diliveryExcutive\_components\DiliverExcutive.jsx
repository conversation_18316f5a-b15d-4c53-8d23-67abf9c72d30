"use client"
import React, { useState } from 'react';
import { Search, Download, Plus, Edit, Trash2, Phone, User, Truck, CheckCircle, Clock, XCircle } from 'lucide-react';

const DiliverExcutive = () => {
  const [searchTerm, setSearchTerm] = useState('');
  
  const executives = [
    {
      id: 1,
      name: 'ALI ELDAWLY',
      phone: '1234566789',
      totalOrders: 25,
      status: 'AVAILABLE'
    },
    {
      id: 2,
      name: 'AHMED HASSAN',
      phone: '9876543210',
      totalOrders: 18,
      status: 'BUSY'
    },
    {
      id: 3,
      name: 'OMAR MAHMOUD',
      phone: '5551234567',
      totalOrders: 32,
      status: 'AVAILABLE'
    },
    {
      id: 4,
      name: 'MOHAMED ALI',
      phone: '1112223333',
      totalOrders: 15,
      status: 'OFFLINE'
    },
    {
      id: 5,
      name: 'SARA AHMED',
      phone: '4445556666',
      totalOrders: 28,
      status: 'AVAILABLE'
    }
  ];

  const filteredExecutives = executives.filter(executive =>
    executive.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    executive.phone.includes(searchTerm)
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'BUSY':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'OFFLINE':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'AVAILABLE':
        return <CheckCircle className="w-4 h-4" />;
      case 'BUSY':
        return <Clock className="w-4 h-4" />;
      case 'OFFLINE':
        return <XCircle className="w-4 h-4" />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">Delivery Executive</h1>
          
          {/* Search and Actions Bar */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* Search Bar */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search by name, email or phone number"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] outline-none text-sm"
              />
            </div>
            
            {/* Action Buttons */}
            <div className="flex gap-3 w-full sm:w-auto">
              <button className="flex items-center gap-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium">
                <Download className="w-4 h-4" />
                <span className="hidden sm:inline">Export</span>
              </button>
              <button className="flex items-center gap-2 px-6 py-3 bg-[#FF6500] hover:bg-[#e55a00] text-white rounded-lg transition-colors text-sm font-medium">
                <Plus className="w-4 h-4" />
                Add Executive
              </button>
            </div>
          </div>
        </div>

        {/* Desktop Table View */}
        <div className="hidden lg:block bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 bg-gray-50">
                  <th className="text-left py-4 px-6 font-medium text-gray-700 text-sm uppercase tracking-wider">
                    Member Name
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700 text-sm uppercase tracking-wider">
                    Phone
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700 text-sm uppercase tracking-wider">
                    Total Orders
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700 text-sm uppercase tracking-wider">
                    Status
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700 text-sm uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredExecutives.map((executive) => (
                  <tr key={executive.id} className="hover:bg-gray-50 transition-colors">
                    <td className="py-4 px-6 text-gray-900 font-medium">{executive.name}</td>
                    <td className="py-4 px-6 text-gray-600">{executive.phone}</td>
                    <td className="py-4 px-6">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {executive.totalOrders} ORDERS
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(executive.status)}`}>
                        {getStatusIcon(executive.status)}
                        {executive.status}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex gap-2">
                        <button className="inline-flex items-center gap-1 px-3 py-1.5 text-sm font-medium text-gray-700 hover:text-[#FF6500] transition-colors">
                          <Edit className="w-4 h-4" />
                          Update
                        </button>
                        <button className="inline-flex items-center gap-1 px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-700 transition-colors">
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile/Tablet Card View */}
        <div className="lg:hidden space-y-4">
          {filteredExecutives.map((executive) => (
            <div key={executive.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-[#FF6500] rounded-full flex items-center justify-center">
                    <Truck className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{executive.name}</h3>
                    <div className="flex items-center gap-1 mt-1">
                      <span className="text-sm text-gray-600">{executive.totalOrders} orders</span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <button className="p-2 text-gray-400 hover:text-[#FF6500] transition-colors">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Phone className="w-4 h-4" />
                  <span>{executive.phone}</span>
                </div>
              </div>
              
              <div className="mt-4 pt-3 border-t border-gray-100 flex items-center justify-between">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {executive.totalOrders} ORDERS
                </span>
                <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(executive.status)}`}>
                  {getStatusIcon(executive.status)}
                  {executive.status}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredExecutives.length === 0 && (
          <div className="text-center py-12">
            <Truck className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No delivery executives found</h3>
            <p className="text-gray-600">Try adjusting your search terms or add a new executive.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DiliverExcutive;