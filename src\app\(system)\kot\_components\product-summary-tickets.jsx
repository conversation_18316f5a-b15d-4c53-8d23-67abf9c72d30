"use client"
import { useState, useEffect } from "react"
import { ChefHat } from "lucide-react"

const ProductSummaryTickets = () => {
  const [aggregatedProducts, setAggregatedProducts] = useState([])
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const aggregateKOTItems = () => {
      try {
        const storedKOTs = localStorage.getItem("kotItems")
        const kots = storedKOTs ? JSON.parse(storedKOTs) : []

        const productMap = new Map()

        kots.forEach((kot) => {
          kot.items.forEach((item) => {
            const productName = item.name // استخدام اسم المنتج كمعرف للتجميع
            if (productMap.has(productName)) {
              const existingProduct = productMap.get(productName)
              productMap.set(productName, {
                ...existingProduct,
                quantity: existingProduct.quantity + item.quantity,
              })
            } else {
              productMap.set(productName, {
                name: item.name,
                arabicName: item.arabicName,
                quantity: item.quantity,
                image: item.image || "/placeholder.svg?height=60&width=60",
                category: item.category,
              })
            }
          })
        })
        setAggregatedProducts(Array.from(productMap.values()))
      } catch (error) {
        console.error("Failed to aggregate KOT items from localStorage:", error)
        setAggregatedProducts([])
      }
    }

    aggregateKOTItems()
    window.addEventListener("storage", aggregateKOTItems)
    return () => {
      window.removeEventListener("storage", aggregateKOTItems)
    }
  }, [])

  if (!isClient) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500]"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 bg-gray-50">
      <h2 className="text-3xl font-bold text-gray-900 mb-6">Product Summary Tickets</h2>
      {aggregatedProducts.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <ChefHat className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No products to display</h3>
          <p className="text-gray-500">Add items to cart and generate KOTs to see them here.</p>
        </div>
      ) : (
        <div className="flex flex-wrap justify-center gap-8 p-4">
          {aggregatedProducts.map((product, index) => (
            <div
              key={product.name}
              className="relative bg-white p-4 rounded-lg shadow-md border border-gray-200 flex flex-col items-center text-center"
              style={{
                width: "180px", // حجم ثابت للتذكرة
                transform: `rotate(${index % 2 === 0 ? -3 : 3}deg)`, // ميلان بسيط
                marginTop: "50px", // مسافة من الأعلى للحبل
              }}
            >
              {/* الحبل العلوي */}
              <div className="absolute -top-12 left-1/2 -translate-x-1/2 w-0.5 h-12 bg-gray-400"></div>
              {/* الدبوس */}
              <div className="absolute -top-14 left-1/2 -translate-x-1/2 w-3 h-3 bg-gray-600 rounded-full"></div>

              <img
                src={product.image || "/placeholder.svg"}
                alt={product.name}
                className="w-24 h-24 object-cover rounded-full mb-3 border-2 border-gray-100"
              />
              <h4 className="font-semibold text-lg text-gray-900 mb-1">{product.name}</h4>
              {product.arabicName && <p className="text-sm text-gray-600 mb-2">{product.arabicName}</p>}
              <span className="bg-[#FF6500] text-white text-xl font-bold px-4 py-2 rounded-full">
                x{product.quantity}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default ProductSummaryTickets
