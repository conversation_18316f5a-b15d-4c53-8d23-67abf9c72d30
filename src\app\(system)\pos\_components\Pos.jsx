

// export default Pos;
"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from 'next/navigation';
import {
  Search,
  RotateCcw,
  ShoppingCart,
  Plus,
  Minus,
  User,
  MapPin,
  Truck,
  Coffee,
  Cookie,
  Droplets,
  CreditCard,
  Printer,
  Users,
  Percent,
  Trash2,
  Grid3X3,
  <PERSON>ake,
  X,
  MessageSquare,
  Edit3,
  Phone,
} from "lucide-react";
import OrderSummary from "./OrderSummary";
import { fetchMenuItems, fetchCategories, fetchMenus, fetchMenuItemVariants, fetchMenuItemAddons } from '../../../../../lib/api';

const Pos = ({ token = null }) => {
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState("Show All");
  const [orderType, setOrderType] = useState("Pickup");
  const [cart, setCart] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [pax, setPax] = useState(1);
  const [discount, setDiscount] = useState(0);
  const [deliveryTable, setDeliveryTable] = useState("");
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [selectedAddons, setSelectedAddons] = useState({});
  const [variantsLoading, setVariantsLoading] = useState(false);
  const [addonsLoading, setAddonsLoading] = useState(false);
  
  // API Data States
  const [categories, setCategories] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [menus, setMenus] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMenu, setSelectedMenu] = useState(null);

  // Helper function to get icons for categories
  function getIconForCategory(categoryName) {
    const name = categoryName.toLowerCase();
    if (name.includes('drink') || name.includes('beverage')) return Coffee;
    if (name.includes('bakery') || name.includes('bread') || name.includes('cake')) return Cake;
    if (name.includes('soft') || name.includes('juice') || name.includes('water')) return Droplets;
    return Cookie; // default icon
  }

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [categoriesData, menuItemsData, menusData] = await Promise.all([
          fetchCategories(token),
          fetchMenuItems(token),
          fetchMenus(token)
        ]);

        setCategories(categoriesData);
        setMenuItems(menuItemsData);
        setMenus(menusData);

        // Set default menu (first active menu or first menu)
        const defaultMenu = menusData.find(menu => menu.is_default) || menusData[0];
        setSelectedMenu(defaultMenu);

      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [token, router]);

  // Transform API categories to match your existing structure and remove duplicates
  const uniqueCategories = categories
    .filter(cat => cat.is_active)
    .reduce((unique, cat) => {
      // Check if category name already exists
      const existing = unique.find(item => item.name === cat.name);
      if (!existing) {
        unique.push({
          name: cat.name,
          icon: getIconForCategory(cat.name),
          id: cat.id,
          code: cat.code
        });
      }
      return unique;
    }, []);

  const transformedCategories = [
    { name: "Show All", icon: Grid3X3, id: "show-all" },
    ...uniqueCategories
  ];

  // Transform API menu items to match your existing structure
  const transformedMenuItems = menuItems
    .filter(item => item.is_active)
    .map(item => ({
      id: item.id,
      name: item.name,
      price: parseFloat(item.base_price),
      category: item.category?.name || 'Other',
      categoryId: item.category_id,
      image: item.image_urls && item.image_urls.length > 0 
        ? item.image_urls[0] 
        : `https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop`,
      description: item.description,
      shortDescription: item.short_description,
      // Check if item has variants by looking at the variants array length
      hasVariations: item.variants && item.variants.length > 0,
      allergens: item.allergens || [],
      dietaryInfo: item.dietary_info || [],
      isSpicy: item.is_spicy,
      prepTime: item.prep_time_minutes,
      calories: item.calories,
      menuId: item.menu_id
    }));

  // Filter items based on selected menu, category, and search
  const filteredItems = transformedMenuItems.filter((item) => {
    // Filter by selected menu if specified
    if (selectedMenu && item.menuId && item.menuId !== selectedMenu.id) {
      return false;
    }

    // Filter by category
    const matchesCategory = activeCategory === "Show All" || item.category === activeCategory;
    
    // Filter by search query
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  const getTotalItems = () =>
    cart.reduce((total, item) => total + item.quantity, 0);
  const getTotalPrice = () =>
    cart.reduce((total, item) => total + item.price * item.quantity, 0);
  const getDiscountAmount = () => (getTotalPrice() * discount) / 100;
  const getSubTotal = () => getTotalPrice() - getDiscountAmount();
  const getTax = () => getSubTotal() * 0.14;
  const getFinalTotal = () => getSubTotal() + getTax();

  const resetOrder = () => {
    setCart([]);
    setDiscount(0);
    setPax(1);
    setDeliveryTable("");
    setSearchQuery("");
  };

  // Load variants and addons for selected product
  const loadProductDetails = async (item) => {
    setSelectedProduct({ ...item, variants: [], addons: [] });
    setShowVariantModal(true);
    setVariantsLoading(true);
    setAddonsLoading(true);

    try {
      // Fetch variants and addons simultaneously
      const [variantsData, addonsData] = await Promise.all([
        fetchMenuItemVariants(item.id, token),
        fetchMenuItemAddons(item.id, token)
      ]);

      // Transform variants data
      const transformedVariants = variantsData.map(variant => ({
        id: variant.id,
        name: variant.name,
        code: variant.code,
        price: parseFloat(item.price) + parseFloat(variant.price_modifier),
        priceModifier: parseFloat(variant.price_modifier),
        isDefault: variant.is_default,
        sortOrder: variant.sort_order
      }));

      // Transform addons data
      const transformedAddons = addonsData.map(addon => ({
        id: addon.id,
        name: addon.name,
        code: addon.code,
        price: parseFloat(addon.price),
        cost: parseFloat(addon.cost || 0),
        isRequired: addon.is_required,
        maxQuantity: addon.max_quantity,
        groupName: addon.addon_group_name,
        sortOrder: addon.sort_order
      }));

      // Update selected product with fetched data
      setSelectedProduct(prev => ({
        ...prev,
        variants: transformedVariants,
        addons: transformedAddons,
        hasVariations: transformedVariants.length > 0
      }));

    } catch (error) {
      console.error('Error loading product details:', error);
      // Keep modal open but show empty variants/addons
      setSelectedProduct(prev => ({
        ...prev,
        variants: [],
        addons: [],
        hasVariations: false
      }));
    } finally {
      setVariantsLoading(false);
      setAddonsLoading(false);
    }
  };

  const handleProductClick = (item) => {
    if (item.hasVariations) {
      // Load variants and addons dynamically
      loadProductDetails(item);
    } else {
      // For products without variations, still check for addons
      loadProductDetails(item);
    }
  };

  const addToCart = (item, variant = null, addons = []) => {
    // Calculate addons price
    const addonsPrice = addons.reduce((total, addon) => total + (addon.price * addon.quantity), 0);
    
    const cartItem = variant
      ? {
          ...item,
          id: `${item.id}_${variant.id}`, 
          name: `${item.name} - ${variant.name}`,
          price: variant.price + addonsPrice,
          originalPrice: variant.price,
          originalId: item.id,
          variantId: variant.id,
          variantName: variant.name,
          image: item.image,
          size: variant.name,
          addons: addons
        }
      : { 
          ...item, 
          image: item.image,
          price: item.price + addonsPrice,
          originalPrice: item.price,
          addons: addons
        };

    const existingItem = cart.find(
      (existingCartItem) => existingCartItem.id === cartItem.id
    );

    if (existingItem) {
      setCart(
        cart.map((existingCartItem) =>
          existingCartItem.id === cartItem.id
            ? { ...existingCartItem, quantity: existingCartItem.quantity + 1 }
            : existingCartItem
        )
      );
    } else {
      setCart([...cart, { ...cartItem, quantity: 1, comment: "" }]);
    }

    if (variant || addons.length > 0) {
      setShowVariantModal(false);
      setSelectedProduct(null);
      setSelectedAddons({});
    }
  };

  const removeFromCart = (itemId) => {
    setCart((prev) =>
      prev.map((item) =>
        item.id === itemId && item.quantity > 1
          ? { ...item, quantity: item.quantity - 1 }
          : item
      )
    );
  };

  const deleteFromCart = (itemId) => {
    setCart((prev) => prev.filter((item) => item.id !== itemId));
  };

  const VariantModal = () => {
    const [selectedAddons, setSelectedAddons] = useState({});

    if (!showVariantModal || !selectedProduct) return null;

    const handleAddonChange = (addonId, quantity) => {
      setSelectedAddons(prev => ({
        ...prev,
        [addonId]: quantity
      }));
    };

    const addProductWithAddons = (product, variant = null) => {
      const selectedAddonsArray = Object.entries(selectedAddons)
        .filter(([_, quantity]) => quantity > 0)
        .map(([addonId, quantity]) => {
          const addon = product.addons.find(a => a.id.toString() === addonId.toString());
          return {
            ...addon,
            quantity: quantity
          };
        });

      addToCart(product, variant, selectedAddonsArray);
      setSelectedAddons({});
    };

    const hasVariants = selectedProduct.variants && selectedProduct.variants.length > 0;
    const hasAddons = selectedProduct.addons && selectedProduct.addons.length > 0;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 bg-opacity-50 backdrop-blur-sm p-4">
        <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[98vh] overflow-hidden animate-in zoom-in-95 duration-300">
          <div className="relative p-6 border-b border-gray-100">
            <button
              onClick={() => {
                setShowVariantModal(false);
                setSelectedProduct(null);
                setSelectedAddons({});
              }}
              className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Customize Your Order
            </h3>
            <p className="text-sm text-gray-600">
              {hasVariants ? 'Choose size and add extras' : 'Add extras'}
            </p>
          </div>

          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center gap-4">
              <img
                src={selectedProduct.image}
                alt={selectedProduct.name}
                className="w-16 h-16 rounded-lg object-cover shadow-md"
              />
              <div>
                <h4 className="font-semibold text-gray-900 text-lg">
                  {selectedProduct.name}
                </h4>
                <p className="text-sm text-gray-500">
                  {selectedProduct.shortDescription || selectedProduct.description}
                </p>
                <p className="text-lg font-bold text-[#FF6500] mt-1">
                  {selectedProduct.price.toFixed(2)} L.E
                </p>
              </div>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            <div className="flex flex-col lg:flex-row">
              {/* Variants Section */}
              {hasVariants && (
                <div className="p-6 border-b lg:border-b-0 lg:border-r border-gray-100 lg:w-1/2">
                  <h5 className="font-semibold text-gray-900 mb-4">Choose Size</h5>
                  {variantsLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF6500] mx-auto mb-2"></div>
                      <p className="text-sm text-gray-500">Loading sizes...</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {selectedProduct.variants.map((variant) => (
                        <div
                          key={variant.id}
                          className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-xl transition-all duration-200 cursor-pointer group border-2 border-transparent hover:border-[#FF6500]/20"
                          onClick={() => addProductWithAddons(selectedProduct, variant)}
                        >
                          <div className="flex-1">
                            <h6 className="font-semibold text-gray-900 group-hover:text-[#FF6500] transition-colors">
                              {variant.name}
                            </h6>
                            {variant.priceModifier !== 0 && (
                              <p className="text-sm text-gray-500">
                                {variant.priceModifier > 0 ? '+' : ''}{variant.priceModifier.toFixed(2)} L.E
                              </p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-lg text-[#FF6500]">
                              {variant.price.toFixed(2)} L.E
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Addons Section */}
              {hasAddons && (
                <div className="p-6 lg:w-1/2">
                  <h5 className="font-semibold text-gray-900 mb-4">Add Extras</h5>
                  {addonsLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF6500] mx-auto mb-2"></div>
                      <p className="text-sm text-gray-500">Loading extras...</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {selectedProduct.addons.map((addon) => (
                        <div key={addon.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <h6 className="font-medium text-gray-900">{addon.name}</h6>
                            <p className="text-sm text-[#FF6500] font-semibold">
                              +{addon.price.toFixed(2)} L.E
                            </p>
                            {addon.isRequired && (
                              <span className="text-xs text-red-500 font-medium">Required</span>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleAddonChange(addon.id, Math.max(0, (selectedAddons[addon.id] || 0) - 1))}
                              className="w-8 h-8 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors disabled:opacity-50"
                              disabled={!selectedAddons[addon.id] || selectedAddons[addon.id] === 0}
                            >
                              <Minus className="w-4 h-4" />
                            </button>
                            <span className="w-8 text-center font-medium">
                              {selectedAddons[addon.id] || 0}
                            </span>
                            <button
                              onClick={() => {
                                const currentQuantity = selectedAddons[addon.id] || 0;
                                if (addon.maxQuantity && currentQuantity >= addon.maxQuantity) return;
                                handleAddonChange(addon.id, currentQuantity + 1);
                              }}
                              className="w-8 h-8 bg-[#FF6500] text-white rounded-full flex items-center justify-center hover:bg-[#FF6500]/90 transition-colors disabled:opacity-50"
                              disabled={addon.maxQuantity && (selectedAddons[addon.id] || 0) >= addon.maxQuantity}
                            >
                              <Plus className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Add to Cart Button for products without variants or when only addons */}
          {(!hasVariants || (!variantsLoading && selectedProduct.variants.length === 0)) && (
            <div className="p-6 border-t border-gray-100">
              <button
                onClick={() => addProductWithAddons(selectedProduct)}
                className="w-full bg-[#FF6500] text-white py-3 px-6 rounded-xl font-semibold hover:bg-[#FF6500]/90 transition-colors"
                disabled={addonsLoading}
              >
                {addonsLoading ? 'Loading...' : 'Add to Cart'}
              </button>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading menu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="max-w-8xl mx-auto px-4 py-8">
        {/* Menu Selector */}
        {menus.length > 1 && (
          <div className="mb-6">
            <select
              value={selectedMenu?.id || ''}
              onChange={(e) => {
                const menu = menus.find(m => m.id === parseInt(e.target.value));
                setSelectedMenu(menu);
              }}
              className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
            >
              <option value="">Select Menu</option>
              {menus.map(menu => (
                <option key={menu.id} value={menu.id}>
                  {menu.name} {menu.is_default ? '(Default)' : ''}
                </option>
              ))}
            </select>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Menu Section */}
          <div className="lg:col-span-2">
            {/* Search Bar */}
            <div className="relative mb-6">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search menu items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              />
            </div>

            {/* Categories */}
            <div className="flex flex-wrap gap-3 mb-8 overflow-x-auto pb-2">
              {transformedCategories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <button
                    key={category.name}
                    onClick={() => setActiveCategory(category.name)}
                    className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2 ${
                      activeCategory === category.name
                        ? "bg-[#FF6500] text-white shadow-lg transform scale-105"
                        : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
                    }`}
                  >
                    <IconComponent className="w-5 h-5" />
                    {category.name}
                  </button>
                );
              })}
            </div>

            {/* Menu Items Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4">
              {filteredItems.map((item) => (
                <div
                  key={item.id}
                  className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group cursor-pointer relative"
                  onClick={() => handleProductClick(item)}
                >
                  {/* Variant Badge */}
                  {item.hasVariations && (
                    <div className="absolute top-3 right-3 z-10">
                      <div className="bg-gradient-to-r from-[#FF6500] to-[#FF8534] text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                          Variant
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Spicy Badge */}
                  {item.isSpicy && (
                    <div className="absolute top-3 left-3 z-10">
                      <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                        🌶️ Spicy
                      </div>
                    </div>
                  )}

                  <div className="relative">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        e.target.src = "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?w=300&h=200&fit=crop";
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  </div>

                  <div className="p-4">
                    <h3 className="text-lg font-bold text-gray-800 mb-1 truncate whitespace-nowrap">
                      {item.name}
                    </h3>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-[#FF6500]">
                          {item.price === 0
                            ? "varies"
                            : `${item.price.toFixed(2)} L.E`}
                        </span>
                      </div>
                    </div>
                    {/* Additional info */}
                    <div className="mt-2 text-xs text-gray-500">
                      {item.prepTime && <span>⏱️ {item.prepTime}min</span>}
                      {item.calories && <span className="ml-2">🔥 {item.calories}cal</span>}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredItems.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">🍽️</div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No items found</h3>
                <p className="text-gray-500">
                  {searchQuery 
                    ? `No items match "${searchQuery}"`
                    : `No items available in "${activeCategory}" category`
                  }
                </p>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <OrderSummary
            cart={cart}
            setCart={setCart}
            addToCart={addToCart}
            removeFromCart={removeFromCart}
            deleteFromCart={deleteFromCart}
          />
        </div>
      </div>

      {/* Variant Modal */}
      <VariantModal />
    </div>
  );
};

export default Pos;