"use client"
import React, { useState } from 'react';
import { Check, Clock, ChefHat, CheckCircle, XCircle } from 'lucide-react';

const KOTSettings = () => {
  const [enableItemLevelStatus, setEnableItemLevelStatus] = useState(true);
  const [defaultKOTStatus, setDefaultKOTStatus] = useState('pending');

  const handleSave = () => {
    // Handle save logic here
    console.log('Settings saved:', {
      enableItemLevelStatus,
      defaultKOTStatus
    });
    // You could show a success message or make an API call
  };

  const kotStatuses = [
    {
      id: 'pending',
      label: 'Pending',
      description: 'Initial status when KOT is created and waiting to be processed',
      icon: <Clock className="w-5 h-5" />
    },
    {
      id: 'cooking',
      label: 'Cooking',
      description: 'Status when kitchen staff is preparing the order',
      icon: <ChefHat className="w-5 h-5" />
    },
    {
      id: 'ready',
      label: 'Ready',
      description: 'Status when the order is prepared and ready to serve',
      icon: <CheckCircle className="w-5 h-5" />
    },
    {
      id: 'served',
      label: 'Served',
      description: 'Status when the order has been served to the customer',
      icon: <Check className="w-5 h-5" />
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-8">KOT Settings</h1>
          
          {/* Enable Item Level Status */}
          <div className="mb-8">
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 pt-1">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={enableItemLevelStatus}
                      onChange={(e) => setEnableItemLevelStatus(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-6 h-6 bg-white border-2 border-gray-300 rounded peer-checked:bg-blue-600 peer-checked:border-blue-600 flex items-center justify-center transition-all duration-200">
                      {enableItemLevelStatus && (
                        <Check className="w-4 h-4 text-white" />
                      )}
                    </div>
                  </label>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Enable Item Level Status
                  </h3>
                  <p className="text-gray-600">
                    Enable this to allow statuses to be set at the item level.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Default KOT Status */}
          <div className="mb-8">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Default KOT Status
              </h3>
              <p className="text-gray-600">
                Set the default status for the KOT.
              </p>
            </div>

            <div className="space-y-4">
              {kotStatuses.map((status) => (
                <div key={status.id} className="bg-gray-50 rounded-lg p-4">
                  <label className="flex items-start space-x-4 cursor-pointer">
                    <div className="flex-shrink-0 pt-1">
                      <div className="relative">
                        <input
                          type="radio"
                          name="defaultKOTStatus"
                          value={status.id}
                          checked={defaultKOTStatus === status.id}
                          onChange={(e) => setDefaultKOTStatus(e.target.value)}
                          className="sr-only peer"
                        />
                        <div className="w-6 h-6 border-2 border-gray-300 rounded-full peer-checked:border-blue-600 peer-checked:bg-blue-600 flex items-center justify-center transition-all duration-200">
                          {defaultKOTStatus === status.id && (
                            <div className="w-3 h-3 bg-white rounded-full"></div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="text-gray-600">
                          {status.icon}
                        </div>
                        <h4 className="text-lg font-semibold text-gray-900">
                          {status.label}
                        </h4>
                      </div>
                      <p className="text-gray-600">
                        {status.description}
                      </p>
                    </div>
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Save Button */}
          <div className="pt-6 border-t border-gray-200">
            <button
              onClick={handleSave}
              className="px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Save
            </button>
          </div>
        </div>

        {/* Additional Settings Preview */}
        <div className="mt-6 bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Settings Preview</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <span className="text-gray-600">Item Level Status:</span>
              <span className={`font-semibold ${enableItemLevelStatus ? 'text-green-600' : 'text-red-600'}`}>
                {enableItemLevelStatus ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div className="flex items-center justify-between py-2">
              <span className="text-gray-600">Default KOT Status:</span>
              <div className="flex items-center space-x-2">
                <span className="text-gray-600">
                  {kotStatuses.find(s => s.id === defaultKOTStatus)?.icon}
                </span>
                <span className="font-semibold text-gray-900">
                  {kotStatuses.find(s => s.id === defaultKOTStatus)?.label}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KOTSettings;