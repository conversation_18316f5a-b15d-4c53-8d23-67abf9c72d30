export default {
  // common
  direction: "rtl",
  login: "تسجيل الدخول",
  header: {
    home: "الرئيسية",
    login: "تسجيل الدخول",
    logOut: "تسجيل الخروج",
    clientDashboard: "لوحة تحكم العميل",
    jobOpportunities: " طلب تجربة",
    allProperties: "جميع العقارات",
    searchPlaceholder: "بحث",
    userMenu: {
      settings: "الإعدادات",
      logout: "تسجيل الخروج",
    },
    logoutConfirm: {
      title: "تأكيد تسجيل الخروج",
      message: "هل أنت متأكد أنك تريد تسجيل الخروج؟",
      cancel: "إلغاء",
      confirm: "تسجيل الخروج",
    },
    logoutSuccess: "تم تسجيل الخروج بنجاح",
    sendMessage: " خدمة العملاء",
  },
  welcome: "مرحبا",
  dashboard: "لوحة التحكم",
  language: "اللغة",
  english: "الإنجليزية",
  arabic: "العربية",
  hero: {
    title1: "اجعلها ذكاء اصطناعي",
    subtitle1: "فعالة وفعالة من حيث التكلفة",
    title2: "نظام إدارة علاقات العملاء العقاري الذكي",
    subtitle2: "أتمتة وتوسيع نطاق بسهولة",
  },
  buttons: {
    tryChat: "جرب روبوت الدردشة",
    requestDemo: "طلب عرض توضيحي",
    addNew: "إضافة جديد",
  },
  heroSection: {
    title1: "بيع بذكاء",
    title2: "الماركتينج عليك… والبيع علينا",
    subtitle:
      "فريق مبيعات كامل بالذكاء الاصطناعى (AI) بيلبى طلبات العملاء فى اى وقت",
    description:
      "فريق مبيعات متكامل بالذكاء الاصطناعى بيرد على العملاء فورًا وعلى مدار ٢٤ ساعة، بيحدد مين العميل الجاد من أول لحظة، وبيقترح عليه العقار المناسب حسب احتياجاته، وكل ده من غير ما تتدخل. كمان يحدد مواعيد المعاينة بشكل أوتوماتيكي بيوفرلك نظام CRM كامل تقدر من خلاله تدير كل الوحدات والعملاء بسهولة. يعني حتى لو مشغول أو مش موجود، فريق المبيعات بالذكاء الاصطناعي شغّال مكانك ويساعدك تزوّد المبيعات وتكسب وقتك.",
    ctaButton: "ابدأ الآن",
    backgroundAlt: "خلفية",
    aiImageAlt: "مساعد الذكاء الاصطناعي",
    message1: "تصنيف العملاء حسب الجدية",
    message2: "سيستم ذكي لإدارة الوحدات، العملاء، وفريق المبيعات",
    message3: " فريق مبيعات ذكي شغال 24 ساعة – مش هيفوتك ليد",
    message4: "شات بوت ذكي على موقعك بيرد مكانك",
    message5: " متابعة مباشرة للعملاء وتحليل السوق لحظة بلحظة",
    message6: "متابعة مباشرة للعملاء وتحليل السوق لحظة بلحظة",
    message7: "تابع ووسّع نشاطك بسهولة",
  },

  dashboard: {
    title: {
      part1: "لينا معك",
      part2: "طوال الوقت",
    },
    subtitle:
      "موقع إلكتروني وتطبيق موبايل - تقدر تتابع شغلك في أي وقت ومن أي مكان",
    featuresTitle: "بعض الحلول اللى لينا بتقدمها:",
    features: {
      feature1:
        " تطبيق دردشة موبايل : خلى العملاء بتوعك يتواصلو معاك ف اى وقت عن طريق الشات اب",
      feature2:
        "تطبيق موبايل للبروكرز – يبلغ فريق المبيعات فورًا أول ما يتحدد معاينة العقار ,تقدر تتابع محادثات وتفاعل لينا مع عملائك لحظة بلحظة.",
      feature3:
        " متابعة تلقائية بالذكاء الاصطناعي – السيستم بيتابع مع العملاء أوتوماتيك ويوفر وقت فريقك.",
      feature4: "شات بوت على موقعك – بيرد على استفسارات الزوار ٢٤ ساعة",
      feature5:
        " CRM ذكي – نظام متكامل لإدارة المبيعات، الوحدات، والعملاء بسهولة.",
      feature6:
        " تحليلات عن العملاء – شوف احتياجات كل عميل وسجل تواصله علشان تركّز على الفرص الصح وتقفل الصفقات أسرع.",
    },
    ctaButton: "ابدأ الآن",
    images: {
      desktopAlt: "لوحة تحكم لينا لنظام إدارة العلاقات - عرض سطح المكتب",
      mobileAlt: "عرض جوال لنظام لينا لإدارة العلاقات",
    },
  },
  salesManager: {
    title: "هل تواجه نفس التحديات؟",
    subtitle: "تعرف على سارة وهاني: مديرين مبيعات عندهم تحديات حقيقية",
    haniTitle: "تعرف على هاني - مدير المبيعات المثقل بالأعباء",
    challenges: {
      challenge1:
        "أهدر ساعات في التحدث مع عملاء غير مؤهلين ليسوا مشترين جادين.",
      challenge2:
        "بيقضى ربع اليوم بس محاولات المتابعة مع العملاء قبل ما يشتروا من غيره",
      challenge3:
        "عندنا CRM بس مش بنستخدمه عشان صعب او ملوش علاقه بالواقع وكمان الدعم مش موجود",
    },
    imageAlt: "هاني - مدير المبيعات المثقل بالأعباء",
  },
  dataInsights: {
    imageAlt: "سارة - مديرة التسويق",
    saraTitle: "تعرف على سارة - مديرة التسويق",
    challenges: {
      challenge1:
        "أنفق الآلاف على الإعلانات، لكن معظم العملاء ليسوا مشترين جادين!",
      challenge2:
        "فريق المبيعات يلومني على 'العملاء منخفضي الجودة'، لكنني لا أملك الأدوات المناسبة لمعرفة الجادين منهم",
      challenge3:
        "بحلول الوقت الذي يصل فيه فريقنا إلى العملاء، يكونون قد اختاروا بالفعل منافسًا.",
    },
    footer: {
      question: "هل يبدو هذا مألوفًا؟",
      solution: "لينا هنا لحل هذه التحديات.",
    },
    ctaButton: "ابدأ الآن",
  },
  footer: {
    companyInfo: {
      title: "معلومات الشركة",
      address:
        "عنوان الشركة: مبنى 505 سياك، ساحة أركبلان، العاصمة الإدارية الجديدة، القاهرة، مصر",
    },
    contact: {
      title: "بيانات الاتصال",
      phone: "معلومات الاتصال: ",
      sales: "مبيعات",
      support: "دعم",
    },
    connect: {
      title: "تواصل معنا",
      linkedin: "لينكد إن",
      privacyPolicy: "اقرأ سياسة الخصوصية الخاصة بنا",
      chat: "تحدث معنا",
      dowenloadios: "حمّل تطبيقنا على iOS",
      dowenloadAndroid: "حمّل تطبيقنا على Android",
      Facebook: "فيسبوك",
    },
    copyright: "©",
    companyName: "لينا AI",
    rightsReserved: "جميع الحقوق محفوظة",
    version: "الإصدار",
  },
  calendarModal: {
    closeButton: "إغلاق",
    confirmationTitle: "تم تأكيد الحجز!",
    thankYou: "شكراً لك",
    meetingScheduled: "تم جدولة اجتماعك في",
    at: "في الساعة",
    confirmationSent: "لقد أرسلنا بريد تأكيد إلى",
    withDetails: "مع جميع التفاصيل.",
  },
  calendar: {
    backButton: "العودة",
    logoAlt: "شعار لينا AI",
    companyName: "لينا AI",
    meetingDuration: "اجتماع لمدة 30 دقيقة",
    duration: "30 دقيقة",
    conferencingDetails: "سيتم توفير تفاصيل المؤتمر عبر الويب عند التأكيد.",
    bookMeeting: "حجز اجتماع",
    meetingDescription: "حجز اجتماع لمدة 30 دقيقة مع فريق لينا",
    selectDateTime: "اختر التاريخ والوقت",
    previousMonth: "الشهر السابق",
    nextMonth: "الشهر التالي",
    dayHeaders: [
      "الأحد",
      "الإثنين",
      "الثلاثاء",
      "الأربعاء",
      "الخميس",
      "الجمعة",
      "السبت",
    ],
    confirmButton: "تأكيد",
    availableTimes: "الأوقات المتاحة",
    selectDayPrompt: "اختر يومًا لرؤية الأوقات المتاحة.",
    enterInfo: "أدخل معلوماتك",
    bookingFor: "أنت تحجز اجتماعًا في",
    at: "في الساعة",
    nameLabel: "الاسم",
    emailLabel: "عنوان البريد الإلكتروني",
    phoneLabel: "رقم الهاتف",
    phonePlaceholder: "0 ************",
    companyLabel: "الشركة",
    notesLabel: "يرجى مشاركة أي شيء قد يساعد في التحضير لاجتماعنا.",
    notesPlaceholder: "أخبرنا كيف يمكننا مساعدتك...",
    completeBooking: "إكمال الحجز",
    bookingInProgress: "جارى حجز الاجتماع",
  },
  login: {
    title: "لينا الذكاء الاصطناعي",
    subtitle: "سجل الدخول إلى حسابك",
    backToHome: "العودة إلى الصفحة الرئيسية",
    usernameLabel: "اسم المستخدم",
    usernamePlaceholder: "أدخل اسم المستخدم",
    passwordLabel: "كلمة المرور",
    passwordPlaceholder: "أدخل كلمة المرور",
    signInButton: "تسجيل الدخول",
    successMessage: "تم تسجيل الدخول بنجاح",
    errorMessage: "فشل تسجيل الدخول. يرجى التحقق من بيانات الاعتماد الخاصة بك",
    footer: {
      copyright: "لينا الذكاء الاصطناعي. جميع الحقوق محفوظة",
      version: "الإصدار",
    },
  },
  sidebar: {
    "schedule":"مهام الاسبوع",
    myProjects: "مشاريعي",
    dashboard: " المحادثات",
    analytics: "التحليلات",
    units: "الوحدات",
    team: "الفريق",
    darkMode: "الوضع المظلم",
    logout: "تسجيل الخروج",
    logoutConfirm: {
      title: "هل أنت متأكد؟",
      message: "سيتم تسجيل خروجك من حسابك",
      cancel: "إلغاء",
      confirm: "تسجيل الخروج",
    },
    logoutSuccess: "تم تسجيل الخروج بنجاح",
  },
  dashboardFilter: {
    actions: {
      all: "كل الإجراءات",
      Monitorlead: "مراقبة العميل ",
      makeCall: "إجراء مكالمة",
      officeVisit: "زيارة مكتب",
      propertyView: "معاينه",
      notInterested: "غير مهتم",
      notQualified: "غير مؤهل",
      followUpLater: "متابعة لاحقاً",
      missingRequirement: "متطلب مفقود",
      blocked: "محظور",
      print: "طباعه",
      onGoingConversion: "محادثه جاريه",
      qualifiedLead: "موجه للشراء",
      noAction: "لا يوجد إجراء",
      allActions: "كل الإجراءات",
    },

    datePicker: {
      startDate: "تاريخ البداية",
      endDate: "تاريخ النهاية",
      cancel: "إلغاء",
      apply: "تطبيق",
    },
    whatsappButton: " عملاء واتساب",
    premuim: "ميزة مدفوعة",
    ADD: "اضافة عميل  جديد",
    cities: {
      "Beni Suef": "بني سويف",
      Qena: "قنا",
      Ismailia: "الإسماعيلية",
      Alexandria: "الإسكندرية",
      Aswan: "أسوان",
      "Kafr El Sheikh": "كفر الشيخ",
      Luxor: "الأقصر",
      Faiyum: "الفيوم",
      Monufia: "المنوفية",
      "Red Sea": "البحر الأحمر",
      Cairo: "القاهرة",
      Giza: "الجيزة",
      "North Sinai": "شمال سيناء",
      Dakahlia: "الدقهلية",
      Beheira: "البحيرة",
      "Port Said": "بورسعيد",
      Qalyubia: "القليوبية",
      Suez: "السويس",
      "South Sinai": "جنوب سيناء",
      Gharbia: "الغربية",
      Sohag: "سوهاج",
      Minya: "المنيا",
      Matrouh: "مطروح",
      Damietta: "دمياط",
      "New Administrative Capital": "العاصمة الإدارية الجديدة",
      Sharqia: "الشرقية",
      Asyut: "أسيوط",
      "New Valley": "الوادي الجديد",
    },
  },
  search: {
    placeholder: "ابحث عن العميل بالاسم أو الرقم...",
  },
  clientsTable: {
    noClients: "لا يوجد عملاء",
    headers: {
      name: "الاسم",
      userNumber: "رقم العميل",
      date: "التاريخ",
      requirements: "المتطلبات",
      messageCount: "عدد الرسائل",
      action: "الإجراء",
    },
    newLead: "عميل جديد",
    notDefined: "غير محدد",
    actions: {
      addAction: "إضافة إجراء لـ {count} عميل",
    },
    lastActivity: {
      na: "غير متاح",
    },
    unreadMessages: {
      na: "لا توجد رسائل غير مقروءة",
      count: "{count} رسالة غير مقروءة",
    }
  },
  actionForm: {
    actions: {
      officeVisit: "زيارة مكتب",
      makeCall: "إجراء مكالمة",
      propertyView: "عرض عقار",
      notInterested: "غير مهتم",
      notQualified: "غير مؤهل",
      followUpLater: "متابعة لاحقاً",
      missingRequirement: "متطلب مفقود",
    },
    commentPlaceholder: "أدخل تعليقك هنا...",
    submitButton: "إرسال",
    successMessage: "تم إضافة الإجراء بنجاح",
    errorMessage: "فشل إضافة الإجراء",
    aiAction: "إجراءات الذكاء الاصطناعي",
  },
  propertyDetails: {
    title: "_المتطلبات",
    title2: "/المتطلبات",
    fields: {
      buildingType: "نوع المبنى",
      landArea: "مساحة الأرض",
      floor: "الطابق",
      roomsCount: "عدد الغرف",
      bathroomCount: "عدد الحمامات",
      viewType: "الإطلالة",
      garageSize: "مساحة الحديقة",
      finishingType: "التشطيب",
      developer: "المطور",
      downPayment: "المقدم",
      deliveryDate: "تاريخ التسليم",
      totalPrice: "السعر الإجمالي",
      propertyPurpose: "لل{purpose}",
    },
    purchaseProbability: "احتمالية الشراء",
    buttons: {
      close: "إغلاق",
    },
    notAvailable: "-----",
  },
  identifierUnit: {
    title: "الممتلكات العقارية",
    subtitle: "تصفح عقاراتنا الحصرية",
  },
  shareModel: {
    Content: "محتوى الوحدة",
    post: "نشر المحتوى",
    images: "الصور",
    loading: "جاري تحميل بيانات المشاركة...",
    copy: "تم النسخ!",
    copyLink: "نسخ مع الرابط",
    noData: "لا توجد بيانات للمشاركة.",
    noImage: "لا توجد صور للمشاركة.",
  },
  unitsFilter: {
    allDevelopers: "جميع المطورين",
    allCompounds: "جميع المجمعات",
    allPurposes: "جميع الأغراض",
    allPropertyTypes: "جميع أنواع العقارات",
    price: "السعر",
    max: "اعلى سعر",
    min: "اقل سعر",
    applay: "تطبيق",
    activeFilter: " الفلاتر النشطه :",
    clearall: "مسح جميع الفلاتر",
    allCities: "جميع المدن",

    purposes: {
      buy: "شراء",
      rent: "إيجار",
      sell: "بيع",
      lease: "تأجير",
    },
    cities: {
      "Beni Suef": "بني سويف",
      Qena: "قنا",
      Ismailia: "الإسماعيلية",
      Alexandria: "الإسكندرية",
      Aswan: "أسوان",
      "Kafr El Sheikh": "كفر الشيخ",
      Luxor: "الأقصر",
      Faiyum: "الفيوم",
      Monufia: "المنوفية",
      "Red Sea": "البحر الأحمر",
      Cairo: "القاهرة",
      Giza: "الجيزة",
      "North Sinai": "شمال سيناء",
      Dakahlia: "الدقهلية",
      Beheira: "البحيرة",
      "Port Said": "بورسعيد",
      Qalyubia: "القليوبية",
      Suez: "السويس",
      "South Sinai": "جنوب سيناء",
      Gharbia: "الغربية",
      Sohag: "سوهاج",
      Minya: "المنيا",
      Matrouh: "مطروح",
      Damietta: "دمياط",
      "New Administrative Capital": "العاصمة الإدارية الجديدة",
      Sharqia: "الشرقية",
      Asyut: "أسيوط",
      "New Valley": "الوادي الجديد",
    },
  },
  unitsSearch: {
    placeholder: "ابحث بالاسم أو الموقع...",
    clearAriaLabel: "مسح البحث",
  },
  units: {
    addButton: {
      addNew: " وحدة جديدة",
      edit: "تعديل الوحدة",
      back: "للخلف",
    },
  },
  modal: {
    addNewUnit: "إضافة وحدة جديدة",
    editUnit: "تعديل الوحدة",
  },
  steps: {
    basicDetails: "التفاصيل الأساسية",
    financialDetails: "التفاصيل المالية",
    rentalDetails: "تفاصيل الإيجار",
    imagesInfo: "الصور ومعلومات إضافية",
  },
  buttons: {
    back: "رجوع",
    next: "التالي",
    saveUnit: "حفظ الوحدة",
    addPhase:"إضافة مرجله",
    updatePhase:"تعديل مرحله"
  },
  toasts: {
    enterValidPrice: "يرجى إدخال سعر أكبر من 0 لنوع مدة واحد على الأقل",
    uploadImage: "يرجى تحميل صورة واحدة على الأقل.",
    selectPurpose: "يرجى تحديد غرض الوحدة (بيع/إيجار)",
    errorProcessing: "حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى",
  },
  addPhase: "اضافة مرحلة",
 
    updatePhase:"تعديل مرحله",
  projectFirst: "اختر المشروع اولا",
  formLabels: {
    unitTitle: "عنوان الوحدة",
    buildingType: "نوع المبنى",
    purpose: "الغرض",
    compound: "المجمع",
    view: "الإطلالة",
    isGated: "مجتمع مغلق",
    city: "المدينة",
    district: "الحي",
    developer: "المطور",
    deliveryDate: "تاريخ التسليم",
    deliveryStatus: "حالة التسليم",
    bathroomCount: "الحمامات",
    floor: "الطابق",
    roomsCount: "الغرف",
    landArea: "مساحة الأرض (م²)",
    gardenSize: "مساحة الحديقة (م²)",
    finishing: "التشطيب",
    garageArea: "مساحة المرآب (م²)",
    images: "الصور",
    districtFirst: "اختر الحي اولا",
    cityFirst: "اختر المدينه اولا",
    selectDistrict: "اختر الحي ",
  },

  buildingTypes: {
    apartment: "شقة",
    villa: "فيلا",
    townhouse: "تاون هاوس",
    duplex: "دوبلكس",
    penthouse: "بنتهاوس",
    studio: "استوديو",
    chalet: "شاليه",
    office: "مكتب",
    shop: "محل",
    land: "أرض",
  },
  purpose: {
    sell: "بيع",
    rent: "إيجار",
    buy: "شراء",
    lease: "تأجير",
  },
  view: {
    garden: "حديقة",
    pool: "مسبح",
    sea: "بحر",
    landmark: "معلم",
    street: "شارع",
    other: "أخرى",
  },
  deliveryStatus: {
    ready: "جاهز",
    underConstruction: "تحت الإنشاء",
    offPlan: "على المخطط",
  },
  finishing: {
    finished: "مشطب",
    semiFinished: "نصف تشطيب",
    core: "دون تشطيب",
    unfinished: "غير مشطب",
  },
  sale: {
    downPayment: "دفعة مقدمة",
    totalPrice: "السعر الإجمالي",
    deliveryDate: "تاريخ التسليم",
    paymentPlans: "خطط الدفع",
    addPaymentPlan: "إضافة خطة دفع",
    yearsLabel: "السنوات",
    priceLabel: "السعر",
    maintenanceLabel: "الصيانة",
  },
  rental: {
    isAvailable: "متاح للإيجار",
    availabilityDate: "تاريخ الإتاحة",
    rentDuration: "مدة الإيجار",
    daily: "يومي",
    weekly: "أسبوعي",
    monthly: "شهري",
    price: "السعر",
    securityDeposit: "تأمين",
    cleaningFee: "رسوم التنظيف",
    serviceFee: "رسوم الخدمة",
    amenities: "وسائل الراحة",
    currency: "العملة",
  },
  basicDetails: {
    selectPhase: "اختر المرحلة",
    noPhases: "لا يوجد مراحل في هذا المشروع",
    selectProject: "اختر المشروع",

    propertyDetails: "تفاصيل العقار",
    propertySpecs: "مواصفات العقار",
    unitTitle: "عنوان الوحدة",
    compound: "كمبوند",
    buildingType: "نوع المبنى",
    purpose: "الغرض",
    city: "المدينة",
    view: "الإطلالة",
    district: "الحي",
    rooms: "غرف",
    bathrooms: "حمامات",
    floor: "الدور",
    landArea: "مساحة الأرض",
    gardenSize: "مساحة الحديقة",
    garageArea: "مساحة الجراج",
    selectCompound: "اختر كمبوند",
    selectPurpose: "اختر الغرض",
    selectCity: "اختر المدينة",
    selectView: "اختر الإطلالة",
    placeholders: {
      unitTitle: "أدخل عنوان الوحدة",
      district: "اسم الحي",
      code: "كود الوحدة (اختياري)",
    },

    code: "كود الوحدة",
    model: "نموذج الوحدة",
    buildingTypes: {
      apartment: "شقة",
      villa: "فيلا",
      townhouse: "تاون هاوس",
      duplex: "دوبلكس",
      penthouse: "بنتهاوس",
      studio: "استوديو",
      chalet: "شاليه",
      office: "مكتب",
      shop: "محل",
      twinhouse: "توين هاوس",
      house: "منزل",
    },
    purposes: {
      sell: "بيع",
      rent: "إيجار",
    },
    cities: {
      cairo: "القاهرة",
      alexandria: "الإسكندرية",
      giza: "الجيزة",
      newCairo: "القاهرة الجديدة",
      october6: "السادس من أكتوبر",
      elShorouk: "الشروق",
      sheikhZayed: "الشيخ زايد",
    },
    views: {
      park: "حديقة",
      street: "شارع",
      lagoon: "بحيرة",
      sea: "بحر",
      city: "مدينة",
      river: "نهر",
      pool: "حمام سباحة",
      golf: "جولف",
      garden: "حديقة",
      openArea: "منطقة مفتوحة",
      mountain: "جبل",
    },
  },
  saleDetails: {
    financialDetails: "التفاصيل المالية",
    totalPrice: "السعر الإجمالي",
    deliveryDate: "تاريخ التسليم",
    downPayment: "الدفعة المقدمة",
    paymentPlans: "خطط الدفع",
    addPlan: "إضافة خطة",
    noPlans: "لم يتم إضافة خطط دفع بعد.",
    years: "السنوات",
    price: "السعر",
    maintenance: "الصيانة",
  },
  rentalDetails: {
    availability: "التوفر",
    chooseAvailabilityDate: "اختر تاريخ التوفر",
    availableForRent: "متاح للإيجار",
    rentDurationOptions: "خيارات مدة الإيجار",
    daily: "يومي",
    weekly: "أسبوعي",
    monthly: "شهري",
    price: "السعر",
    securityDeposit: "تأمين",
    cleaningFee: "رسوم التنظيف",
    serviceFee: "رسوم الخدمة",
    amenities: "المرافق",
    amenities: {
      wifi: "واي فاي",
      dryer: "مجفف",
      air_conditioning: "تكييف هواء",
      heating: "تدفئة",
      smart_tv: "تلفزيون ذكي",
      hair_dryer: "مجفف شعر",
      pool: "مسبح",
      free_parking: "موقف سيارات مجاني",
      ev_charger: "شاحن سيارات كهربائية",
      bbq_grill: "شواية",
      indoor_fireplace: "موقد داخلي",
      smoking_allowed: "التدخين مسموح",
      beachfront: "على الشاطئ",
      smoke_alarm: "جهاز إنذار الدخان",
      co_alarm: "جهاز إنذار أول أكسيد الكربون",
    },
  },
  january: "يناير",
  february: "فبراير",
  march: "مارس",
  april: "أبريل",
  may: "مايو",
  june: "يونيو",
  july: "يوليو",
  august: "أغسطس",
  september: "سبتمبر",
  october: "أكتوبر",
  november: "نوفمبر",
  december: "ديسمبر",
  numberConversation: "عدد المحادثات",
  lastMonth: "اخر شهر",
  lastMonths: "اخر {count} شهر",
  selectNumberOfMonths: "اختر عدد الاشهر",
  months: "الاشهر",
  conversation: "المحادثات",
  dailyConversationAnalysis: "تحليل المحادثات اليومية",
  averageMessagesPerUser: "متوسط الرسائل لكل مستخدم",
  count: "عدد الرسائل والمحادثات",
  dashboardTitle: "لوحة التحليلات",
  updatephase:"تعديل المرحله",
  dashboardDescription: "عرض أنشطة المستخدم وتحليلات الإجراءات اليومية",
  conversationAnalysis: "تحليل المحادثات",
  dailyConversationAnalysis: "تحليل المحادثات اليومية",
  countMessagesConversations: "عدد الرسائل والمحادثات",
  numberOfConversations: "عدد المحادثات",
  averageMessagesPerUser: "متوسط الرسائل لكل مستخدم",
  conversations: "المحادثات",
  avgMessages: "متوسط الرسائل",
  actionFrequency: "تكرار الإجراءات",
  monthlyActionFrequency: "تكرار الإجراءات الشهرية",
  breakdownDescription: "تفصيل الإجراءات المتخذة كل شهر",
  bubbleVisualization: "تصور فقاعي لإجمالي الإجراءات اليومية",
  totalActions: "إجمالي الإجراءات",
  clickBarInfo: "انقر على أي شريط لمشاهدة تفاصيل الإجراءات",
  addNewProject: "اضافة مشروع جديد",
  description: "الوصف",
  phases: "المراحل",
  viewInCRM: "زور موقعنا",
  noPhsesProject: "لا تتوفر مراحل لهذا المشروع",
  compoundUpdated: "تم تعديل المشروع بنجاح",
  compoundAdded: "تم اضافة المشروع بنجاح",
  updating: "تعديل...",
  updatesuccess: "",
  updateProject: "تعديل المشروع",
  projectUndfined: "لا توجد مشاريع متاحة لك",
  deleteButton: "حذف",
  cancelButton: "الغاء",
  sureDelet: "هل أنت متأكد أنك تريد الحذف؟",
  actionDelet: "لا يمكن التراجع عن هذا الإجراء.",
  deleteTitel: "حذف المشروع",
  failedProject: "خطا في حذف المشروع",
  projectDelete: "تم حذف المشروع بنجاح",
  associateProject: "هذا المشروع مرتبط بعدة وحدات.",
  typeYourMessage: "اكتب رسالتك هنا",
  send: "إرسال",
  Ai: "الرد الألي",
  manual: "رد مناسب",
  saveDeveloper: "حفظ المطور",
  saving: "جاري الحفظ...",
  cancel: "إلغاء",
  DeveloperName: "اسم المطور",
  bathrooms: "دورات المياه",
  for: "لل",
  sell: "بيع",
  rent: "إيجار",
  addNew: "إضافة جديد",
  city: "المدينة",
  project: "المشروع",
  rentPrice: "سعر الإيجار",
  totalPrice: "السعر الإجمالي",
  additionalDetails: "تفاصيل إضافية",
  finishingType: "نوع التشطيب",
  developer: "المطور",
  selectFinishingType: "اختر نوع التشطيب",
  furnished: "مفروش",
  unfurnished: "غير مفروش",
  fullyFinished: "تشطيب كامل",
  semiFinished: "نصف تشطيب",
  coreAndShell: "دون تشطيب",
  selectDeveloper: "اختر المطور ",
  furnishingType: "نوع التأثيث",
  selectDeveloper: "اختر المطور",
  propertyImages: "صور العقار",
  maximum: "الحد الأقصى",
  clickOrDragAndDrop: "انقر أو اسحب وأفلت الصور هنا",
  supportedFormats:
    "الصيغ المدعومة: JPG، PNG، WEBP (الحد الأقصى 5 ميجابايت لكل صورة)",
  uploading: "جارٍ التحميل...",
  upload: "تحميل",
  selectedImage: "صورة محددة",
  selectedImages: "صور محددة",
  images: "صور",
  selectedImagesTitle: "الصور المحددة:",
  uploadedImagesTitle: "الصور التي تم تحميلها:",
  retry: "إعادة المحاولة",
  imageDeletedSuccess: "تم حذف الصورة بنجاح",
  failedToDeleteImage: "فشل في حذف الصورة. يرجى المحاولة مرة أخرى.",
  maxImagesError:
    "يمكنك تحميل 8 صور كحد أقصى. يرجى إزالة بعض الصور قبل إضافة صور جديدة.",
  invalidFileType: "نوع ملف غير صالح. يرجى تحميل صور بصيغة JPG أو PNG أو WEBP.",
  fileSizeExceeds: "تجاوز 3 ميجابايت. يرجى تحميل صور أصغر.",
  failedToUploadImage: "فشل في تحميل الصورة",
  unitPage: {
    backToUnits: "العودة إلى الوحدات",
    in: "في",
    deleteUnit: "حذف الوحدة",
    confirmDeleteMsg: "هل أنت متأكد أنك تريد حذف هذه الوحدة؟",
    cancel: "إلغاء",
    delete: "حذف",
    deleteFail: "فشل حذف الوحدة. يرجى المحاولة مرة أخرى.",
    deleteError: "حدث خطأ أثناء حذف الوحدة.",
  },
  unitDetails: {
    title: "تفاصيل الوحدة",
    noImages: "لا توجد صور متاحة",
    noThumbnails: "لا توجد صور مصغرة متاحة",
    viewFullscreen: "انقر لعرض الصورة بحجم كامل",
    deleteConfirmation: "هل أنت متأكد أنك تريد حذف هذه الوحدة؟",
    delete: "حذف",
    cancel: "إلغاء",
    updateSuccess: "تم تحديث الوحدة بنجاح",
    galleryTitle: "معرض الصور",
    galleryNavigation: "استخدم مفاتيح الأسهم أو السحب للتنقل",
    imageCounter: "الصورة {current} من {total}",
    amenities: "المرافق:",
    noAmenities: "لا توجد مرافق محددة",
    location: "الموقع",
    developer: "المطور",
    dailyRate: "سعر اليومي",
    weeklyRate: "سعر الأسبوعي",
    monthlyRate: "سعر الشهري",
    totalPrice: "السعر الإجمالي",
    securityDeposit: "وديعة الضمان",
    serviceFee: "رسوم الخدمة",
    cleaningFee: "رسوم التنظيف",
    availableFrom: "متاح من:",
    rooms: "الغرف",
    bathrooms: "الحمامات",
    floor: "الطابق",
    view: "الإطلالة",
    city: "المدينة",
    country: "الدولة",
    purpose: "الغرض",
    buildingType: "نوع المبنى",
    finishing: "التشطيب",
    landArea: "مساحة الأرض",
    gardenSize: "مساحة الحديقة",
    deliveryDate: "تاريخ التسليم:",
    deliveryStatus: "حالة التسليم:",
    developer: "المطوّر",
    deliveryDate: "تاريخ التسليم",
    deliveryDatee: "Delivery Date",
    floor: "الطابق",
    finishing: "التشطيب",
    furnishd: "التأثيث",
    area: "المساحة",
    view: "الإطلالة",
    rooms: "الغرف",
    bathrooms: "دورات المياه ",
    notAvailable: "غير متوفر",
    ground: "أرضي",
    first: "الأول",
    second: "الثاني",
    third: "الثالث",
    th: "",
    viewTypes: {
      garden: "حديقة",
      pool: "مسبح",
      mountain: "جبل",
      street: "شارع",
      city: "مدينة",
      park: "حديقة",
      lake: "بحيرة",
      desert: "صحراء",
      river: "نهر",
      courtyard: "فناء",
      open: "مفتوح",
      desert: "صحراء",
      river: "نهر",
      courtyard: "فناء",
      forest: "غابة",
      beach: "شاطئ",
      lagoon: "بحيرة",
      openArea: "منطقة مفتوحة",
    },
    finishingTypes: {
      "fully finished": "تشطيب كامل",
      "semi finished": "نصف تشطيب",
      "core & shell": "دون تشطيب",
      unfinished: "غير مشطب",
      finished: "مشطب",
    },
    furnishingTypes: {
      furnished: "مفروش",
      unfurnished: "غير مفروش",
      "semi furnished": "نصف مفروش",
    },
    amenitiesTypes: {
      wifi: "واي فاي",
      dryer: "مجفف",
      air_conditioning: "تكييف هواء",
      heating: "تدفئة",
      smart_tv: "تلفزيون ذكي",
      hair_dryer: "مجفف شعر",
      pool: "مسبح",
      free_parking: "موقف سيارات مجاني",
      ev_charger: "شاحن سيارات كهربائية",
      bbq_grill: "شواية",
      indoor_fireplace: "موقد داخلي",
      smoking_allowed: "التدخين مسموح",
      beachfront: "على الشاطئ",
      smoke_alarm: "جهاز إنذار الدخان",
      co_alarm: "جهاز إنذار أول أكسيد الكربون",
    },
    buildingTypesMap: {
      "Not defined": "غير محدد",
      apartment: "شقة",
      villa: "فيلا",
      townhouse: "تاون هاوس",
      duplex: "دوبلكس",
      penthouse: "بنتهاوس",
      studio: "استوديو",
      chalet: "شاليه",
      office: "مكتب",
      shop: "محل",
      twinhouse: "توين هاوس",
      house: "منزل",
    },
  },
  common: {
    ProcessingImages: "جارٍ معالجة الصور...",
    addImage: "إضافة صورة",
  },
  formValidation: {
    compoundNameRequired: "اسم المشروع مطلوب",
    cityRequired: "المدينة مطلوبة",
    countryRequired: "الدولة مطلوبة",
    districtRequired: "المنطقة مطلوبة",
    areaPositive: "يجب أن تكون المساحة رقمًا موجبًا",
  },
  toasts: {
    imageRemoved: "تمت إزالة الصورة بنجاح من الخادم!",
    imageRemoveFailed: "فشل في إزالة الصورة من الخادم. يرجى المحاولة مرة أخرى.",
    selectImage: "يرجى اختيار صورة للتحميل.",
    imageUploaded: "تم تحميل الصورة بنجاح!",
    imageUploadFailed: "فشل في تحميل الصورة. يرجى المحاولة مرة أخرى.",
    compoundAdded: "تمت إضافة المشروع بنجاح!",
    compoundAddFailed: "فشل في إضافة المشروع. يرجى المحاولة مرة أخرى.",
    enterValidPrice: "يرجى إدخال سعر أكبر من 0 لنوع مدة واحد على الأقل",
    uploadImage: "يرجى تحميل صورة واحدة على الأقل.",
    selectPurpose: "يرجى اختيار غرض للوحدة (بيع/إيجار)",
    errorProcessing: "حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى",
  },
  phase: "المرحلة",
  viewOnGoogleMaps: "عرض على خرائط جوجل",
  clickToViewFullscreen: "انقر لعرض الصورة بحجم كامل",
  watchVideo: "مشاهدة الفيديو",
  shareUnitContent: "مشاركة محتوى الوحدة",
  postContent: "مشاركة محتوى المنشور",
  clickCopy: "انقر على زر 'نسخ مع الرابط' لنسخ النص والصور ورابط المشاركة",
  noImagesAvailable: "لا توجد صور متاحة للمشاركة",
  copyWithLink: "نسخ مع الرابط",
  copied: "تم النسخ",
  addPhaseSuccess: "تم إضافة المرحلة بنجاح",
  addPhaseFailed: "فشل إضافة المرحلة. يرجى المحاولة مرة أخرى.",
  loadingShareData: "جاري تحميل بيانات المشاركة...",
  images: "صور",
  formLabels: {
    compoundName: "اسم المشروع",
    description: "الوصف",
    city: "المدينة",
    selectCity: "اختر المدينة",
    country: "الدولة",
    district: "المنطقة",
    selectDistrict: "اختر المنطقة",
    cityFirst: "اختر المدينة أولاً",
    area: "المساحة (م²)",
    gatedCommunity: "مجتمع مغلق",
    developer: "المطور",
    selectDeveloper: "اختر المطور",
    videoURL: "رابط الفيديو",
    googleMapsLink: "رابط خرائط جوجل",
    masterPlanImage: "صورة المخطط الرئيسي",
    selectedImage: "الصورة المختارة",
    dragDropImage: "انقر أو اسحب وأفلت صورة هنا",
    supportedFormats:
      "الصيغ المدعومة: JPG، PNG، WEBP (الحد الأقصى 5 ميجابايت لكل منها)",
    unitTitle: "عنوان الوحدة",
    buildingType: "نوع المبنى",
    purpose: "الغرض",
    compound: "المجمع",
    view: "الإطلالة",
    isGated: "مجتمع مغلق",
    deliveryDate: "تاريخ التسليم",
    deliveryStatus: "حالة التسليم",
    bathroomCount: "الحمامات",
    floor: "الطابق",
    roomsCount: "الغرف",
    landArea: "مساحة الأرض (م²)",
    gardenSize: "حجم الحديقة (م²)",
    finishing: "التشطيب",
    garageArea: "مساحة الجراج (م²)",
    images: "الصور",
    districtFirst: "اختر المنطقة أولاً",
    selectDistrict: "اختر المنطقة",
  },
  buttons: {
    addNew: "إضافة جديد",
    cancel: "إلغاء",
    uploading: "جاري التحميل...",
    uploadImage: "تحميل الصورة",
    saving: "جاري الحفظ...",
    saveProject: "حفظ المشروع",
    tryChat: "جرب روبوت الدردشة",
    requestDemo: "طلب عرض توضيحي",
    back: "رجوع",
    next: "التالي",
    saveUnit: "حفظ الوحدة",
  },
  modal: {
    addNewProject: "إضافة مشروع جديد",
    addNewUnit: "إضافة وحدة جديدة",
    editUnit: "تعديل الوحدة",
  },
  team: {
    addNew: "إضافة جديد",
    noMembers: "لم يتم إضافة موظفين.",
    name: "الاسم",
    email: "البريد الإلكتروني",
    phone: "الهاتف",
    role: "الدور",
    save: "حفظ",
    loading: "جاري الحفظ...",
  },
  compoundNames: {
    بريفادو: "بريفادو",

    "the address east": " ذا أدرس إيست",

    "new giza": "نيو جيزة",

    Sarai: "سراي",

    Wesal: "وصال",

    "Palm Hills New Cairo": " بالم هيلز القاهرة الجديدة",

    "New Administrative Capital": " العاصمة الإدارية الجديدة",

    "lake view residence": "منزل البحر",
    "the arabesque compound": "العربسك المجمع",
    "ALDAU Development": "الدواو للتطوير",
    "New Giza": "الجديدة الجيزة",
    "El Borouj": "البروج",
    "El Rehab": "الرحاب",
    "Hassan Allam": "حسن علام",
    Madinaty: "مدينتي",
    Mivida: "ميفيدا",
    "Solare North Coast": "سولاري الشمالي",
    "Swan Lake": "البحر الوردي",
    "Terrace Smouha": "تريس سموحة",
    cairohouse: "المنزل القاهري",
    "jayd residence": "جايد ريسيدنس",
    "marina marassi": "مارينا مراسي",
    "mountain view ras el hekma": "مونتاين فيو راس الحكمة",
  },
  developerNames: {
    " La Vista Developments": "لا فيستا للتطوير",
    "Tatweer Misr": "تطوير مصر",
    "Madinet Masr": "مدينة مصر",
    "Government-backed initiative": "مبادرة حكومية",
    "Emaar Misr": "إعمار مصر",
    " شركة سوديك (SODIC)": "شركة سوديك (SODIC)",
    " La Vista Developments": "لا فيستا للتطوير",
    "Misr Italia": "مصر إيطاليا",
    Founders: "فاوندرز",
    "Saudi Egyptian Developers - SED": "الشركة السعودية المصرية للتعمير (SED)",
    "Capital Group Properties": "كابيتال جروب بروبرتيز",
    "New Giza Developments": "نيو جيزة للتطوير",
    "Dorra Developments": "درة للتطوير",
    "El Hazek Group": "مجموعة الحاذق",
    "Arabia Holding": "أرابيا هولدينج",
    TMG: "تي إم جي",
    "شركة سوديك (SODIC)": "شركة سوديك (SODIC)",
    "Madinat Masr": "مدينة مصر",
    "Mountain View Developments": "ماونتن فيو للتطوير",
    "ALDAU Development": "الدواو للتطوير",
    "G Developments": "جي للتطوير",
    "Hassan Allam": "حسن علام",
    "OHK Consultants": "أو إتش كيه للاستشارات",
    "Palm Hills": "بالم هيلز",
  },
  currency: {
    egp: "جنيه",
  },
  schaduall: {
    "assignError": "فشل في تعيين مندوب المبيعات",
"salesAssigned": "تم تعيين مندوب المبيعات بنجاح",
    "ChooseSalesperson":"اختر السيلز الخاص لهذه المهمه",
    "Available":"متاح",
   "noappointments": "لا توجد مواعيد هذا الأسبوع",
"noSale": "لا توجد مبيعات متاحة",
"scheduleweek": "جدولك خالٍ هذا الأسبوع.",
"Schedule":"مهام الاسبوع",
"NoSalesAvailable":"لا يوجد موظفين مبيعات"
  },

"noappointments": "لا توجد مواعيد هذا الأسبوع",
"noSale": "لا توجد مبيعات متاحة",
"scheduleweek": "جدولك خالٍ هذا الأسبوع.",
"Schedule":"مهام الاسبوع",
"previous":"السابق",
"next":"التالي",
"AllSales": "جميع المبيعات",
"Noappointments": "لا توجد مواعيد",
  "scheduleweek": "لا توجد مواعيد مجدولة لهذا الأسبوع",
  "Unassigned": "غير مُعيَّن",
  "no":"لا توجد مواعيد مجدولة لهذا الأسبوع",
  unit_pricing: {
    down_payment: "دفعة أولى",
    years: "سنوات",
    maintenance: "صيانة",
    duration: {
      daily: "يومي",
      weekly: "أسبوعي",
      monthly: "شهري",
    },
    per_duration: {
      daily: "للليلة",
      weekly: "للأسبوع",
      monthly: "لشهر",
    },
    security_deposit: "التأمين",
    cleaning_fee: "رسوم تنظيف",
    service_fee: "رسوم خدمة",
    available_now: "متاح الآن",
    from: "من",
    currently_unavailable: "غير متاح حالياً",
  },
  common: {
    na: "غير متوفر",
  },

  phasee:{
   updatePhasesuccess:"تم نعديل المارحله بنجاح",
   updatePhaseFaile :"فشل في تعديل المرحله",
   addnew:"إضافة مرحله جديده",
   delete:"تم حذف المرحله بنجاح"
   
  },
  ourResultsInNumbers: {
    title: "نتائجنا في الأرقام",
    fasterLeadResponse: "  هيرد علي اي عميل في 30 ثانيه",
    fasterLeadResponseValue: "3X",
    moreConversion: " محادثات اكثر",
    moreConversionValue: "40%",
    engagement: "بيبيع",
    engagementValue: "24/7",
    lessManualWork: "  مجهود اقل ",
    lessManualWorkValue: "80%",
    increaseInSales: "زيادة في المبيعات",
    increaseInSalesValue: "26%",
    increaseInCSAT: "زيادة في رضا  العملاء",
    increaseInCSATValue: "60%",
  },
};
