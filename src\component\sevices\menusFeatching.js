"use server"
import axios from "axios"

const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'

// Categories API
export async function fetchCategories() {
    try {
        const res = await axios.get(`${baseURL}/menu/categories`)
        console.log('Categories:', res.data)
        return res.data
    } catch (error) {
        console.error('Error fetching categories:', error)
        throw error
    }
}

// Menu Items API
export async function fetchMenuItems() {
    try {
        const res = await axios.get(`${baseURL}/menu/items`)
        console.log('Menu Items:', res.data)
        return res.data
    } catch (error) {
        console.error('Error fetching menu items:', error)
        throw error
    }
}