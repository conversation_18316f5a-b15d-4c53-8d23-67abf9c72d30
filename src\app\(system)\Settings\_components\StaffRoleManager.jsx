"use client"
import React, { useState } from 'react';
import { Plus, Minus, Edit2, Trash2, Users, Shield } from 'lucide-react';

const StaffRoleManager = () => {
  const [permissions, setPermissions] = useState([
    { id: 1, name: 'Create Menu', branchHead: false, waiter: true, chef: true },
    { id: 2, name: 'Show Menu', branchHead: false, waiter: true, chef: true },
    { id: 3, name: 'Update Menu', branchHead: false, waiter: true, chef: true },
    { id: 4, name: 'Delete Menu', branchHead: false, waiter: true, chef: true },
    { id: 5, name: 'Create Menu Item', branchHead: false, waiter: true, chef: true },
    { id: 6, name: 'Show Menu Item', branchHead: false, waiter: true, chef: true },
    { id: 7, name: 'Update Menu Item', branchHead: false, waiter: true, chef: true },
    { id: 8, name: 'Delete Menu Item', branchHead: false, waiter: true, chef: true },
  ]);

  const [newPermission, setNewPermission] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');

  const togglePermission = (id, role) => {
    setPermissions(permissions.map(perm => 
      perm.id === id ? { ...perm, [role]: !perm[role] } : perm
    ));
  };

  const addPermission = () => {
    if (newPermission.trim()) {
      const newPerm = {
        id: Date.now(),
        name: newPermission,
        branchHead: false,
        waiter: false,
        chef: false
      };
      setPermissions([...permissions, newPerm]);
      setNewPermission('');
    }
  };

  const deletePermission = (id) => {
    setPermissions(permissions.filter(perm => perm.id !== id));
  };

  const getRoleIcon = (role) => {
    switch(role) {
      case 'branchHead': return <Shield className="w-4 h-4" />;
      case 'waiter': return <Users className="w-4 h-4" />;
      case 'chef': return <Edit2 className="w-4 h-4" />;
      default: return null;
    }
  };

  const getRoleColor = (role) => {
    switch(role) {
      case 'branchHead': return 'bg-red-100 text-red-800';
      case 'waiter': return 'bg-blue-100 text-blue-800';
      case 'chef': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredPermissions = selectedRole === 'all' 
    ? permissions 
    : permissions.filter(perm => perm[selectedRole]);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm mb-6 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Staff Role Management</h1>
              <p className="text-gray-600 mt-1">Manage permissions for different staff roles</p>
            </div>
            <div className="flex space-x-3">
              <select 
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="all">All Permissions</option>
                <option value="branchHead">Branch Head</option>
                <option value="waiter">Waiter</option>
                <option value="chef">Chef</option>
              </select>
            </div>
          </div>
        </div>

        {/* Add New Permission */}
        <div className="bg-white rounded-lg shadow-sm mb-6 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Add New Permission</h2>
          <div className="flex space-x-3">
            <input
              type="text"
              placeholder="Enter permission name..."
              value={newPermission}
              onChange={(e) => setNewPermission(e.target.value)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              onKeyPress={(e) => e.key === 'Enter' && addPermission()}
            />
            <button
              onClick={addPermission}
              className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2"
              style={{ backgroundColor: '#FF6500' }}
            >
              <Plus className="w-4 h-4" />
              <span>Add Permission</span>
            </button>
          </div>
        </div>

        {/* Permissions Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              {selectedRole === 'all' ? 'All Permissions' : `${selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)} Permissions`}
            </h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Permission
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center justify-center space-x-2">
                      <Shield className="w-4 h-4" />
                      <span>Branch Head</span>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center justify-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>Waiter</span>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center justify-center space-x-2">
                      <Edit2 className="w-4 h-4" />
                      <span>Chef</span>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredPermissions.map((permission) => (
                  <tr key={permission.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {permission.name}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => togglePermission(permission.id, 'branchHead')}
                        className={`inline-flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200 ${
                          permission.branchHead 
                            ? 'bg-orange-500 text-white shadow-lg' 
                            : 'bg-red-100 text-red-500 hover:bg-red-200'
                        }`}
                        style={permission.branchHead ? { backgroundColor: '#FF6500' } : {}}
                      >
                        {permission.branchHead ? <Plus className="w-4 h-4" /> : <Minus className="w-4 h-4" />}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => togglePermission(permission.id, 'waiter')}
                        className={`inline-flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200 ${
                          permission.waiter 
                            ? 'bg-orange-500 text-white shadow-lg' 
                            : 'bg-red-100 text-red-500 hover:bg-red-200'
                        }`}
                        style={permission.waiter ? { backgroundColor: '#FF6500' } : {}}
                      >
                        {permission.waiter ? <Plus className="w-4 h-4" /> : <Minus className="w-4 h-4" />}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => togglePermission(permission.id, 'chef')}
                        className={`inline-flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200 ${
                          permission.chef 
                            ? 'bg-orange-500 text-white shadow-lg' 
                            : 'bg-red-100 text-red-500 hover:bg-red-200'
                        }`}
                        style={permission.chef ? { backgroundColor: '#FF6500' } : {}}
                      >
                        {permission.chef ? <Plus className="w-4 h-4" /> : <Minus className="w-4 h-4" />}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => deletePermission(permission.id)}
                        className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-100 text-red-500 hover:bg-red-200 transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          {['branchHead', 'waiter', 'chef'].map((role) => {
            const rolePermissions = permissions.filter(p => p[role]);
            const roleName = role === 'branchHead' ? 'Branch Head' : role.charAt(0).toUpperCase() + role.slice(1);
            
            return (
              <div key={role} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${getRoleColor(role)}`}>
                      {getRoleIcon(role)}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{roleName}</h3>
                  </div>
                  <span className="text-2xl font-bold text-orange-500" style={{ color: '#FF6500' }}>
                    {rolePermissions.length}
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {rolePermissions.length} permissions assigned
                </p>
                <div className="mt-3 flex flex-wrap gap-1">
                  {rolePermissions.slice(0, 3).map((perm) => (
                    <span key={perm.id} className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                      {perm.name}
                    </span>
                  ))}
                  {rolePermissions.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      +{rolePermissions.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default StaffRoleManager;