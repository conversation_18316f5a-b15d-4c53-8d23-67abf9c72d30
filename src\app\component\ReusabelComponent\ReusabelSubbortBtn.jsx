"use client"
import { MessageCircle } from 'lucide-react'
import React from 'react'

const ReusabelSubbortBtn = ({ phone, text = "تحدث مع فريق الدعم", icon: Icon = MessageCircle ,background}) => {
  
  const handleWhatsAppClick = () => {
    // Remove any non-numeric characters from phone number
    const cleanPhone = phone.replace(/\D/g, '');
    
    // Default message if no text provided
    const message = text || "مرحباً، أحتاج مساعدة";
    
    // Encode the message for URL
    const encodedMessage = encodeURIComponent(message);
    
    // WhatsApp URL - works on both mobile and desktop
    const whatsappURL = `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
    
    // Open WhatsApp in new tab/window
    window.open(whatsappURL, '_blank');
  };

  return (
    <div>
      <button 
        onClick={handleWhatsAppClick}
        className={` cursor-pointer ${background ? "group relative px-8 py-4 bg-[#FF6500] rounded-full font-bold text-lg text-white hover:shadow-2xl hover:shadow-[#FF6500]/30 transition-all duration-300 hover:scale-105" :"group relative px-8 py-4 bg-white border-2 border-[#FF6500] rounded-full font-bold text-lg text-[#FF6500] hover:bg-[#FF6500] hover:text-white transition-all duration-300 hover:scale-105"}`}
      >
        <span className="relative z-10 flex items-center gap-2">
          <Icon className="w-5 h-5" />
          {text}
        </span>
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#FCB190] to-[#FFD5C7] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </button>
    </div>
  )
}

export default ReusabelSubbortBtn

// Usage Examples:
// <ReusableWhatsAppBtn phone="966501234567" />
// <ReusableWhatsAppBtn phone="+966 50 123 4567" text="تواصل معنا الآن" />
// <ReusableWhatsAppBtn phone="966501234567" text="احصل على الدعم" icon={Phone} />