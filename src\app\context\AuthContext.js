// "use client"
// import React, { createContext, useContext, useState, useEffect } from 'react'
// import { getUserData, getAuthToken, logoutAction } from '../(auth)/login/_action/action' // Adjust path

// const AuthContext = createContext(null)

// export const useAuth = () => {
//     const context = useContext(AuthContext)
//     if (!context) {
//         throw new Error('useAuth must be used within an AuthProvider')
//     }
//     return context
// }

// export const AuthProvider = ({ children }) => {
//     const [user, setUser] = useState(null)
//     const [token, setToken] = useState(null)
//     const [loading, setLoading] = useState(true)
//     const [isAuthenticated, setIsAuthenticated] = useState(false)

//     // Check authentication status on mount
//     useEffect(() => {
//         checkAuthStatus()
//     }, [])

//     const checkAuthStatus = async () => {
//         try {
//             setLoading(true)
//             const [userData, authToken] = await Promise.all([
//                 getUserData(),
//                 getAuthToken()
//             ])

//             if (userData && authToken) {
//                 setUser(userData)
//                 setToken(authToken)
//                 setIsAuthenticated(true)
//             } else {
//                 setUser(null)
//                 setToken(null)
//                 setIsAuthenticated(false)
//             }
//         } catch (error) {
//             console.error('Error checking auth status:', error)
//             setUser(null)
//             setToken(null)
//             setIsAuthenticated(false)
//         } finally {
//             setLoading(false)
//         }
//     }

//     const login = (userData, authToken) => {
//         setUser(userData)
//         setToken(authToken)
//         setIsAuthenticated(true)
//     }

//     const logout = async () => {
//         try {
//             await logoutAction()
//             setUser(null)
//             setToken(null)
//             setIsAuthenticated(false)
            
//             // Redirect to login page
//             window.location.href = '/login'
//         } catch (error) {
//             console.error('Logout error:', error)
//         }
//     }

//     const value = {
//         user,
//         token,
//         loading,
//         isAuthenticated,
//         login,
//         logout,
//         checkAuthStatus
//     }

//     return (
//         <AuthContext.Provider value={value}>
//             {children}
//         </AuthContext.Provider>
//     )
// }
"use client"
import React, { createContext, useContext, useState, useEffect } from 'react'
import { logoutFromServer } from '../(auth)/login/_action/action' // Adjust path
import { 
    storeAuthData, 
    getAuthToken, 
    getUserData, 
    clearAuthData, 
    isAuthenticated as checkIsAuthenticated 
} from '../utils/auth' // Adjust path

const AuthContext = createContext(null)

export const useAuth = () => {
    const context = useContext(AuthContext)
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider')
    }
    return context
}

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null)
    const [token, setToken] = useState(null)
    const [loading, setLoading] = useState(true)
    const [isAuthenticated, setIsAuthenticated] = useState(false)

    // Check authentication status on mount
    useEffect(() => {
        checkAuthStatus()
    }, [])

    const checkAuthStatus = () => {
        try {
            setLoading(true)
            const userData = getUserData()
            const authToken = getAuthToken()

            if (userData && authToken) {
                setUser(userData)
                setToken(authToken)
                setIsAuthenticated(true)
            } else {
                setUser(null)
                setToken(null)
                setIsAuthenticated(false)
            }
        } catch (error) {
            console.error('Error checking auth status:', error)
            setUser(null)
            setToken(null)
            setIsAuthenticated(false)
        } finally {
            setLoading(false)
        }
    }

    const login = (userData, authToken) => {
        // Store in localStorage
        storeAuthData(userData, authToken)
        
        // Update state
        setUser(userData)
        setToken(authToken)
        setIsAuthenticated(true)
    }

    const logout = async () => {
        try {
            // Call server logout API
            const currentToken = getAuthToken()
            if (currentToken) {
                await logoutFromServer(currentToken)
            }
        } catch (error) {
            console.error('Server logout error:', error)
            // Continue with local logout even if server call fails
        } finally {
            // Clear local storage and state
            clearAuthData()
            setUser(null)
            setToken(null)
            setIsAuthenticated(false)
            
            // Redirect to login page
            if (typeof window !== 'undefined') {
                window.location.href = '/login'
            }
        }
    }

    const value = {
        user,
        token,
        loading,
        isAuthenticated,
        login,
        logout,
        checkAuthStatus
    }

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    )
}