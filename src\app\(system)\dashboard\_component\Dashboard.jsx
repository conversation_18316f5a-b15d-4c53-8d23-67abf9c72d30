"use client";
import React, { useState, useEffect } from "react";

import StatCard from "./StatCard";
import OrderDashboard from "./OrderDashboard";
import TopValues from "./TopValues";
import Stats from "./Stats";
import HeaderTime from "./HeaderTime";
import Statistics from "./Statistics";
import { useI18n } from "@/context/translate-api";


const Dashboard = () => {
  const {t} = useI18n()

  return (
    <div className="space-y-6 ">
      {/* Header */}
      <HeaderTime />
      <p>Auto Reply</p>
      {/* <p>{t.navigation.home }</p> */}
      {/* Stats Cards */}

      <Stats />
      {/* Charts Section */}

      <Statistics />

      {/* Tables Section */}
      <TopValues />

      {/* Orders of the Day Section */}
      <OrderDashboard />
    </div>
  );
};

export default Dashboard;
