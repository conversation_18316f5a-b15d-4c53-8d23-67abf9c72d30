"use client"
import React, { useState, useRef } from 'react';
import { Delete, Edit, Trash2, Plus, Cookie, Upload, GripVertical, Save } from 'lucide-react';

// Sample data for demonstration
const sampleData = [
  {
    id: 1,
    title: "Cappuccino Coffee",
    category: "HOT DRINK",
    price: 15.99,
    image: "https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=100&h=100&fit=crop",
    rating: { count: 10 },
    order: 1
  },
  {
    id: 2,
    title: "Chocolate Cake",
    category: "DESERTS",
    price: 12.50,
    image: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=100&h=100&fit=crop",
    rating: { count: 0 },
    order: 2
  },
  {
    id: 3,
    title: "Orange Juice",
    category: "FRESH JUICE",
    price: 8.99,
    image: "https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=100&h=100&fit=crop",
    rating: { count: 5 },
    order: 3
  }
];

const initialCategories = [
  { id: 1, name: "HOT DRINK", order: 1 },
  { id: 2, name: "SOFT DRINKS", order: 2 },
  { id: 3, name: "DESERTS", order: 3 },
  { id: 4, name: "FRESH JUICE", order: 4 },
  { id: 5, name: "bakery", order: 5 }
];

const ArrangeMenue = () => {
  const [menuItems, setMenuItems] = useState(sampleData);
  const [categories, setCategories] = useState(initialCategories);
  const [draggedItem, setDraggedItem] = useState(null);
  const [draggedCategory, setDraggedCategory] = useState(null);
  const [dragOverIndex, setDragOverIndex] = useState(null);
  const [dragOverCategoryIndex, setDragOverCategoryIndex] = useState(null);
  const [filters, setFilters] = useState({
    category: "all",
    availability: "all",
    subCategory: "all"
  });
  const [isDragging, setIsDragging] = useState(false);
  const [isDraggingCategory, setIsDraggingCategory] = useState(false);

  const filteredItems = menuItems.filter(item => {
    if (filters.category !== "all" && item.category !== filters.category) return false;
    if (filters.availability === "available" && item.rating?.count <= 0) return false;
    if (filters.availability === "unavailable" && item.rating?.count > 0) return false;
    return true;
  });

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Category drag and drop handlers
  const handleCategoryDragStart = (e, category, index) => {
    setDraggedCategory({ category, index });
    setIsDraggingCategory(true);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleCategoryDragOver = (e, index) => {
    e.preventDefault();
    setDragOverCategoryIndex(index);
  };

  const handleCategoryDragLeave = () => {
    setDragOverCategoryIndex(null);
  };

  const handleCategoryDrop = (e, dropIndex) => {
    e.preventDefault();
    
    if (draggedCategory && draggedCategory.index !== dropIndex) {
      const newCategories = [...categories];
      const draggedCategoryData = newCategories[draggedCategory.index];
      
      // Remove the dragged category
      newCategories.splice(draggedCategory.index, 1);
      
      // Insert at new position
      newCategories.splice(dropIndex, 0, draggedCategoryData);
      
      // Update order values
      const updatedCategories = newCategories.map((cat, index) => ({
        ...cat,
        order: index + 1
      }));
      
      setCategories(updatedCategories);
    }
    
    setDraggedCategory(null);
    setDragOverCategoryIndex(null);
    setIsDraggingCategory(false);
  };

  const handleCategoryDragEnd = () => {
    setDraggedCategory(null);
    setDragOverCategoryIndex(null);
    setIsDraggingCategory(false);
  };

  // Menu items drag and drop handlers

  const handleDragStart = (e, item, index) => {
    setDraggedItem({ item, index });
    setIsDragging(true);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e, index) => {
    e.preventDefault();
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    
    if (draggedItem && draggedItem.index !== dropIndex) {
      const newItems = [...menuItems];
      const draggedItemData = newItems[draggedItem.index];
      
      // Remove the dragged item
      newItems.splice(draggedItem.index, 1);
      
      // Insert at new position
      newItems.splice(dropIndex, 0, draggedItemData);
      
      // Update order values
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        order: index + 1
      }));
      
      setMenuItems(updatedItems);
    }
    
    setDraggedItem(null);
    setDragOverIndex(null);
    setIsDragging(false);
  };

  const handleDragEnd = () => {
    setDraggedItem(null);
    setDragOverIndex(null);
    setIsDragging(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Filter Section */}
      <div className="w-full max-w-6xl mx-auto mb-6">
        {/* Header for Categories */}
        <div className="mb-4 flex items-center justify-between ">
          <div>          <h2 className="text-xl font-bold text-gray-800 mb-2">ترتيب الفئات</h2>
          <p className="text-gray-500">اسحب وأفلت الفئات لإعادة ترتيبها</p>
          </div>
             <button className="flex items-center gap-2 px-4 py-2 rounded-lg bg-[#FF6500] text-white font-semibold hover:bg-[#ff7f32] focus:ring-2 focus:ring-[#FF6500] transition-all duration-200 ml-2">
              <Save className="w-4 h-4" />
              حفظ ترتيب الفئات
            </button>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-3 mb-6">
          {/* All Categories */}
          <div
            onClick={() => handleFilterChange("category", "all")}
            className={`relative cursor-pointer rounded-xl p-4 transition-all duration-300 border-2 min-h-[120px] flex flex-col justify-center bg-white hover:border-[#FCB190] hover:shadow-md hover:scale-105 
              ${filters.category === "all" ? "border-[#FF6500] bg-gradient-to-br from-orange-50 to-amber-50 shadow-lg scale-105" : "border-gray-200"}`}
          >
            <div className="flex items-center justify-center mb-3">
              <div className="p-2 rounded-lg transition-all duration-300 bg-gradient-to-r from-[#FF6500] to-[#FCB190]">
                <Cookie className="w-5 h-5 transition-colors duration-300 text-white" />
              </div>
            </div>
            <h3 className="font-semibold text-sm text-center mb-2 leading-tight text-gray-800">
              جميع الفئات
            </h3>
          </div>

          {/* Add New Category */}
          <div className="relative">
            <div className="cursor-pointer rounded-xl p-4 transition-all duration-300 border-2 border-dashed border-gray-300 hover:border-[#FCB190] hover:bg-gradient-to-br hover:from-orange-50 hover:to-amber-50 bg-gray-50 hover:shadow-md hover:scale-105 group h-full flex flex-col justify-center items-center min-h-[120px]">
              <div className="flex items-center justify-center mb-3">
                <div className="p-2 rounded-lg transition-all duration-300 bg-gradient-to-r from-gray-400 to-gray-500 group-hover:from-[#FF6500] group-hover:to-[#FCB190]">
                  <Plus className="w-5 h-5 transition-colors duration-300 text-white" />
                </div>
              </div>
              <h3 className="font-semibold text-xs text-center leading-tight text-gray-600 group-hover:text-gray-800">
                إضافة فئة جديدة
              </h3>
            </div>
          </div>

          {/* Existing Categories - Draggable */}
          {categories.map((category, idx) => (
            <div
              key={category.id}
              draggable
              onDragStart={(e) => handleCategoryDragStart(e, category, idx)}
              onDragOver={(e) => handleCategoryDragOver(e, idx)}
              onDragLeave={handleCategoryDragLeave}
              onDrop={(e) => handleCategoryDrop(e, idx)}
              onDragEnd={handleCategoryDragEnd}
              onClick={() => handleFilterChange("category", category.name)}
              className={`relative cursor-move rounded-xl p-4 transition-all duration-300 border-2 min-h-[120px] flex flex-col justify-center bg-white hover:border-[#FCB190] hover:shadow-md hover:scale-105 
                ${filters.category === category.name ? "border-[#FF6500] bg-gradient-to-br from-orange-50 to-amber-50 shadow-lg scale-105" : "border-gray-200"}
                ${dragOverCategoryIndex === idx ? 'border-[#FF6500] bg-gradient-to-r from-orange-50 to-amber-50 transform scale-102' : ''}
                ${isDraggingCategory ? 'hover:border-[#FCB190]' : ''}
              `}
            >
              {/* Drag Handle for Categories */}
              <div className="absolute top-2 right-2 text-gray-400 hover:text-[#FF6500] transition-colors duration-200">
                <GripVertical className="w-4 h-4" />
              </div>
              
              {/* Order Number for Categories */}
              <div className="absolute top-2 left-2 w-6 h-6 bg-gradient-to-r from-[#FF6500] to-[#FCB190] rounded-full flex items-center justify-center text-white font-bold text-xs">
                {idx + 1}
              </div>

              <div className="flex items-center justify-center mb-3">
                <div className="p-2 rounded-lg transition-all duration-300 bg-gradient-to-r from-[#FF6500] to-[#FCB190]">
                  <Cookie className="w-5 h-5 transition-colors duration-300 text-white" />
                </div>
              </div>
              <h3 className="font-semibold text-sm text-center mb-2 leading-tight text-gray-800">
                {category.name.toUpperCase()}
              </h3>
            </div>
          ))}
        </div>

        {/* Filter Controls */}
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <select
              className="px-4 py-2 rounded-lg border border-gray-300 focus:border-[#FF6500] focus:ring-2 focus:ring-[#FF6500] bg-white text-gray-800 font-medium shadow-sm transition-all duration-200"
              value={filters.availability}
              onChange={(e) => handleFilterChange("availability", e.target.value)}
            >
              <option value="all">الكل</option>
              <option value="available">متاح</option>
              <option value="unavailable">غير متاح</option>
            </select>
          </div>
          <div>
         
            <button className="flex items-center gap-2 px-4 py-2 rounded-lg bg-green-600 text-white font-semibold hover:bg-green-700 focus:ring-2 focus:ring-green-500 transition-all duration-200">
              <Save className="w-4 h-4" />
              حفظ ترتيب العناصر
            </button>
          </div>
        </div>
      </div>

      {/* Menu Items Grid */}
      <div className="w-full max-w-7xl mx-auto">
        <div className="bg-white rounded-2xl shadow-lg p-6">
          {/* Header */}
          <div className="mb-6 flex items-center justify-between px-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-800">ترتيب عناصر القائمة</h2>
              <p className="text-gray-500">اسحب وأفلت لترتيب العناصر</p>
            </div>
            <div className="flex items-center gap-2">
              <p className="mx-2 font-semibold text-[#FF6500] uppercase">
                {filters.category === "all" ? "جميع الفئات" : filters.category}
              </p>
            </div>
          </div>

          {/* Draggable Items List */}
          <div className="space-y-4">
            {filteredItems.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                <p className="text-lg font-medium">لا توجد عناصر متاحة</p>
              </div>
            ) : (
              filteredItems.map((item, index) => (
                <div
                  key={item.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, item, index)}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, index)}
                  onDragEnd={handleDragEnd}
                  className={`flex items-center gap-4 p-4 bg-white border-2 rounded-xl transition-all duration-300 cursor-move hover:shadow-md
                    ${dragOverIndex === index ? 'border-[#FF6500] bg-gradient-to-r from-orange-50 to-amber-50 transform scale-102' : 'border-gray-200'}
                    ${isDragging ? 'hover:border-[#FCB190]' : ''}
                  `}
                >
                  {/* Drag Handle */}
                  <div className="flex-shrink-0 text-gray-400 hover:text-[#FF6500] transition-colors duration-200">
                    <GripVertical className="w-5 h-5" />
                  </div>

                  {/* Order Number */}
                  <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-[#FF6500] to-[#FCB190] rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>

                  {/* Image */}
                  <div className="flex-shrink-0">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-16 h-16 object-cover rounded-lg shadow-md"
                      onError={(e) => {
                        e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMS4zMzMzIDQyLjY2NjdMMzIgMzJMMjEuMzMzMyAyMS4zMzMzTDE4LjY2NjcgMjRMMjYuNjY2NyAzMkwxOC42NjY3IDQwTDIxLjMzMzMgNDIuNjY2N1oiIGZpbGw9IiM5Q0E0QUYiLz4KPC9zdmc+';
                      }}
                    />
                  </div>

                  {/* Item Details */}
                  <div className="flex-grow">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-gray-800 text-lg">
                          {item.title}
                        </h3>
                        <div className="flex items-center gap-3 mt-2">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-[#FF6500]/10 to-[#FCB190]/10 text-[#FF6500] border border-[#FF6500]/20">
                            {item.category}
                          </span>
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                            item.rating?.count > 0 
                              ? 'bg-green-100 text-green-800 border border-green-200' 
                              : 'bg-red-100 text-red-800 border border-red-200'
                          }`}>
                            {item.rating?.count > 0 ? 'متاح' : 'غير متاح'}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-[#FF6500] text-xl">
                          ${item.price?.toFixed(2)}
                        </div>
                        <div className="flex items-center gap-2 mt-2">
                          <button className="p-2 text-gray-400 hover:text-[#FF6500] hover:bg-[#FF6500]/10 rounded-lg transition-all duration-200">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-all duration-200">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Instructions */}
          {filteredItems.length > 0 && (
            <div className="mt-6 p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg border border-[#FF6500]/20">
              <p className="text-sm text-gray-600 text-center mb-2">
                💡 <strong>تلميح للعناصر:</strong> اسحب العناصر بالضغط على أيقونة الشبكة لإعادة ترتيبها
              </p>
              <p className="text-sm text-gray-600 text-center">
                🔄 <strong>تلميح للفئات:</strong> اسحب الفئات من أي مكان لإعادة ترتيبها (ستجد رقم الترتيب وأيقونة السحب)
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ArrangeMenue;