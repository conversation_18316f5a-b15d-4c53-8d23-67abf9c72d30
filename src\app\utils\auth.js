// utils/auth.js - Client-side utilities for localStorage
"use client"

// Store auth data in localStorage
export const storeAuthData = (user, token) => {
    if (typeof window !== 'undefined') {
        localStorage.setItem('auth_token', token)
        localStorage.setItem('user_data', JSON.stringify(user))
    }
}

// Get token from localStorage
export const getAuthToken = () => {
    if (typeof window !== 'undefined') {
        return localStorage.getItem('auth_token')
    }
    return null
}

// Get user data from localStorage
export const getUserData = () => {
    if (typeof window !== 'undefined') {
        const userData = localStorage.getItem('user_data')
        return userData ? JSON.parse(userData) : null
    }
    return null
}

// Clear auth data from localStorage
export const clearAuthData = () => {
    if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token')
        localStorage.removeItem('user_data')
    }
}

// Check if user is authenticated
export const isAuthenticated = () => {
    const token = getAuthToken()
    const user = getUserData()
    return !!(token && user)
}