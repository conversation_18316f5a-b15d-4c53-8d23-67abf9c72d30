"use client"
import React, { useState } from 'react'
import PersonalRegiter from './PersonalRegiter'
import ResturantInfo from './ResturantInfo'

const SignUpForm = () => {
  const [step, setStep] = useState(1)

  const renderStep = () => {
    switch (step) {
      case 1:
        return <PersonalRegiter setStep={setStep} />
      case 2:
        return <ResturantInfo setStep={setStep} />
      default:
        return <div>انتهى التسجيل</div>
    }
  }

  return (
    <div className=' relative flex items-center justify-center p-4 bg-gradient-to-br from-[#FF6500] via-[#FCB190] to-[#FFE5D9]'>
      
      {renderStep()}
    </div>
  )
}

export default SignUpForm
