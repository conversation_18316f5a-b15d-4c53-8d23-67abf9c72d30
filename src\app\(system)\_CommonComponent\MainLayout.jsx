// 'use client';

// import { SidebarProvider } from "@/context/SidebarContext";
// import Sidebar from "./Sidebar";
// import Header from "./Header";
// import { useSidebar } from "@/context/SidebarContext";

// const MainLayout = ({ children }) => {
//   return (
//     <SidebarProvider>
//       <LayoutContent>{children}</LayoutContent>
//     </SidebarProvider>
//   );
// };

// const LayoutContent = ({ children }) => {
//   const { isOpen } = useSidebar();

//   return (
//     <div className="flex h-screen overflow-hidden bg-gray-50">
//       <Sidebar />
//       <div
//         className={`flex-1 flex flex-col transition-all duration-300 ${
//           isOpen ? "ml-64" : "ml-0"
//         }`}
//       >
//         <Header />
//         <main className="overflow-y-auto p-3 flex-1">
//           {children}
//         </main>
//       </div>
//     </div>
//   );
// };

// export default MainLayout;
'use client';

import { SidebarProvider } from "@/context/SidebarContext";
import Sidebar from "./Sidebar";
import Header from "./Header";
import { useSidebar } from "@/context/SidebarContext";

const MainLayout = ({ children }) => {
  return (
    <SidebarProvider>
      <LayoutContent>{children}</LayoutContent>
    </SidebarProvider>
  );
};

const LayoutContent = ({ children }) => {
  const { isOpen } = useSidebar();

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header at top */}
      <Header />

      {/* Sidebar + Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar with conditional width */}
        <div
          className={`transition-all duration-300 ${
            isOpen ? 'w-64' : 'w-0'
          } overflow-hidden`}
        >
          <Sidebar />
        </div>

        {/* Main content shrinks accordingly */}
        <main className="flex-1 overflow-y-auto p-3">
          {children}
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
