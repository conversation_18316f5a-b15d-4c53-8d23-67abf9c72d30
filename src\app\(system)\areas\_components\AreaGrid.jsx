"use client";
import React, { useState, useEffect } from "react";
import { Edit2, Trash2 } from "lucide-react";
import { fetchAreas } from "../../../../../lib/api";
import CreateAreaModal from "./CreateAreaModal";
import UpdateAreaModal from "./UpdateAreaModal";
import DeleteAreaModal from "./DeleteAreaModal";

const AreaGrid = () => {
  const [areas, setAreas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedArea, setSelectedArea] = useState(null);

  useEffect(() => {
    loadAreas();
  }, []);

  const loadAreas = async () => {
    try {
      setLoading(true);
      const areasData = await fetchAreas(1); // Using branch_id = 1
      setAreas(areasData);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch areas:', err);
      setError('Failed to load areas. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAreaCreated = (newArea) => {
    setAreas(prev => [...prev, newArea]);
  };

  const handleAreaUpdated = (updatedArea) => {
    setAreas(prev => prev.map(area => 
      area.id === updatedArea.id ? updatedArea : area
    ));
  };

  const handleAreaDeleted = (deletedAreaId) => {
    setAreas(prev => prev.filter(area => area.id !== deletedAreaId));
  };

  const openUpdateModal = (area) => {
    setSelectedArea(area);
    setShowUpdateModal(true);
  };

  const openDeleteModal = (area) => {
    setSelectedArea(area);
    setShowDeleteModal(true);
  };

  return (
    <div className="p-6 bg-gray-50 ">
      <div className="max-w-7xl mx-auto">
        <div className=" flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">
            Area Management
          </h2>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 4v16m8-8H4"
              />
            </svg>
            إضافة مساحه جديده  
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              <span className="ml-2 text-gray-600">Loading areas...</span>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center py-12">
              <div className="text-red-500 text-center">
                <p className="text-lg font-semibold">Error</p>
                <p>{error}</p>
                <button 
                  onClick={() => window.location.reload()} 
                  className="mt-2 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
                >
                  Retry
                </button>
              </div>
            </div>
          ) : (
            <table className="w-full">
              <thead style={{ backgroundColor: "#FF6500" }}>
                <tr>
                  <th className="px-6 py-4 text-left text-white font-semibold">
                    ID
                  </th>
                  <th className="px-6 py-4 text-left text-white font-semibold">
                    Area Name
                  </th>
                  <th className="px-6 py-4 text-left text-white font-semibold">
                    Number of Tables
                  </th>
                  <th className="px-6 py-4 text-left text-white font-semibold">
                    Total Capacity
                  </th>
                  <th className="px-6 py-4 text-center text-white font-semibold">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {areas.length === 0 ? (
                  <tr>
                    <td colSpan="5" className="px-6 py-8 text-center text-gray-500">
                      No areas found
                    </td>
                  </tr>
                ) : (
                  areas.map((area, index) => (
                    <tr
                      key={area.id}
                      className={`border-b border-gray-300 ${
                        index % 2 === 0 ? "bg-gray-50" : "bg-white"
                      } hover:bg-orange-50 transition-colors`}
                    >
                      <td className="px-6 py-4 text-gray-800">{area.id}</td>
                      <td className="px-6 py-4 text-gray-800 capitalize">
                        {area.name}
                      </td>
                      <td className="px-6 py-4 text-gray-800">
                        {area.tables ? area.tables.length : 0}
                      </td>
                      <td className="px-6 py-4 text-gray-800">
                        {area.total_capacity || 0}
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex justify-center space-x-2">
                          <button
                            onClick={() => openUpdateModal(area)}
                            className="p-2 text-white rounded-md hover:opacity-90 transition-opacity"
                            style={{ backgroundColor: "#FF6500" }}
                            title="Edit"
                          >
                            <Edit2 size={16} />
                          </button>
                          <button
                            onClick={() => openDeleteModal(area)}
                            className="p-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                            title="Delete"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* Modals */}
      <CreateAreaModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onAreaCreated={handleAreaCreated}
      />

      <UpdateAreaModal
        isOpen={showUpdateModal}
        onClose={() => setShowUpdateModal(false)}
        area={selectedArea}
        onAreaUpdated={handleAreaUpdated}
      />

      <DeleteAreaModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        area={selectedArea}
        onAreaDeleted={handleAreaDeleted}
      />
    </div>
  );
};

export default AreaGrid;
