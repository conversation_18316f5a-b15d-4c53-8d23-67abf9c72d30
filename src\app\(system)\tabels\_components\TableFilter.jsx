import React from 'react'

const TableFilter = ({ selectedArea, onAreaChange, tables, areas, onAddTable }) => {
  // Create areas list with "All Areas" first, then actual areas
  const areasList = ["All Areas", ...areas];

  return (
    <div className="mb-6">
      <div className="flex gap-2 justify-between">
        <div className="flex gap-2">
        {areasList.map((area) => (
          <button
            key={area}
            onClick={() => onAreaChange(area)}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
              selectedArea === area
                ? "text-white shadow-sm"
                : "text-gray-600 bg-white hover:bg-gray-50 border border-gray-200"
            }`}
            style={selectedArea === area ? { backgroundColor: "#FF6500" } : {}}
          >
            {area === "All Areas" ? "جميع المناطق" : area}
          </button>
        ))}
        </div>
        <button
          onClick={onAddTable}
          className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 4v16m8-8H4"
            />
          </svg>
          إضافة طاولة جديدة
        </button>
      </div>

      {/* Area Info */}
      <div className="mt-4 flex items-center gap-4 text-sm text-gray-600">
        <span className="font-semibold text-gray-800">
          {selectedArea === "All Areas" ? "جميع المناطق" : selectedArea}
        </span>
        <span>
          {selectedArea === "All Areas"
            ? `${tables.length} طاولة`
            : `${tables.filter((t) => t.area?.name === selectedArea).length} طاولة`}
        </span>
        {/* Show total capacity */}
        <span className="text-gray-500">
          •
        </span>
        <span>
          {selectedArea === "All Areas"
            ? `${tables.reduce((sum, t) => sum + (t.seating_capacity || 0), 0)} مقعد إجمالي`
            : `${tables.filter((t) => t.area?.name === selectedArea).reduce((sum, t) => sum + (t.seating_capacity || 0), 0)} مقعد إجمالي`}
        </span>
      </div>
    </div>
  );
};

export default TableFilter