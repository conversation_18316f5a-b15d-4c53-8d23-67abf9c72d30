"use client";
import React, { useState, useEffect } from "react";
import { Edit, Trash2, Plus, Search, Filter, MoreVertical, X, Eye, ChevronRight, Loader2 } from "lucide-react";
import { fetchMenuItems, fetchMenuItemVariants } from '../../../../../lib/api';

const ModifiersGrid = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedItemDetails, setSelectedItemDetails] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentItem, setCurrentItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);

  // Color schemes for items
  const colorSchemes = [
    {
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
      borderColor: "border-purple-200",
    },
    {
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
      borderColor: "border-blue-200",
    },
    {
      color: "from-emerald-500 to-teal-500",
      bgColor: "bg-emerald-50",
      textColor: "text-emerald-700",
      borderColor: "border-emerald-200",
    },
    {
      color: "from-orange-500 to-red-500",
      bgColor: "bg-orange-50",
      textColor: "text-orange-700",
      borderColor: "border-orange-200",
    },
    {
      color: "from-indigo-500 to-purple-500",
      bgColor: "bg-indigo-50",
      textColor: "text-indigo-700",
      borderColor: "border-indigo-200",
    },
    {
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50",
      textColor: "text-green-700",
      borderColor: "border-green-200",
    }
  ];

  // Transform API data to match existing component structure
  const transformApiData = (menuItems) => {
    const transformedData = [];
    
    // Filter only items that have variants
    const itemsWithVariants = menuItems.filter(item => item.variants && item.variants.length > 0);
    
    for (let i = 0; i < itemsWithVariants.length; i++) {
      const item = itemsWithVariants[i];
      const colorScheme = colorSchemes[i % colorSchemes.length];
      
      transformedData.push({
        id: item.id,
        title: item.name,
        description: item.description,
        short_description: item.short_description,
        code: item.code,
        base_price: parseFloat(item.base_price),
        cost_price: parseFloat(item.cost_price),
        image_urls: item.image_urls,
        prep_time_minutes: item.prep_time_minutes,
        calories: item.calories,
        is_active: item.is_active,
        is_featured: item.is_featured,
        is_spicy: item.is_spicy,
        spice_level: item.spice_level,
        sort_order: item.sort_order,
        category_id: item.category_id,
        category: item.category,
        variants: item.variants || [],
        allergens: item.allergens || [],
        dietary_info: item.dietary_info || [],
        ...colorScheme
      });
    }
    
    return transformedData.sort((a, b) => a.sort_order - b.sort_order);
  };

  // Get unique categories for filter
  const getUniqueCategories = (items) => {
    const categories = items.reduce((acc, item) => {
      if (item.category && !acc.find(cat => cat.id === item.category.id)) {
        acc.push(item.category);
      }
      return acc;
    }, []);
    return categories;
  };

  // Fetch data from API
  useEffect(() => {
    const loadMenuItems = async () => {
      try {
        setLoading(true);
        setError(null);
        const menuItems = await fetchMenuItems();
        const transformedData = transformApiData(menuItems);
        setData(transformedData);
      } catch (err) {
        setError('Failed to load menu items');
        console.error('Error loading menu items:', err);
      } finally {
        setLoading(false);
      }
    };

    loadMenuItems();
  }, []);

  // Filter data based on search and category selection
  useEffect(() => {
    let filtered = data
      .filter(
        (item) =>
          selectedCategory === "all" ||
          item.category_id.toString() === selectedCategory
      )
      .filter((item) =>
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.variants.some((variant) =>
          variant.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    setFilteredData(filtered);
  }, [data, searchTerm, selectedCategory]);

  const openDetailsModal = (item) => {
    setSelectedItemDetails(item);
    setShowDetailsModal(true);
  };

  const handleEdit = (item) => {
    setCurrentItem(item);
    setShowEditModal(true);
  };

  const handleDelete = (item) => {
    setCurrentItem(item);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (currentItem) {
      setData(data.filter(item => item.id !== currentItem.id));
      setShowDeleteModal(false);
      setCurrentItem(null);
    }
  };

  // Modal component
  const Modal = ({ isOpen, onClose, title, children, size = "max-w-2xl" }) => {
    if (!isOpen) return null;

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className={`bg-white rounded-2xl shadow-2xl w-full ${size} max-h-[90vh] flex flex-col`}>
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-2xl font-bold text-gray-800">{title}</h3>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
            >
              <X size={24} />
            </button>
          </div>
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>
        </div>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-3">
              <Loader2 className="w-8 h-8 animate-spin text-[#FF6500]" />
              <span className="text-lg font-medium text-gray-600">Loading menu items...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <X className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Menu Items</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const uniqueCategories = getUniqueCategories(data);

  return (
    <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
                Menu Items with Variants
              </h1>
              <p className="text-gray-600 text-lg">Manage menu items and their variants efficiently</p>
            </div>
            <button 
              onClick={() => setShowAddModal(true)}
              className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300"
            >
              <Plus className="w-5 h-5" />
              Add New Item
            </button>
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search items or variants..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="pl-12 pr-8 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 bg-white min-w-48"
              >
                <option value="all">All Categories</option>
                {uniqueCategories.map((category) => (
                  <option key={category.id} value={category.id.toString()}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

       {/* Data Grid */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Item
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Code
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Base Price
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Variants Count
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredData.length > 0 ? (
                  filteredData.map((item, index) => {
                    return (
                      <tr key={item.id} className="hover:bg-gray-50 transition-all duration-200">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">
                            #{item.id}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${item.color}`}></div>
                            <div>
                              <div className="text-sm font-semibold text-gray-900">
                                {item.title}
                              </div>
                              {item.short_description && (
                                <div className="text-xs text-gray-500 max-w-xs truncate">
                                  {item.short_description}
                                </div>
                              )}
                              {item.is_featured && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-1">
                                  Featured
                                </span>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-600 font-mono">
                            {item.code}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-semibold text-[#FF6500]">
                            ${item.base_price.toFixed(2)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center justify-center px-3 py-1 rounded-full text-xs font-medium ${item.bgColor} ${item.textColor}`}>
                            {item.variants.length} variant{item.variants.length !== 1 ? 's' : ''}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">
                            {item.category?.name || 'No Category'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            item.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {item.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center gap-2">
                            <button
                              onClick={() => openDetailsModal(item)}
                              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="View Details"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleEdit(item)}
                              className="p-2 text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="Edit"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(item)}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="Delete"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan="8" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                          <Search className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          No Results Found
                        </h3>
                        <p className="text-gray-500">
                          Try adjusting your search terms or filters
                        </p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-6 text-center">
          <span className="text-sm text-gray-600">
            Showing {filteredData.length} of {data.length} menu items with variants
          </span>
        </div>

        {/* Details Modal */}
        <Modal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          title={`${selectedItemDetails?.title} - Details`}
          size="max-w-4xl"
        >
          {selectedItemDetails && (
            <div className="p-6">
              <div className="mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${selectedItemDetails.color}`}></div>
                  <h4 className="text-xl font-bold text-gray-800">{selectedItemDetails.title}</h4>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${selectedItemDetails.bgColor} ${selectedItemDetails.textColor}`}>
                    {selectedItemDetails.variants.length} variant{selectedItemDetails.variants.length !== 1 ? 's' : ''}
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-500">Code</p>
                    <p className="font-mono text-sm">{selectedItemDetails.code}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Base Price</p>
                    <p className="text-sm font-semibold text-[#FF6500]">${selectedItemDetails.base_price.toFixed(2)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Cost Price</p>
                    <p className="text-sm">${selectedItemDetails.cost_price.toFixed(2)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Category</p>
                    <p className="text-sm">{selectedItemDetails.category?.name || 'No Category'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedItemDetails.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {selectedItemDetails.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Sort Order</p>
                    <p className="text-sm">{selectedItemDetails.sort_order}</p>
                  </div>
                  {selectedItemDetails.prep_time_minutes && (
                    <div>
                      <p className="text-sm text-gray-500">Prep Time</p>
                      <p className="text-sm">{selectedItemDetails.prep_time_minutes} minutes</p>
                    </div>
                  )}
                  {selectedItemDetails.calories && (
                    <div>
                      <p className="text-sm text-gray-500">Calories</p>
                      <p className="text-sm">{selectedItemDetails.calories}</p>
                    </div>
                  )}
                </div>
                {selectedItemDetails.description && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-500">Description</p>
                    <p className="text-sm text-gray-900">{selectedItemDetails.description}</p>
                  </div>
                )}
                
                {/* Dietary Info and Allergens */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {selectedItemDetails.allergens && selectedItemDetails.allergens.length > 0 && (
                    <div>
                      <p className="text-sm text-gray-500 mb-2">Allergens</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedItemDetails.allergens.map((allergen, idx) => (
                          <span key={idx} className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                            {allergen}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  {selectedItemDetails.dietary_info && selectedItemDetails.dietary_info.length > 0 && (
                    <div>
                      <p className="text-sm text-gray-500 mb-2">Dietary Info</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedItemDetails.dietary_info.map((info, idx) => (
                          <span key={idx} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                            {info}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Variants Section */}
              {selectedItemDetails.variants.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                    <h5 className="text-lg font-semibold text-gray-800">Variants</h5>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                    {selectedItemDetails.variants.map((variant, index) => (
                      <div
                        key={variant.id}
                        className={`bg-gradient-to-br from-gray-50 to-white p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-md ${
                          variant.is_default ? 'border-[#FF6500] bg-orange-50' : 'border-gray-200'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h6 className="font-semibold text-gray-900">
                                {variant.name}
                              </h6>
                              {variant.is_default && (
                                <span className="px-2 py-0.5 bg-[#FF6500] text-white text-xs rounded-full">
                                  Default
                                </span>
                              )}
                            </div>
                            <p className="text-xs text-gray-500 font-mono mt-1">
                              {variant.code}
                            </p>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Price Modifier:</span>
                            <span className={`text-sm font-semibold ${
                              parseFloat(variant.price_modifier) >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {parseFloat(variant.price_modifier) >= 0 ? '+' : ''}${parseFloat(variant.price_modifier).toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Final Price:</span>
                            <span className="text-lg font-bold text-[#FF6500]">
                              ${(selectedItemDetails.base_price + parseFloat(variant.price_modifier)).toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Cost Modifier:</span>
                            <span className={`text-sm ${
                              parseFloat(variant.cost_modifier) >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {parseFloat(variant.cost_modifier) >= 0 ? '+' : ''}${parseFloat(variant.cost_modifier).toFixed(2)}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Sort Order:</span>
                            <span className="text-sm text-gray-900">{variant.sort_order}</span>
                          </div>
                        </div>
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="flex gap-1 justify-end">
                            <button 
                              className="p-1.5 text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200"
                              title="Edit Variant"
                            >
                              <Edit className="w-3.5 h-3.5" />
                            </button>
                            <button 
                              className="p-1.5 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                              title="Delete Variant"
                            >
                              <Trash2 className="w-3.5 h-3.5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </Modal>

        {/* Add Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Add New Menu Item"
        >
          <div className="p-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Item Name
                </label>
                <input
                  type="text"
                  placeholder="Enter item name"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  placeholder="Enter item description"
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Base Price
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cost Price
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                  />
                </div>
              </div>
              <div className="flex gap-3 justify-end pt-4">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-6 py-3 text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200"
                >
                  Cancel
                </button>
                <button className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300">
                  Add Item
                </button>
              </div>
            </div>
          </div>
        </Modal>

        {/* Edit Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title="Edit Menu Item"
        >
          <div className="p-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Item Name
                </label>
                <input
                  type="text"
                  defaultValue={currentItem?.title}
                  placeholder="Enter item name"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  defaultValue={currentItem?.description}
                  placeholder="Enter item description"
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Base Price
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    defaultValue={currentItem?.base_price}
                    placeholder="0.00"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cost Price
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    defaultValue={currentItem?.cost_price}
                    placeholder="0.00"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                  />
                </div>
              </div>
              <div className="flex gap-3 justify-end pt-4">
                <button
                  onClick={() => setShowEditModal(false)}
                  className="px-6 py-3 text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200"
                >
                  Cancel
                </button>
                <button className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300">
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </Modal>

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          title="Delete Menu Item"
          size="max-w-lg"
        >
          <div className="p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trash2 className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Are you sure?
              </h3>
              <p className="text-gray-600 mb-6">
                This will permanently delete the menu item "{currentItem?.title}" and all its variants. This action cannot be undone.
              </p>
              <div className="flex gap-3 justify-center">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="px-6 py-3 text-gray-600 bg-gray-100 rounded-xl hover:bg-gray-200 transition-all duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-6 py-3 bg-red-600 text-white font-semibold rounded-xl hover:bg-red-700 transition-all duration-200"
                >
                  Delete Item
                </button>
              </div>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default ModifiersGrid;
