"use client";
import { <PERSON><PERSON>, Plus, CheckCircle, XCircle, Upload, Import, ListOrdered } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";

const subCategoryMap = {
  electronics: ["Mobile", "Headphone"],
  jewelery: ["Ring", "Necklace"],
  // Add more mappings as needed
};

const MenueFiltetr = ({ categories, applayfilter }) => {
  const router = useRouter();
  const categoriesData = categories.map((e, idx) => ({ id: idx, name: e }));
  const [filters, setFilters] = useState({
    category: applayfilter.category,
    availability: applayfilter.availability || "all",
    subCategory: applayfilter.subCategory || "all",
  });

  useEffect(() => {
    setFilters({
      category: applayfilter.category,
      availability: applayfilter.availability || "all",
      subCategory: applayfilter.subCategory || "all",
    });
  }, [applayfilter.category, applayfilter.availability, applayfilter.subCategory]);

  console.log(applayfilter);

  const handleFilterChange = (key, value) => {
    setFilters((prev) => {
      if (key === "category") {
        return { ...prev, category: value, subCategory: "all" };
      }
      return { ...prev, [key]: value };
    });
    const newParams = new URLSearchParams(window.location.search);
    if (key === "category") {
      value !== "all" ? newParams.set("category", value) : newParams.delete("category");
      newParams.delete("subCategory"); // reset subCategory on category change
    }
    if (key === "availability") {
      value !== "all" ? newParams.set("availability", value) : newParams.delete("availability");
    }
    if (key === "subCategory") {
      value !== "all" ? newParams.set("subCategory", value) : newParams.delete("subCategory");
    }
    router.push(`${window.location.pathname}?${newParams.toString()}`);
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4">
      {/* Categories Grid (old style) */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-3 mb-6">
        {/* Add New Category Button */}
      
        {/* Existing Categories as Cards */}
        {categoriesData.length === 0 ? (
          <div className="col-span-full text-center py-8 text-gray-500">
            لا توجد تصنيفات متاحة
          </div>
        ) : (
          categoriesData?.map((e, idx) => (
            <div
              onClick={() => handleFilterChange("category", e.name)}
              key={idx}
              className={`relative cursor-pointer rounded-xl p-4 transition-all duration-300 border-2 min-h-[120px] flex flex-col justify-center bg-white hover:border-[#FCB190] hover:shadow-md hover:scale-105 
                ${filters.category === e.name ? "border-[#FF6500] bg-gradient-to-br from-orange-50 to-amber-50 shadow-lg scale-105" : "border-gray-200"}`}
            >
              {/* Icon */}
              <div className="flex items-center justify-center mb-3">
                <div className="p-2 rounded-lg transition-all duration-300 bg-gradient-to-r from-[#FF6500] to-[#FCB190]">
                  <Cookie className="w-5 h-5 transition-colors duration-300 text-white" />
                </div>
              </div>
              {/* Category Name */}
              <h3 className="font-semibold text-sm text-center mb-2 leading-tight text-gray-800">
                {e.name.toUpperCase()}
              </h3>
            </div>
          ))
        )}
      </div>
      {/* Availability and Subcategory Dropdowns below categories */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="flex flex-col items-start mb-6 mt-2">
            <select
              id="availability-select"
              className="w-full max-w-xs px-4 py-2 rounded-lg border border-gray-300 focus:border-[#FF6500] focus:ring-2 focus:ring-[#FF6500] bg-white text-gray-800 font-medium shadow-sm transition-all duration-200"
              value={filters.availability || "all"}
              onChange={e => handleFilterChange("availability", e.target.value)}
            >
              <option value="all">الكل</option>
              <option value="available">متاح</option>
              <option value="unavailable">غير متاح</option>
            </select>
          </div>
          {/* Subcategory dropdown, only show if category is selected and has subcategories */}
          {filters.category && subCategoryMap[filters.category] && (
            <div className="flex flex-col items-start mb-6 mt-2">
              <select
                id="subcategory-select"
                className="w-full max-w-xs px-4 py-2 rounded-lg border border-gray-300 focus:border-[#FF6500] focus:ring-2 focus:ring-[#FF6500] bg-white text-gray-800 font-medium shadow-sm transition-all duration-200"
                value={filters.subCategory || "all"}
                onChange={e => handleFilterChange("subCategory", e.target.value)}
              >
                <option value="all">كل التصنيفات الفرعية</option>
                {subCategoryMap[filters.category].map((sub) => (
                  <option key={sub} value={sub}>{sub}</option>
                ))}
              </select>
            </div>
          )}
        </div>
        <div className=" flex items-center gap-4">
          <button className="flex items-center gap-2 px-4 py-2 rounded-lg bg-primary text-white font-semibold hover:bg-[#ff7f32] focus:ring-2 focus:ring-[#FF6500] transition-all duration-200">
            <Import className="w-4 h-4" />
            استيراد ملف Excel
          </button>
          <button className="flex items-center gap-2 px-4 py-2 rounded-lg bg-primary text-white font-semibold hover:bg-[#ff7f32] focus:ring-2 focus:ring-[#FF6500] transition-all duration-200">
            <Upload className="w-4 h-4" />
            تحميل ملف  منيو Excel 
          </button>
          <Link href={"/arrengeMenue"} className="flex items-center gap-2 px-4 py-2 rounded-lg bg-primary text-white font-semibold hover:bg-[#ff7f32] focus:ring-2 focus:ring-[#FF6500] transition-all duration-200">
            <ListOrdered className="w-4 h-4" />
            ترتيب عناصر القائمه
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MenueFiltetr;
