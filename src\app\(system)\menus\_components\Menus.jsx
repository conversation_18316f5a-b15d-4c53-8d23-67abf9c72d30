// "use client";
// import React, { useState, useEffect } from "react";
// import { Edit, Trash2, Plus, Search, Filter, MoreVertical, X, Eye, ChevronRight, Loader2, Clock, Calendar, Users } from "lucide-react";

// import { fetchMenus } from '../../../../../lib/api';

// const Menus = () => {
//   const [searchTerm, setSearchTerm] = useState("");
//   const [selectedMenuType, setSelectedMenuType] = useState("all");
//   const [selectedTenant, setSelectedTenant] = useState("all");
//   const [showDetailsModal, setShowDetailsModal] = useState(false);
//   const [selectedMenuDetails, setSelectedMenuDetails] = useState(null);
//   const [showAddModal, setShowAddModal] = useState(false);
//   const [showEditModal, setShowEditModal] = useState(false);
//   const [showDeleteModal, setShowDeleteModal] = useState(false);
//   const [currentMenu, setCurrentMenu] = useState(null);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState(null);

//   const [data, setData] = useState([]);
//   const [filteredData, setFilteredData] = useState([]);

//   // Color schemes for menu types
//   const menuTypeColors = {
//     breakfast: {
//       color: "from-yellow-500 to-orange-500",
//       bgColor: "bg-yellow-50",
//       textColor: "text-yellow-700",
//       borderColor: "border-yellow-200",
//     },
//     lunch: {
//       color: "from-green-500 to-emerald-500",
//       bgColor: "bg-green-50",
//       textColor: "text-green-700",
//       borderColor: "border-green-200",
//     },
//     dinner: {
//       color: "from-purple-500 to-indigo-500",
//       bgColor: "bg-purple-50",
//       textColor: "text-purple-700",
//       borderColor: "border-purple-200",
//     },
//     drinks: {
//       color: "from-blue-500 to-cyan-500",
//       bgColor: "bg-blue-50",
//       textColor: "text-blue-700",
//       borderColor: "border-blue-200",
//     },
//     all_day: {
//       color: "from-orange-500 to-red-500",
//       bgColor: "bg-orange-50",
//       textColor: "text-orange-700",
//       borderColor: "border-orange-200",
//     },
//     main: {
//       color: "from-gray-500 to-gray-700",
//       bgColor: "bg-gray-50",
//       textColor: "text-gray-700",
//       borderColor: "border-gray-200",
//     }
//   };

//   // Transform API data to match component structure
//   const transformApiData = (menus) => {
//     return menus.map(menu => ({
//       ...menu,
//       ...(menuTypeColors[menu.menu_type] || menuTypeColors.main),
//       categoriesCount: menu.categories ? menu.categories.length : 0,
//       menuItemsCount: menu.menu_items ? menu.menu_items.length : 0,
//       totalItems: (menu.categories ? menu.categories.length : 0) + (menu.menu_items ? menu.menu_items.length : 0)
//     })).sort((a, b) => a.sort_order - b.sort_order);
//   };

//   // Get unique menu types for filter
//   const getMenuTypes = (menus) => {
//     const types = [...new Set(menus.map(menu => menu.menu_type))];
//     return types.filter(Boolean);
//   };

//   // Get unique tenants for filter
//   const getTenants = (menus) => {
//     const tenants = [...new Set(menus.map(menu => menu.tenant_id))];
//     return tenants.filter(Boolean);
//   };

//   // Format available days
//   const formatAvailableDays = (days) => {
//     if (!days || days.length === 0) return "All days";
//     return days.map(day => day.charAt(0).toUpperCase() + day.slice(1)).join(", ");
//   };

//   // Format time
//   const formatTime = (time) => {
//     if (!time) return "Not set";
//     return time;
//   };

//   // Fetch data from API
//   useEffect(() => {
//     const loadMenus = async () => {
//       try {
//         setLoading(true);
//         setError(null);
//         const menus = await fetchMenus();
//         const transformedData = transformApiData(menus);
//         setData(transformedData);
//       } catch (err) {
//         setError('Failed to load menus');
//         console.error('Error loading menus:', err);
//       } finally {
//         setLoading(false);
//       }
//     };

//     loadMenus();
//   }, []);

//   // Filter data based on search and filters
//   useEffect(() => {
//     let filtered = data
//       .filter(menu => 
//         selectedMenuType === "all" || menu.menu_type === selectedMenuType
//       )
//       .filter(menu =>
//         selectedTenant === "all" || menu.tenant_id.toString() === selectedTenant
//       )
//       .filter(menu =>
//         menu.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
//         menu.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
//         (menu.description && menu.description.toLowerCase().includes(searchTerm.toLowerCase()))
//       );
//     setFilteredData(filtered);
//   }, [data, searchTerm, selectedMenuType, selectedTenant]);

//   const openDetailsModal = (menu) => {
//     setSelectedMenuDetails(menu);
//     setShowDetailsModal(true);
//   };

//   const handleEdit = (menu) => {
//     setCurrentMenu(menu);
//     setShowEditModal(true);
//   };

//   const handleDelete = (menu) => {
//     setCurrentMenu(menu);
//     setShowDeleteModal(true);
//   };

//   const confirmDelete = () => {
//     if (currentMenu) {
//       setData(data.filter(menu => menu.id !== currentMenu.id));
//       setShowDeleteModal(false);
//       setCurrentMenu(null);
//     }
//   };

//   // Modal component
//   const Modal = ({ isOpen, onClose, title, children, size = "max-w-2xl" }) => {
//     if (!isOpen) return null;

//     return (
//       <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
//         <div className={`bg-white rounded-2xl shadow-2xl w-full ${size} max-h-[90vh] flex flex-col`}>
//           <div className="flex justify-between items-center p-6 border-b border-gray-200">
//             <h3 className="text-2xl font-bold text-gray-800">{title}</h3>
//             <button
//               onClick={onClose}
//               className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
//             >
//               <X size={24} />
//             </button>
//           </div>
//           <div className="flex-1 overflow-y-auto">
//             {children}
//           </div>
//         </div>
//       </div>
//     );
//   };

//   // Loading state
//   if (loading) {
//     return (
//       <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
//         <div className="max-w-7xl mx-auto">
//           <div className="flex items-center justify-center h-64">
//             <div className="flex items-center gap-3">
//               <Loader2 className="w-8 h-8 animate-spin text-[#FF6500]" />
//               <span className="text-lg font-medium text-gray-600">Loading menus...</span>
//             </div>
//           </div>
//         </div>
//       </div>
//     );
//   }

//   // Error state
//   if (error) {
//     return (
//       <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
//         <div className="max-w-7xl mx-auto">
//           <div className="flex items-center justify-center h-64">
//             <div className="text-center">
//               <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
//                 <X className="w-8 h-8 text-red-600" />
//               </div>
//               <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Menus</h3>
//               <p className="text-gray-600 mb-4">{error}</p>
//               <button
//                 onClick={() => window.location.reload()}
//                 className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
//               >
//                 Retry
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
//       <div className="max-w-7xl mx-auto">
//         {/* Header Section */}
//         <div className="mb-8">
//           <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
//             <div>
//               <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
//                 Menu Management
//               </h1>
//               <p className="text-gray-600 text-lg">Manage restaurant menus and their configurations</p>
//             </div>
//             <button 
//               onClick={() => setShowAddModal(true)}
//               className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300"
//             >
//               <Plus className="w-5 h-5" />
//               Add New Menu
//             </button>
//           </div>
//         </div>

//         {/* Search and Filter Section */}
//         <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 mb-8">
//           <div className="flex flex-col md:flex-row gap-4">
//             <div className="relative flex-1">
//               <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
//               <input
//                 type="text"
//                 placeholder="Search menus by name, code, or description..."
//                 value={searchTerm}
//                 onChange={(e) => setSearchTerm(e.target.value)}
//                 className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//               />
//             </div>
//             <div className="relative">
//               <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
//               <select
//                 value={selectedMenuType}
//                 onChange={(e) => setSelectedMenuType(e.target.value)}
//                 className="pl-12 pr-8 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 bg-white min-w-48"
//               >
//                 <option value="all">All Menu Types</option>
//                 {getMenuTypes(data).map((type) => (
//                   <option key={type} value={type}>
//                     {type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}
//                   </option>
//                 ))}
//               </select>
//             </div>
//             <div className="relative">
//               <Users className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
//               <select
//                 value={selectedTenant}
//                 onChange={(e) => setSelectedTenant(e.target.value)}
//                 className="pl-12 pr-8 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 bg-white min-w-48"
//               >
//                 <option value="all">All Tenants</option>
//                 {getTenants(data).map((tenantId) => (
//                   <option key={tenantId} value={tenantId.toString()}>
//                     Tenant {tenantId}
//                   </option>
//                 ))}
//               </select>
//             </div>
//           </div>
//         </div>

//         {/* Data Grid */}
//         <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
//           <div className="overflow-x-auto">
//             <table className="w-full">
//               <thead>
//                 <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
//                   <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     ID
//                   </th>
//                   <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Menu Name
//                   </th>
//                   <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Type
//                   </th>
//                   <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Time Range
//                   </th>
//                   <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Items
//                   </th>
//                   <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Status
//                   </th>
//                   <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Tenant
//                   </th>
//                   <th className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider">
//                     Actions
//                   </th>
//                 </tr>
//               </thead>
//               <tbody className="divide-y divide-gray-200">
//                 {filteredData.length > 0 ? (
//                   filteredData.map((menu, index) => {
//                     return (
//                       <tr key={menu.id} className="hover:bg-gray-50 transition-all duration-200">
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           <span className="text-sm font-medium text-gray-900">
//                             #{menu.id}
//                           </span>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           <div className="flex items-center gap-3">
//                             <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${menu.color}`}></div>
//                             <div>
//                               <div className="text-sm font-semibold text-gray-900">
//                                 {menu.name}
//                               </div>
//                               <div className="text-xs text-gray-500 font-mono">
//                                 {menu.code}
//                               </div>
//                             </div>
//                           </div>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${menu.bgColor} ${menu.textColor}`}>
//                             {menu.menu_type?.replace('_', ' ')}
//                           </span>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           <div className="text-sm text-gray-900">
//                             <div className="flex items-center gap-1">
//                               <Clock className="w-3 h-3 text-gray-400" />
//                               <span>{formatTime(menu.start_time)} - {formatTime(menu.end_time)}</span>
//                             </div>
//                           </div>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           <div className="flex flex-col gap-1">
//                             <span className="text-xs text-gray-600">
//                               {menu.categoriesCount} categories
//                             </span>
//                             <span className="text-xs text-gray-600">
//                               {menu.menuItemsCount} items
//                             </span>
//                           </div>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           <div className="flex flex-col gap-1">
//                             <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
//                               menu.is_active 
//                                 ? 'bg-green-100 text-green-800' 
//                                 : 'bg-red-100 text-red-800'
//                             }`}>
//                               {menu.is_active ? 'Active' : 'Inactive'}
//                             </span>
//                             {menu.is_default && (
//                               <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
//                                 Default
//                               </span>
//                             )}
//                           </div>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap">
//                           <span className="text-sm text-gray-900">
//                             T-{menu.tenant_id}
//                           </span>
//                           <div className="text-xs text-gray-500">
//                             B-{menu.branch_id}
//                           </div>
//                         </td>
//                         <td className="px-6 py-4 whitespace-nowrap text-center">
//                           <div className="flex items-center justify-center gap-2">
//                             <button
//                               onClick={() => openDetailsModal(menu)}
//                               className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 hover:scale-110"
//                               title="View Details"
//                             >
//                               <Eye className="w-4 h-4" />
//                             </button>
//                             <button
//                               onClick={() => handleEdit(menu)}
//                               className="p-2 text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200 hover:scale-110"
//                               title="Edit"
//                             >
//                               <Edit className="w-4 h-4" />
//                             </button>
//                             <button
//                               onClick={() => handleDelete(menu)}
//                               className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 hover:scale-110"
//                               title="Delete"
//                             >
//                               <Trash2 className="w-4 h-4" />
//                             </button>
//                           </div>
//                         </td>
//                       </tr>
//                     );
//                   })
//                 ) : (
//                   <tr>
//                     <td colSpan="8" className="px-6 py-12 text-center">
//                       <div className="flex flex-col items-center justify-center">
//                         <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
//                           <Search className="w-8 h-8 text-gray-400" />
//                         </div>
//                         <h3 className="text-lg font-semibold text-gray-900 mb-2">
//                           No Results Found
//                         </h3>
//                         <p className="text-gray-500">
//                           Try adjusting your search terms or filters
//                         </p>
//                       </div>
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//           </div>
//         </div>

//         {/* Results Count */}
//         <div className="mt-6 text-center">
//           <span className="text-sm text-gray-600">
//             Showing {filteredData.length} of {data.length} menus
//           </span>
//         </div>

//         {/* Details Modal */}
//         <Modal
//           isOpen={showDetailsModal}
//           onClose={() => setShowDetailsModal(false)}
//           title={`${selectedMenuDetails?.name} - Details`}
//           size="max-w-6xl"
//         >
//           {selectedMenuDetails && (
//             <div className="p-6">
//               {/* Menu Info */}
//               <div className="mb-6">
//                 <div className="flex items-center gap-3 mb-4">
//                   <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${selectedMenuDetails.color}`}></div>
//                   <h4 className="text-xl font-bold text-gray-800">{selectedMenuDetails.name}</h4>
//                   <span className={`px-3 py-1 rounded-full text-sm font-medium ${selectedMenuDetails.bgColor} ${selectedMenuDetails.textColor}`}>
//                     {selectedMenuDetails.menu_type?.replace('_', ' ')}
//                   </span>
//                 </div>

//                 <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
//                   <div className="bg-gray-50 p-4 rounded-xl">
//                     <h5 className="font-semibold text-gray-700 mb-2">Basic Info</h5>
//                     <div className="space-y-2 text-sm">
//                       <div><span className="text-gray-500">Code:</span> <span className="font-mono">{selectedMenuDetails.code}</span></div>
//                       <div><span className="text-gray-500">Tenant:</span> T-{selectedMenuDetails.tenant_id}</div>
//                       <div><span className="text-gray-500">Branch:</span> B-{selectedMenuDetails.branch_id}</div>
//                       <div><span className="text-gray-500">Sort Order:</span> {selectedMenuDetails.sort_order}</div>
//                     </div>
//                   </div>

//                   <div className="bg-gray-50 p-4 rounded-xl">
//                     <h5 className="font-semibold text-gray-700 mb-2">Schedule</h5>
//                     <div className="space-y-2 text-sm">
//                       <div className="flex items-center gap-1">
//                         <Clock className="w-3 h-3 text-gray-400" />
//                         <span className="text-gray-500">Time:</span> 
//                         <span>{formatTime(selectedMenuDetails.start_time)} - {formatTime(selectedMenuDetails.end_time)}</span>
//                       </div>
//                       <div className="flex items-start gap-1">
//                         <Calendar className="w-3 h-3 text-gray-400 mt-1" />
//                         <div>
//                           <span className="text-gray-500">Days:</span>
//                           <div className="text-xs">{formatAvailableDays(selectedMenuDetails.available_days)}</div>
//                         </div>
//                       </div>
//                     </div>
//                   </div>

//                   <div className="bg-gray-50 p-4 rounded-xl">
//                     <h5 className="font-semibold text-gray-700 mb-2">Status</h5>
//                     <div className="space-y-2">
//                       <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
//                         selectedMenuDetails.is_active 
//                           ? 'bg-green-100 text-green-800' 
//                           : 'bg-red-100 text-red-800'
//                       }`}>
//                         {selectedMenuDetails.is_active ? 'Active' : 'Inactive'}
//                       </span>
//                       {selectedMenuDetails.is_default && (
//                         <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 block">
//                           Default Menu
//                         </span>
//                       )}
//                     </div>
//                   </div>
//                 </div>

//                 {selectedMenuDetails.description && (
//                   <div className="bg-blue-50 p-4 rounded-xl mb-6">
//                     <h5 className="font-semibold text-blue-900 mb-2">Description</h5>
//                     <p className="text-blue-800">{selectedMenuDetails.description}</p>
//                   </div>
//                 )}
//               </div>

//               {/* Categories and Menu Items */}
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                 {/* Categories */}
//                 <div>
//                   <h5 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
//                     Categories ({selectedMenuDetails.categoriesCount})
//                   </h5>
//                   {selectedMenuDetails.categories && selectedMenuDetails.categories.length > 0 ? (
//                     <div className="space-y-3 max-h-64 overflow-y-auto">
//                       {selectedMenuDetails.categories.map((category) => (
//                         <div key={category.id} className="bg-white border border-gray-200 p-4 rounded-xl">
//                           <div className="flex items-center justify-between mb-2">
//                             <h6 className="font-medium text-gray-900">{category.name}</h6>
//                             <span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${
//                               category.is_active 
//                                 ? 'bg-green-100 text-green-800' 
//                                 : 'bg-red-100 text-red-800'
//                             }`}>
//                               {category.is_active ? 'Active' : 'Inactive'}
//                             </span>
//                           </div>
//                           <div className="text-xs text-gray-500 space-y-1">
//                             <div>Code: {category.code}</div>
//                             <div>Sort: {category.sort_order}</div>
//                             {category.description && <div>Desc: {category.description}</div>}
//                           </div>
//                         </div>
//                       ))}
//                     </div>
//                   ) : (
//                     <p className="text-gray-500 text-sm">No categories available</p>
//                   )}
//                 </div>

//                 {/* Menu Items */}
//                 <div>
//                   <h5 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
//                     Menu Items ({selectedMenuDetails.menuItemsCount})
//                   </h5>
//                   {selectedMenuDetails.menu_items && selectedMenuDetails.menu_items.length > 0 ? (
//                     <div className="space-y-3 max-h-64 overflow-y-auto">
//                       {selectedMenuDetails.menu_items.map((item) => (
//                         <div key={item.id} className="bg-white border border-gray-200 p-4 rounded-xl">
//                           <div className="flex items-center justify-between mb-2">
//                             <h6 className="font-medium text-gray-900">{item.name}</h6>
//                             <div className="flex items-center gap-2">
//                               <span className="text-lg font-bold text-[#FF6500]">
//                                 ${item.base_price}
//                               </span>
//                               {item.is_featured && (
//                                 <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800">
//                                   Featured
//                                 </span>
//                               )}
//                             </div>
//                           </div>
//                           <div className="text-xs text-gray-500 space-y-1">
//                             <div>Code: {item.code}</div>
//                             <div>Calories: {item.calories}</div>
//                             {item.prep_time_minutes > 0 && <div>Prep: {item.prep_time_minutes}min</div>}
//                             {item.short_description && <div>{item.short_description}</div>}
//                           </div>
//                         </div>
//                       ))}
//                     </div>
//                   ) : (
//                     <p className="text-gray-500 text-sm">No menu items available</p>
//                   )}
//                 </div>
//               </div>
              
//               <div className="mt-6 flex justify-end">
//                 <button
//                   onClick={() => setShowDetailsModal(false)}
//                   className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
//                 >
//                   Close
//                 </button>
//               </div>
//             </div>
//           )}
//         </Modal>

//         {/* Add Modal */}
//         <Modal
//           isOpen={showAddModal}
//           onClose={() => setShowAddModal(false)}
//           title="Add New Menu"
//         >
//           <div className="p-6">
//             <div className="space-y-4">
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                  <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Menu Name
//                   </label>
//                   <input
//                     type="text"
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                     placeholder="Enter menu name"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Menu Code
//                   </label>
//                   <input
//                     type="text"
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                     placeholder="Enter menu code"
//                   />
//                 </div>
//               </div>
              
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Menu Type
//                   </label>
//                   <select className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200">
//                     <option value="">Select menu type</option>
//                     <option value="breakfast">Breakfast</option>
//                     <option value="lunch">Lunch</option>
//                     <option value="dinner">Dinner</option>
//                     <option value="drinks">Drinks</option>
//                     <option value="all_day">All Day</option>
//                   </select>
//                 </div>
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Tenant ID
//                   </label>
//                   <input
//                     type="number"
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                     placeholder="Enter tenant ID"
//                   />
//                 </div>
//               </div>

//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Start Time
//                   </label>
//                   <input
//                     type="time"
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     End Time
//                   </label>
//                   <input
//                     type="time"
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                   />
//                 </div>
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Description
//                 </label>
//                 <textarea
//                   rows={4}
//                   className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                   placeholder="Enter menu description"
//                 />
//               </div>

//               <div className="flex items-center gap-6">
//                 <label className="flex items-center gap-3">
//                   <input
//                     type="checkbox"
//                     className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
//                   />
//                   <span className="text-sm font-medium text-gray-700">Active</span>
//                 </label>
//                 <label className="flex items-center gap-3">
//                   <input
//                     type="checkbox"
//                     className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
//                   />
//                   <span className="text-sm font-medium text-gray-700">Default Menu</span>
//                 </label>
//               </div>
//             </div>
            
//             <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
//               <button
//                 onClick={() => setShowAddModal(false)}
//                 className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={() => setShowAddModal(false)}
//                 className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
//               >
//                 Create Menu
//               </button>
//             </div>
//           </div>
//         </Modal>

//         {/* Edit Modal */}
//         <Modal
//           isOpen={showEditModal}
//           onClose={() => setShowEditModal(false)}
//           title={`Edit Menu - ${currentMenu?.name}`}
//         >
//           <div className="p-6">
//             <div className="space-y-4">
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Menu Name
//                   </label>
//                   <input
//                     type="text"
//                     defaultValue={currentMenu?.name}
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                     placeholder="Enter menu name"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Menu Code
//                   </label>
//                   <input
//                     type="text"
//                     defaultValue={currentMenu?.code}
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                     placeholder="Enter menu code"
//                   />
//                 </div>
//               </div>
              
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Menu Type
//                   </label>
//                   <select 
//                     defaultValue={currentMenu?.menu_type}
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                   >
//                     <option value="">Select menu type</option>
//                     <option value="breakfast">Breakfast</option>
//                     <option value="lunch">Lunch</option>
//                     <option value="dinner">Dinner</option>
//                     <option value="drinks">Drinks</option>
//                     <option value="all_day">All Day</option>
//                   </select>
//                 </div>
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Tenant ID
//                   </label>
//                   <input
//                     type="number"
//                     defaultValue={currentMenu?.tenant_id}
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                     placeholder="Enter tenant ID"
//                   />
//                 </div>
//               </div>

//               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     Start Time
//                   </label>
//                   <input
//                     type="time"
//                     defaultValue={currentMenu?.start_time}
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">
//                     End Time
//                   </label>
//                   <input
//                     type="time"
//                     defaultValue={currentMenu?.end_time}
//                     className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                   />
//                 </div>
//               </div>

//               <div>
//                 <label className="block text-sm font-medium text-gray-700 mb-2">
//                   Description
//                 </label>
//                 <textarea
//                   rows={4}
//                   defaultValue={currentMenu?.description}
//                   className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
//                   placeholder="Enter menu description"
//                 />
//               </div>

//               <div className="flex items-center gap-6">
//                 <label className="flex items-center gap-3">
//                   <input
//                     type="checkbox"
//                     defaultChecked={currentMenu?.is_active}
//                     className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
//                   />
//                   <span className="text-sm font-medium text-gray-700">Active</span>
//                 </label>
//                 <label className="flex items-center gap-3">
//                   <input
//                     type="checkbox"
//                     defaultChecked={currentMenu?.is_default}
//                     className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
//                   />
//                   <span className="text-sm font-medium text-gray-700">Default Menu</span>
//                 </label>
//               </div>
//             </div>
            
//             <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
//               <button
//                 onClick={() => setShowEditModal(false)}
//                 className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={() => setShowEditModal(false)}
//                 className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
//               >
//                 Update Menu
//               </button>
//             </div>
//           </div>
//         </Modal>

//         {/* Delete Confirmation Modal */}
//         <Modal
//           isOpen={showDeleteModal}
//           onClose={() => setShowDeleteModal(false)}
//           title="Confirm Delete"
//           size="max-w-md"
//         >
//           <div className="p-6">
//             <div className="text-center">
//               <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
//                 <Trash2 className="w-8 h-8 text-red-600" />
//               </div>
//               <h3 className="text-lg font-semibold text-gray-900 mb-2">
//                 Delete Menu
//               </h3>
//               <p className="text-gray-600 mb-6">
//                 Are you sure you want to delete "<span className="font-semibold">{currentMenu?.name}</span>"? 
//                 This action cannot be undone.
//               </p>
//               <div className="flex justify-center gap-3">
//                 <button
//                   onClick={() => setShowDeleteModal(false)}
//                   className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   onClick={confirmDelete}
//                   className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-xl transition-all duration-200"
//                 >
//                   Delete Menu
//                 </button>
//               </div>
//             </div>
//           </div>
//         </Modal>
//       </div>
//     </div>
//   );
// };

// export default Menus;
"use client";
import React, { useState, useEffect } from "react";
import { Edit, Trash2, Plus, Search, Filter, MoreVertical, X, Eye, ChevronRight, Loader2, Clock, Calendar, Users } from "lucide-react";

import { fetchMenus, createMenu } from '../../../../../lib/api';

const Menus = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMenuType, setSelectedMenuType] = useState("all");
  const [selectedTenant, setSelectedTenant] = useState("all");
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedMenuDetails, setSelectedMenuDetails] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentMenu, setCurrentMenu] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);

  // Add Modal Form State
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    menu_type: "",
    is_active: true,
    is_default: false,
    sort_order: 1
  });
  const [formErrors, setFormErrors] = useState({});
  const [isCreating, setIsCreating] = useState(false);

  // Color schemes for menu types
  const menuTypeColors = {
    breakfast: {
      color: "from-yellow-500 to-orange-500",
      bgColor: "bg-yellow-50",
      textColor: "text-yellow-700",
      borderColor: "border-yellow-200",
    },
    lunch: {
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50",
      textColor: "text-green-700",
      borderColor: "border-green-200",
    },
    dinner: {
      color: "from-purple-500 to-indigo-500",
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
      borderColor: "border-purple-200",
    },
    all_day: {
      color: "from-orange-500 to-red-500",
      bgColor: "bg-orange-50",
      textColor: "text-orange-700",
      borderColor: "border-orange-200",
    },
    seasonal: {
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
      borderColor: "border-blue-200",
    },
    special: {
      color: "from-pink-500 to-rose-500",
      bgColor: "bg-pink-50",
      textColor: "text-pink-700",
      borderColor: "border-pink-200",
    },
    main: {
      color: "from-gray-500 to-gray-700",
      bgColor: "bg-gray-50",
      textColor: "text-gray-700",
      borderColor: "border-gray-200",
    }
  };

  // Get user data from localStorage
  const getUserData = () => {
    try {
      const userData = localStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
      return null;
    }
  };

  // Transform API data to match component structure
  const transformApiData = (menus) => {
    return menus.map(menu => ({
      ...menu,
      ...(menuTypeColors[menu.menu_type] || menuTypeColors.main),
      categoriesCount: menu.categories ? menu.categories.length : 0,
      menuItemsCount: menu.menu_items ? menu.menu_items.length : 0,
      totalItems: (menu.categories ? menu.categories.length : 0) + (menu.menu_items ? menu.menu_items.length : 0)
    })).sort((a, b) => a.sort_order - b.sort_order);
  };

  // Get unique menu types for filter
  const getMenuTypes = (menus) => {
    const types = [...new Set(menus.map(menu => menu.menu_type))];
    return types.filter(Boolean);
  };

  // Get unique tenants for filter
  const getTenants = (menus) => {
    const tenants = [...new Set(menus.map(menu => menu.tenant_id))];
    return tenants.filter(Boolean);
  };

  // Format available days
  const formatAvailableDays = (days) => {
    if (!days || days.length === 0) return "All days";
    return days.map(day => day.charAt(0).toUpperCase() + day.slice(1)).join(", ");
  };

  // Format time
  const formatTime = (time) => {
    if (!time) return "Not set";
    return time;
  };

  // Fetch data from API
  const loadMenus = async () => {
    try {
      setLoading(true);
      setError(null);
      const menus = await fetchMenus();
      const transformedData = transformApiData(menus);
      setData(transformedData);
    } catch (err) {
      setError('Failed to load menus');
      console.error('Error loading menus:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMenus();
  }, []);

  // Filter data based on search and filters
  useEffect(() => {
    let filtered = data
      .filter(menu => 
        selectedMenuType === "all" || menu.menu_type === selectedMenuType
      )
      .filter(menu =>
        selectedTenant === "all" || menu.tenant_id.toString() === selectedTenant
      )
      .filter(menu =>
        menu.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        menu.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (menu.description && menu.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    setFilteredData(filtered);
  }, [data, searchTerm, selectedMenuType, selectedTenant]);

  // Handle form input changes
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear errors when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      code: "",
      description: "",
      menu_type: "",
      is_active: true,
      is_default: false,
      sort_order: 1
    });
    setFormErrors({});
  };

  // Handle create menu
  const handleCreateMenu = async () => {
    const userData = getUserData();
    
    if (!userData || !userData.tenant_id) {
      setFormErrors({ general: 'Unable to get user data. Please login again.' });
      return;
    }

    // Prepare menu data
    const menuData = {
      tenant_id: userData.tenant_id,
      name: formData.name,
      code: formData.code,
      description: formData.description,
      menu_type: formData.menu_type,
      is_active: formData.is_active,
      is_default: formData.is_default,
      sort_order: formData.sort_order
    };

    try {
      setIsCreating(true);
      setFormErrors({});
      
      const newMenu = await createMenu(menuData);
      
      // Reload menus to get updated data
      await loadMenus();
      
      // Close modal and reset form
      setShowAddModal(false);
      resetForm();
      
      // Show success message (you might want to add a toast notification here)
      console.log('Menu created successfully:', newMenu);
      
    } catch (error) {
      console.error('Error creating menu:', error);
      
      // Handle validation errors
      if (error.errors) {
        setFormErrors(error.errors);
      } else {
        setFormErrors({ 
          general: error.message || 'Failed to create menu. Please try again.' 
        });
      }
    } finally {
      setIsCreating(false);
    }
  };

  const openDetailsModal = (menu) => {
    setSelectedMenuDetails(menu);
    setShowDetailsModal(true);
  };

  const handleEdit = (menu) => {
    setCurrentMenu(menu);
    setShowEditModal(true);
  };

  const handleDelete = (menu) => {
    setCurrentMenu(menu);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (currentMenu) {
      setData(data.filter(menu => menu.id !== currentMenu.id));
      setShowDeleteModal(false);
      setCurrentMenu(null);
    }
  };

  // Modal component
  const Modal = ({ isOpen, onClose, title, children, size = "max-w-2xl" }) => {
    if (!isOpen) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className={`bg-white rounded-2xl shadow-2xl w-full ${size} max-h-[90vh] flex flex-col`}>
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-2xl font-bold text-gray-800">{title}</h3>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
            >
              <X size={24} />
            </button>
          </div>
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>
        </div>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-3">
              <Loader2 className="w-8 h-8 animate-spin text-[#FF6500]" />
              <span className="text-lg font-medium text-gray-600">Loading menus...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <X className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Menus</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
                Menu Management
              </h1>
              <p className="text-gray-600 text-lg">Manage restaurant menus and their configurations</p>
            </div>
            <button 
              onClick={() => {
                resetForm();
                setShowAddModal(true);
              }}
              className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300"
            >
              <Plus className="w-5 h-5" />
              Add New Menu
            </button>
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search menus by name, code, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={selectedMenuType}
                onChange={(e) => setSelectedMenuType(e.target.value)}
                className="pl-12 pr-8 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 bg-white min-w-48"
              >
                <option value="all">All Menu Types</option>
                {getMenuTypes(data).map((type) => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}
                  </option>
                ))}
              </select>
            </div>
            <div className="relative">
              <Users className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={selectedTenant}
                onChange={(e) => setSelectedTenant(e.target.value)}
                className="pl-12 pr-8 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 bg-white min-w-48"
              >
                <option value="all">All Tenants</option>
                {getTenants(data).map((tenantId) => (
                  <option key={tenantId} value={tenantId.toString()}>
                    Tenant {tenantId}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Data Grid */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Menu Name
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Time Range
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Items
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Tenant
                  </th>
                  <th className="px-6 py-4 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredData.length > 0 ? (
                  filteredData.map((menu, index) => {
                    return (
                      <tr key={menu.id} className="hover:bg-gray-50 transition-all duration-200">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm font-medium text-gray-900">
                            #{menu.id}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${menu.color}`}></div>
                            <div>
                              <div className="text-sm font-semibold text-gray-900">
                                {menu.name}
                              </div>
                              <div className="text-xs text-gray-500 font-mono">
                                {menu.code}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${menu.bgColor} ${menu.textColor}`}>
                            {menu.menu_type?.replace('_', ' ')}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3 text-gray-400" />
                              <span>{formatTime(menu.start_time)} - {formatTime(menu.end_time)}</span>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex flex-col gap-1">
                            <span className="text-xs text-gray-600">
                              {menu.categoriesCount} categories
                            </span>
                            <span className="text-xs text-gray-600">
                              {menu.menuItemsCount} items
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex flex-col gap-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              menu.is_active 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {menu.is_active ? 'Active' : 'Inactive'}
                            </span>
                            {menu.is_default && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Default
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">
                            T-{menu.tenant_id}
                          </span>
                          <div className="text-xs text-gray-500">
                            B-{menu.branch_id}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="flex items-center justify-center gap-2">
                            <button
                              onClick={() => openDetailsModal(menu)}
                              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="View Details"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleEdit(menu)}
                              className="p-2 text-[#FF6500] hover:bg-orange-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="Edit"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(menu)}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 hover:scale-110"
                              title="Delete"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan="8" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                          <Search className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          No Results Found
                        </h3>
                        <p className="text-gray-500">
                          Try adjusting your search terms or filters
                        </p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-6 text-center">
          <span className="text-sm text-gray-600">
            Showing {filteredData.length} of {data.length} menus
          </span>
        </div>

        {/* Details Modal */}
        <Modal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          title={`${selectedMenuDetails?.name} - Details`}
          size="max-w-6xl"
        >
          {selectedMenuDetails && (
            <div className="p-6">
              {/* Menu Info */}
              <div className="mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${selectedMenuDetails.color}`}></div>
                  <h4 className="text-xl font-bold text-gray-800">{selectedMenuDetails.name}</h4>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${selectedMenuDetails.bgColor} ${selectedMenuDetails.textColor}`}>
                    {selectedMenuDetails.menu_type?.replace('_', ' ')}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div className="bg-gray-50 p-4 rounded-xl">
                    <h5 className="font-semibold text-gray-700 mb-2">Basic Info</h5>
                    <div className="space-y-2 text-sm">
                      <div><span className="text-gray-500">Code:</span> <span className="font-mono">{selectedMenuDetails.code}</span></div>
                      <div><span className="text-gray-500">Tenant:</span> T-{selectedMenuDetails.tenant_id}</div>
                      <div><span className="text-gray-500">Branch:</span> B-{selectedMenuDetails.branch_id}</div>
                      <div><span className="text-gray-500">Sort Order:</span> {selectedMenuDetails.sort_order}</div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-xl">
                    <h5 className="font-semibold text-gray-700 mb-2">Schedule</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-gray-500">Time:</span> 
                        <span>{formatTime(selectedMenuDetails.start_time)} - {formatTime(selectedMenuDetails.end_time)}</span>
                      </div>
                      <div className="flex items-start gap-1">
                        <Calendar className="w-3 h-3 text-gray-400 mt-1" />
                        <div>
                          <span className="text-gray-500">Days:</span>
                          <div className="text-xs">{formatAvailableDays(selectedMenuDetails.available_days)}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-xl">
                    <h5 className="font-semibold text-gray-700 mb-2">Status</h5>
                    <div className="space-y-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        selectedMenuDetails.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {selectedMenuDetails.is_active ? 'Active' : 'Inactive'}
                      </span>
                      {selectedMenuDetails.is_default && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 block">
                          Default Menu
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {selectedMenuDetails.description && (
                  <div className="bg-blue-50 p-4 rounded-xl mb-6">
                    <h5 className="font-semibold text-blue-900 mb-2">Description</h5>
                    <p className="text-blue-800">{selectedMenuDetails.description}</p>
                  </div>
                )}
              </div>

              {/* Categories and Menu Items */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Categories */}
                <div>
                  <h5 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    Categories ({selectedMenuDetails.categoriesCount})
                  </h5>
                  {selectedMenuDetails.categories && selectedMenuDetails.categories.length > 0 ? (
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {selectedMenuDetails.categories.map((category) => (
                        <div key={category.id} className="bg-white border border-gray-200 p-4 rounded-xl">
                          <div className="flex items-center justify-between mb-2">
                            <h6 className="font-medium text-gray-900">{category.name}</h6>
                            <span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${
                              category.is_active 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {category.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 space-y-1">
                            <div>Code: {category.code}</div>
                            <div>Sort: {category.sort_order}</div>
                            {category.description && <div>Desc: {category.description}</div>}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No categories available</p>
                  )}
                </div>

                {/* Menu Items */}
                <div>
                  <h5 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    Menu Items ({selectedMenuDetails.menuItemsCount})
                  </h5>
                  {selectedMenuDetails.menu_items && selectedMenuDetails.menu_items.length > 0 ? (
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {selectedMenuDetails.menu_items.map((item) => (
                        <div key={item.id} className="bg-white border border-gray-200 p-4 rounded-xl">
                          <div className="flex items-center justify-between mb-2">
                            <h6 className="font-medium text-gray-900">{item.name}</h6>
                            <div className="flex items-center gap-2">
                              <span className="text-lg font-bold text-[#FF6500]">
                                ${item.base_price}
                              </span>
                              {item.is_featured && (
                                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800">
                                  Featured
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 space-y-1">
                            <div>Code: {item.code}</div>
                            <div>Calories: {item.calories}</div>
                            {item.prep_time_minutes > 0 && <div>Prep: {item.prep_time_minutes}min</div>}
                            {item.short_description && <div>{item.short_description}</div>}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">No menu items available</p>
                  )}
                </div>
              </div>
              
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </Modal> 
        {/* Add Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Add New Menu"
        >
          <div className="p-6">
            {formErrors.general && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                <p className="text-red-700 text-sm">{formErrors.general}</p>
              </div>
            )}
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Menu Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleFormChange('name', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 ${
                      formErrors.name ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="Enter menu name"
                    disabled={isCreating}
                  />
                  {formErrors.name && (
                    <p className="text-red-600 text-xs mt-1">{formErrors.name}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Menu Code *
                  </label>
                  <input
                    type="text"
                    value={formData.code}
                    onChange={(e) => handleFormChange('code', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 ${
                      formErrors.code ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="Enter menu code"
                    disabled={isCreating}
                  />
                  {formErrors.code && (
                    <p className="text-red-600 text-xs mt-1">{formErrors.code}</p>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Menu Type *
                  </label>
                  <select 
                    value={formData.menu_type}
                    onChange={(e) => handleFormChange('menu_type', e.target.value)}
                    className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 ${
                      formErrors.menu_type ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    disabled={isCreating}
                  >
                    <option value="">Select menu type</option>
                    <option value="breakfast">Breakfast</option>
                    <option value="lunch">Lunch</option>
                    <option value="dinner">Dinner</option>
                    <option value="all_day">All Day</option>
                    <option value="seasonal">Seasonal</option>
                    <option value="special">Special</option>
                  </select>
                  {formErrors.menu_type && (
                    <p className="text-red-600 text-xs mt-1">{formErrors.menu_type}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort Order
                  </label>
                  <input
                    type="number"
                    value={formData.sort_order}
                    onChange={(e) => handleFormChange('sort_order', parseInt(e.target.value) || 1)}
                    className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 ${
                      formErrors.sort_order ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="Enter sort order"
                    min="1"
                    disabled={isCreating}
                  />
                  {formErrors.sort_order && (
                    <p className="text-red-600 text-xs mt-1">{formErrors.sort_order}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  rows={4}
                  value={formData.description}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200 ${
                    formErrors.description ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="Enter menu description"
                  disabled={isCreating}
                />
                {formErrors.description && (
                  <p className="text-red-600 text-xs mt-1">{formErrors.description}</p>
                )}
              </div>

              <div className="flex items-center gap-6">
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => handleFormChange('is_active', e.target.checked)}
                    className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
                    disabled={isCreating}
                  />
                  <span className="text-sm font-medium text-gray-700">Active</span>
                </label>
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={formData.is_default}
                    onChange={(e) => handleFormChange('is_default', e.target.checked)}
                    className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
                    disabled={isCreating}
                  />
                  <span className="text-sm font-medium text-gray-700">Default Menu</span>
                </label>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => setShowAddModal(false)}
                disabled={isCreating}
                className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateMenu}
                disabled={isCreating}
                className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Menu'
                )}
              </button>
            </div>
          </div>
        </Modal>

        {/* Edit Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title={`Edit Menu - ${currentMenu?.name}`}
        >
          <div className="p-6">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Menu Name
                  </label>
                  <input
                    type="text"
                    defaultValue={currentMenu?.name}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                    placeholder="Enter menu name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Menu Code
                  </label>
                  <input
                    type="text"
                    defaultValue={currentMenu?.code}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                    placeholder="Enter menu code"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Menu Type
                  </label>
                  <select 
                    defaultValue={currentMenu?.menu_type}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                  >
                    <option value="">Select menu type</option>
                    <option value="breakfast">Breakfast</option>
                    <option value="lunch">Lunch</option>
                    <option value="dinner">Dinner</option>
                    <option value="all_day">All Day</option>
                    <option value="seasonal">Seasonal</option>
                    <option value="special">Special</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort Order
                  </label>
                  <input
                    type="number"
                    defaultValue={currentMenu?.sort_order}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                    placeholder="Enter sort order"
                    min="1"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Time
                  </label>
                  <input
                    type="time"
                    defaultValue={currentMenu?.start_time}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Time
                  </label>
                  <input
                    type="time"
                    defaultValue={currentMenu?.end_time}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  rows={4}
                  defaultValue={currentMenu?.description}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] transition-all duration-200"
                  placeholder="Enter menu description"
                />
              </div>

              <div className="flex items-center gap-6">
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    defaultChecked={currentMenu?.is_active}
                    className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
                  />
                  <span className="text-sm font-medium text-gray-700">Active</span>
                </label>
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    defaultChecked={currentMenu?.is_default}
                    className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
                  />
                  <span className="text-sm font-medium text-gray-700">Default Menu</span>
                </label>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowEditModal(false)}
                className="px-6 py-3 bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
              >
                Update Menu
              </button>
            </div>
          </div>
        </Modal>

        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          title="Confirm Delete"
          size="max-w-md"
        >
          <div className="p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trash2 className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Delete Menu
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete "<span className="font-semibold">{currentMenu?.name}</span>"? 
                This action cannot be undone.
              </p>
              <div className="flex justify-center gap-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-xl transition-all duration-200"
                >
                  Delete Menu
                </button>
              </div>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default Menus;