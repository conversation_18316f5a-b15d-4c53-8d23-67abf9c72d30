"use client"
import React, { useState, useEffect } from 'react';
import { Calendar, ChevronDown, RefreshCw, Plus, Eye, Edit, Trash2, Clock, User, MapPin, CreditCard, Loader2 } from 'lucide-react';
import { fetchOrders, fetchOrderById, createOrder } from '../../../../../lib/api';

const Orders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState('10 Seconds');
  const [dateFrom, setDateFrom] = useState('07/06/2025');
  const [dateTo, setDateTo] = useState('07/06/2025');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showCreateOrder, setShowCreateOrder] = useState(false);
  const [createOrderLoading, setCreateOrderLoading] = useState(false);

  // Load orders data
  useEffect(() => {
    loadOrders();
  }, [currentPage]);

  // Auto refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const intervalMs = refreshInterval === '10 Seconds' ? 10000 :
                      refreshInterval === '30 Seconds' ? 30000 : 60000;

    const interval = setInterval(() => {
      loadOrders();
    }, intervalMs);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, currentPage]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get token from localStorage
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

      const ordersData = await fetchOrders(currentPage, token);

      // Handle the paginated API response
      if (ordersData && ordersData.data) {
        setOrders(ordersData.data);
        setCurrentPage(ordersData.current_page);
        setTotalPages(ordersData.last_page);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Failed to fetch orders:', err);
      setError('Failed to load orders. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = async (orderId) => {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      const orderDetails = await fetchOrderById(orderId, token);
      setSelectedOrder(orderDetails);
      setShowOrderDetails(true);
    } catch (err) {
      console.error('Failed to fetch order details:', err);
      alert('Failed to load order details');
    }
  };

  // Helper functions
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'preparing':
        return 'bg-orange-100 text-orange-800';
      case 'ready':
        return 'bg-green-100 text-green-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderTypeIcon = (orderType) => {
    switch (orderType?.toLowerCase()) {
      case 'delivery':
        return <MapPin className="w-4 h-4" />;
      case 'pickup':
        return <Clock className="w-4 h-4" />;
      case 'dine-in':
        return <User className="w-4 h-4" />;
      default:
        return <CreditCard className="w-4 h-4" />;
    }
  };

  // Loading state
  if (loading && orders.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center gap-3">
              <Loader2 className="w-8 h-8 animate-spin text-[#FF6500]" />
              <span className="text-lg font-medium text-gray-600">Loading orders...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error && orders.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-red-500 text-lg font-semibold mb-2">Error</div>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={() => loadOrders()}
                className="px-4 py-2 bg-[#FF6500] text-white rounded-lg hover:bg-[#E55A00] transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold text-gray-900">Orders ({orders.length})</h1>
            <div className="flex items-center gap-4">
              {/* Auto Refresh Toggle */}
              <div className="flex items-center gap-3">
                <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={autoRefresh}
                      onChange={(e) => setAutoRefresh(e.target.checked)}
                      className="sr-only"
                    />
                    <div className={`w-12 h-6 rounded-full transition-colors ${
                      autoRefresh ? 'bg-[#FF6500]' : 'bg-gray-300'
                    }`}>
                      <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform ${
                        autoRefresh ? 'translate-x-6' : 'translate-x-0.5'
                      } mt-0.5`}></div>
                    </div>
                  </div>
                  Auto Refresh
                </label>
                <select 
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                >
                  <option>10 Seconds</option>
                  <option>30 Seconds</option>
                  <option>1 Minute</option>
                  <option>5 Minutes</option>
                </select>
                <select className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
                  <option>All</option>
                  <option>Today</option>
                  <option>This Week</option>
                </select>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Today</span>
              <ChevronDown className="w-4 h-4 text-gray-500" />
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <input
                type="text"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              />
            </div>

            <span className="text-sm text-gray-500">To</span>

            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <input
                type="text"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              />
            </div>

            <select className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
              <option>Show All Orders</option>
              <option>Pending Orders</option>
              <option>Completed Orders</option>
              <option>Cancelled Orders</option>
            </select>

            <select className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
              <option>Show All Waiters</option>
              <option>Waiter 1</option>
              <option>Waiter 2</option>
            </select>

            <button
              onClick={() => setShowCreateOrder(true)}
              className="ml-auto bg-[#FF6500] hover:bg-[#E55A00] text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              New Order
            </button>
          </div>
        </div>

        {/* Orders Grid - Redesigned to match screenshot */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {orders.map((order) => (
            <div key={order.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              {/* Order Header - Compact Design */}
              <div className="p-4">
                {/* Top Row: Order Number and Type */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-[#FF6500] rounded-full flex items-center justify-center text-white">
                      {getOrderTypeIcon(order.order_type)}
                    </div>
                    <div>
                      <h3 className="text-sm font-bold text-gray-900">{order.order_number}</h3>
                      <div className="text-xs text-gray-500 capitalize">{order.order_type}</div>
                    </div>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(order.status)}`}>
                    {order.status?.charAt(0).toUpperCase() + order.status?.slice(1)}
                  </span>
                </div>

                {/* Items Count */}
                {order.items && order.items.length > 0 && (
                  <div className="text-xs text-gray-600 mb-2">
                    {order.items.length} item{order.items.length > 1 ? 's' : ''}
                  </div>
                )}

                {/* Order Time */}
                <div className="text-xs text-gray-500 mb-3">
                  {formatDate(order.order_time)}
                </div>

                {/* Price - Large and Bold */}
                <div className="text-xl font-bold text-gray-900 mb-3">
                  LE {parseFloat(order.total_amount || 0).toFixed(2)}
                </div>

                {/* Additional Info */}
                <div className="space-y-1 mb-4">
                  {order.pax && (
                    <div className="text-xs text-gray-600">{order.pax} person{order.pax > 1 ? 's' : ''}</div>
                  )}

                  {order.estimated_ready_time && (
                    <div className="flex items-center gap-1 text-xs text-orange-600">
                      <Clock className="w-3 h-3" />
                      <span>Ready: {formatDate(order.estimated_ready_time)}</span>
                    </div>
                  )}

                  {order.table && (
                    <div className="text-xs text-gray-600">
                      Table: {order.table.table_number}
                    </div>
                  )}

                  {order.customer && (
                    <div className="flex items-center gap-1 text-xs text-gray-600">
                      <User className="w-3 h-3" />
                      <span>{order.customer.first_name} {order.customer.last_name}</span>
                    </div>
                  )}

                  {order.delivery_address && (
                    <div className="flex items-center gap-1 text-xs text-gray-600">
                      <MapPin className="w-3 h-3" />
                      <span className="truncate">{order.delivery_address}</span>
                    </div>
                  )}
                </div>

                {/* Action Buttons - Compact */}
                <div className="flex gap-2">
                  <button
                    onClick={() => handleViewDetails(order.id)}
                    className="flex-1 bg-[#FF6500] hover:bg-[#E55A00] text-white px-3 py-2 rounded text-xs font-medium transition-colors flex items-center justify-center gap-1"
                  >
                    <Eye className="w-3 h-3" />
                    View Details
                  </button>
                  <button className="px-3 py-2 border border-gray-300 rounded text-xs font-medium text-gray-700 hover:bg-gray-100 transition-colors">
                    <Edit className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State (when no orders) */}
        {orders.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <RefreshCw className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-500 mb-6">Try adjusting your filters or date range</p>
            <button
              onClick={() => setShowCreateOrder(true)}
              className="bg-[#FF6500] hover:bg-[#E55A00] text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto"
            >
              <Plus className="w-4 h-4" />
              Create New Order
            </button>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8 flex items-center justify-center gap-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Previous
            </button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = i + 1;
                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      currentPage === pageNum
                        ? 'bg-[#FF6500] text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Next
            </button>
          </div>
        )}

        {/* Order Details Modal */}
        {showOrderDetails && selectedOrder && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900">Order Details</h2>
                  <button
                    onClick={() => setShowOrderDetails(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <span className="text-gray-500 text-xl">×</span>
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Order Number</label>
                    <p className="text-lg font-semibold text-gray-900">{selectedOrder.order_number}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    <p className={`inline-block px-2 py-1 rounded-md text-sm font-medium ${getStatusColor(selectedOrder.status)}`}>
                      {selectedOrder.status?.charAt(0).toUpperCase() + selectedOrder.status?.slice(1)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Order Type</label>
                    <p className="text-gray-900 capitalize">{selectedOrder.order_type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Total Amount</label>
                    <p className="text-lg font-semibold text-gray-900">L.E {parseFloat(selectedOrder.total_amount || 0).toFixed(2)}</p>
                  </div>
                </div>

                {/* Order Items */}
                {selectedOrder.items && selectedOrder.items.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                    <div className="space-y-3">
                      {selectedOrder.items.map((item) => (
                        <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">
                              {item.menu_item?.name || item.menu_item_name || 'Unknown Item'}
                            </p>
                            <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                            {item.special_instructions && (
                              <p className="text-sm text-gray-500 italic">Note: {item.special_instructions}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">L.E {parseFloat(item.total_price || 0).toFixed(2)}</p>
                            <p className="text-sm text-gray-600">L.E {parseFloat(item.unit_price || 0).toFixed(2)} each</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Additional Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <label className="font-medium text-gray-500">Order Time</label>
                    <p className="text-gray-900">{formatDate(selectedOrder.order_time)}</p>
                  </div>
                  {selectedOrder.estimated_ready_time && (
                    <div>
                      <label className="font-medium text-gray-500">Estimated Ready Time</label>
                      <p className="text-gray-900">{formatDate(selectedOrder.estimated_ready_time)}</p>
                    </div>
                  )}
                  {selectedOrder.delivery_address && (
                    <div className="md:col-span-2">
                      <label className="font-medium text-gray-500">Delivery Address</label>
                      <p className="text-gray-900">{selectedOrder.delivery_address}</p>
                    </div>
                  )}
                  {selectedOrder.special_instructions && (
                    <div className="md:col-span-2">
                      <label className="font-medium text-gray-500">Special Instructions</label>
                      <p className="text-gray-900">{selectedOrder.special_instructions}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Create Order Modal */}
        {showCreateOrder && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
              {/* Modal Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900">Create New Order</h2>
                  <button
                    onClick={() => setShowCreateOrder(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <span className="text-gray-500 text-xl">×</span>
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="p-6">
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-[#FF6500] rounded-full flex items-center justify-center mx-auto mb-4">
                    <Plus className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Quick Order Creation</h3>
                  <p className="text-gray-600 mb-6">
                    For full order creation with menu items, please use the dedicated order creation page.
                  </p>

                  <div className="flex gap-3">
                    <button
                      onClick={() => {
                        setShowCreateOrder(false);
                        window.location.href = '/orders';
                      }}
                      className="flex-1 bg-[#FF6500] hover:bg-[#E55A00] text-white px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      Go to Order Creation
                    </button>
                    <button
                      onClick={() => setShowCreateOrder(false)}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Orders;