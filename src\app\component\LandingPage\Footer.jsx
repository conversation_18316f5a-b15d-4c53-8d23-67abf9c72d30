"use client"
import React, { useState } from 'react';
import { Facebook, Twitter, Instagram, Linkedin, Phone, Mail, MapPin } from 'lucide-react';
import Image from 'next/image';

const Footer = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
    phone:''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = () => {
    console.log('Form submitted:', formData);
    // Handle form submission here
    alert('تم إرسال الرسالة بنجاح!');
  };

  return (
    <footer className="bg-gradient-to-br from-gray-50 to-[#FFD5C7]/30 pt-16 pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          
          {/* Brand and Contact Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-6">
              <div className="bg-gradient-to-br from-[#FF6500] to-[#FCB190] p-3 rounded-xl shadow-lg">
                <div className="w-8 h-8 bg-white rounded-md flex items-center justify-center">
                  <Image width={40} height={60} alt='episie' src={"/images/EPISYS.png"} className="text-[#FF6500] font-bold text-lg"/>
                </div>
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-[#FF6500] to-[#FCB190] bg-clip-text text-transparent">
                إيبيسيس
              </h3>
            </div>
            
            <p className="text-gray-600 mb-6 leading-relaxed">
              إيبيسيس هو حل عالمي لإدارة وتوسيع نطاق المطاعم وتطوير الأعمال المطعمية بجميع أنواعها عبر العالم.
            </p>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-gray-600">
                <Phone className="w-5 h-5 text-[#FF6500]" />
                <span>01503135009 هاتف</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-600">
                <Mail className="w-5 h-5 text-[#FF6500]" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-gray-600">
                <MapPin className="w-5 h-5 text-[#FF6500]" />
                <span>شارع دكتور حسن الشريف المنطقة الثامنة - مدينة نصر</span>
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="grid grid-cols-2 gap-8 lg:col-span-2">
            {/* Column 1 */}
            <div>
              <h4 className="text-[#FF6500] font-bold text-lg mb-4">حلول إيبيسيس</h4>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">نظام نقاط البيع</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">إيبيسيس Pay</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">إيبيسيس للمطاعم</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">الطلب الآني</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">إيبيسيس محاسب</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">Online</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">الشعار</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">One</a></li>
              </ul>
            </div>

            {/* Column 2 */}
            <div>
              <h4 className="text-[#FF6500] font-bold text-lg mb-4">إيبيسيس</h4>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">الوظائف</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">وصول شامل</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">دعم</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">مركز الأخبار</a></li>
              </ul>
              
              <h4 className="text-[#FF6500] font-bold text-lg mb-4 mt-8">مصادر إيبيسيس</h4>
              <ul className="space-y-3">
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">مركز الدعم</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">القنوات والأحكام</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#FF6500] transition-colors duration-300">سياسة الخصوصية</a></li>
              </ul>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-1">
            <h4 className="text-[#FF6500] font-bold text-lg mb-4">ابق على اتصال</h4>
            <p className="text-gray-600 mb-6">
              احصل على معلومات حصرية عن إيبيسيس وأحدث المطاعم.
            </p>
            
            <div className="space-y-4">
              <div>
                <input
                  type="text"
                  name="name"
                  placeholder="الاسم *"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent transition-all duration-300"
                />
              </div>
              <div>
                <input
                  type="text"
                  name="phone"
                  placeholder="رقم الهاتف *"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent transition-all duration-300"
                />
              </div>
              
              <div>
                <input
                  type="email"
                  name="email"
                  placeholder="البريد الإلكتروني *"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent transition-all duration-300"
                />
              </div>
              
              <div>
                <textarea
                  name="message"
                  placeholder="الرسالة"
                  rows="4"
                  value={formData.message}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent transition-all duration-300 resize-none"
                ></textarea>
              </div>
              
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="privacy"
                  className="mt-1 h-4 w-4 text-[#FF6500] focus:ring-[#FF6500] border-gray-300 rounded"
                />
                <label htmlFor="privacy" className="text-sm text-gray-600">
                  أوافق على أن يتم جمع بياناتي الشخصية واستخدامها وفقاً لسياسة الخصوصية
                </label>
              </div>
              
              <button
                onClick={handleSubmit}
                className="w-full bg-[#FF6500] text-white py-3 px-6 rounded-lg font-semibold hover:from-[#e55a00] hover:to-[#e5a082] transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
              >
                إرسال الرسالة
              </button>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        {/* <div className="border-t border-gray-200 pt-8 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-4 lg:mb-0">
              <h5 className="text-gray-700 font-semibold mb-3">طرق الدفع المتاحة</h5>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 bg-white p-2 rounded-lg shadow-sm">
                  <img src="https://via.placeholder.com/40x25/0072CE/FFFFFF?text=VISA" alt="Visa" className="h-6" />
                  <img src="https://via.placeholder.com/40x25/EB001B/FFFFFF?text=MC" alt="Mastercard" className="h-6" />
                  <img src="https://via.placeholder.com/40x25/006FCF/FFFFFF?text=AMEX" alt="American Express" className="h-6" />
                </div>
                <div className="bg-white p-2 rounded-lg shadow-sm">
                  <span className="text-[#FF6500] font-bold text-sm">مدى</span>
                </div>
                <div className="bg-white p-2 rounded-lg shadow-sm">
                  <span className="text-orange-500 font-bold text-sm">STC Pay</span>
                </div>
              </div>
            </div>
            
            <div className="text-center lg:text-right">
              <p className="text-gray-600 mb-2">سيتم لإرسال فاتورة الرقم 13564 الطلب الآني</p>
              <p className="text-2xl font-bold text-[#FF6500]">15798 هاتف</p>
            </div>
          </div>
        </div> */}

        {/* Bottom Footer */}
        <div className="border-t border-gray-200 pt-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-4 lg:mb-0">
              <p className="text-gray-600 text-sm">
                © 2025 إيبيسيس - جميع الحقوق محفوظة
              </p>
            </div>
            
            <div className="flex items-center space-x-6">
              <a href="https://www.facebook.com/episys.eg" className="text-gray-400 hover:text-[#FF6500] transition-colors duration-300">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="https://x.com/" className="text-gray-400 hover:text-[#FF6500] transition-colors duration-300">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="https://www.instagram.com/" className="text-gray-400 hover:text-[#FF6500] transition-colors duration-300">
                <Instagram className="w-5 h-5" />
              </a>
           
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;