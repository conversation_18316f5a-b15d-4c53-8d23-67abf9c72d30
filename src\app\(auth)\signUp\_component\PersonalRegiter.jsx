"use client"
import React, { useState } from 'react'
import { Eye, EyeOff, User, Mail, Lock, Phone, Check, AlertCircle } from 'lucide-react'
import BrandingRegister from './BrandingRegister'

const PersonalRegister = ({ setStep }) => {
  const [formData, setFormData] = useState({
    restaurantName: '',
    fullName: '',
    email: '',
    password: '',
    countryCode: '+20',
    phoneNumber: '',
    acceptTerms: false
  })
  
  const [showPassword, setShowPassword] = useState(false)
  const [showCountryDropdown, setShowCountryDropdown] = useState(false)
  const [errors, setErrors] = useState({})
  const [showErrors, setShowErrors] = useState(false)
  
  const countries = [
    { code: '+20', name: 'مصر', flag: '🇪🇬' },
    { code: '+966', name: 'السعودية', flag: '🇸🇦' },
    { code: '+971', name: 'الإمارات', flag: '🇦🇪' },
    { code: '+965', name: 'الكويت', flag: '🇰🇼' },
    { code: '+974', name: 'قطر', flag: '🇶🇦' },
    { code: '+973', name: 'البحرين', flag: '🇧🇭' },
    { code: '+968', name: 'عمان', flag: '🇴🇲' },
    { code: '+962', name: 'الأردن', flag: '🇯🇴' },
    { code: '+961', name: 'لبنان', flag: '🇱🇧' }
  ]

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.restaurantName.trim()) {
      newErrors.restaurantName = 'اسم المطعم مطلوب'
    }
    
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'الاسم الكامل مطلوب'
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }
    
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'رقم الهاتف مطلوب'
    } else if (!/^\d{8,15}$/.test(formData.phoneNumber.replace(/\s/g, ''))) {
      newErrors.phoneNumber = 'رقم الهاتف غير صحيح'
    }
    
    if (!formData.password) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 8) {
      newErrors.password = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
    }
    
    if (!formData.acceptTerms) {
      newErrors.acceptTerms = 'يجب الموافقة على الشروط والأحكام'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    })
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      })
    }
  }

  const handleCountrySelect = (country) => {
    setFormData({
      ...formData,
      countryCode: country.code
    })
    setShowCountryDropdown(false)
  }

  const handleStep = () => {
    setShowErrors(true)
    if (validateForm()) {
      console.log("Form Data:", formData)
      setStep(2)
    }
  }

  const selectedCountry = countries.find(country => country.code === formData.countryCode)

  const getInputBorderClass = (fieldName) => {
    if (showErrors && errors[fieldName]) {
      return 'border-red-300 focus:ring-red-500 focus:border-red-500'
    }
    return 'border-gray-200 focus:ring-[#FF6500] focus:border-transparent'
  }

  return (
    <div className="min-h-screen   flex items-center justify-center p-4">
       
      <div className="w-full max-w-6xl flex bg-white rounded-3xl shadow-2xl overflow-hidden">
        
        {/* Right Side - Form */}
        <div className="w-full lg:w-1/2 p-8 lg:p-12">
          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-2">إنشاء حساب جديد</h2>
              <p className="text-gray-600">الخاص بك EPISYS انضم إلى عائلة</p>
            </div>

            <div className="space-y-6">
              {/* Restaurant Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center text-gray-700 gap-1">
                  اسم المطعم 
                  <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    name="restaurantName"
                    value={formData.restaurantName}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 pr-12 bg-gray-50 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 text-right ${getInputBorderClass('restaurantName')}`}
                    placeholder="مطعم الأصالة"
                  />
                  <User className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>
                {showErrors && errors.restaurantName && (
                  <div className="flex items-center gap-1 text-red-500 text-sm">
                    <AlertCircle size={16} />
                    <span>{errors.restaurantName}</span>
                  </div>
                )}
              </div>

              {/* Full Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block text-right">
                  اسمك الكامل <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 pr-12 bg-gray-50 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 text-right ${getInputBorderClass('fullName')}`}
                    placeholder="أحمد محمد علي"
                  />
                  <User className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>
                {showErrors && errors.fullName && (
                  <div className="flex items-center gap-1 text-red-500 text-sm">
                    <AlertCircle size={16} />
                    <span>{errors.fullName}</span>
                  </div>
                )}
              </div>

              {/* Email */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block text-right">
                  أدخل بريدك الإلكتروني <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 pr-12 bg-gray-50 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 ${getInputBorderClass('email')}`}
                    placeholder="<EMAIL>"
                  />
                  <Mail className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>
                {showErrors && errors.email && (
                  <div className="flex items-center gap-1 text-red-500 text-sm">
                    <AlertCircle size={16} />
                    <span>{errors.email}</span>
                  </div>
                )}
              </div>

              {/* Phone Number */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block text-right">
                  رقم الهاتف <span className="text-red-500">*</span>
                </label>
                <div className="flex gap-3">
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    required
                    className={`flex-1 px-4 py-3 bg-gray-50 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 text-right ${getInputBorderClass('phoneNumber')}`}
                    placeholder="123456789"
                  />
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setShowCountryDropdown(!showCountryDropdown)}
                      className="flex items-center gap-2 px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent transition-all duration-200 min-w-[120px]"
                    >
                      <span className="text-lg">{selectedCountry?.flag}</span>
                      <span className="text-sm font-medium">{selectedCountry?.code}</span>
                    </button>
                    
                    {showCountryDropdown && (
                      <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-xl shadow-lg z-10 max-h-60 overflow-y-auto">
                        {countries.map((country) => (
                          <button
                            key={country.code}
                            type="button"
                            onClick={() => handleCountrySelect(country)}
                            className="w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 text-right"
                          >
                            <span className="text-lg">{country.flag}</span>
                            <span className="text-sm">{country.name}</span>
                            <span className="text-sm text-gray-500 mr-auto">{country.code}</span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                {showErrors && errors.phoneNumber && (
                  <div className="flex items-center gap-1 text-red-500 text-sm">
                    <AlertCircle size={16} />
                    <span>{errors.phoneNumber}</span>
                  </div>
                )}
              </div>

              {/* Password */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 block text-right">
                  كلمة المرور <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 pr-12 pl-12 bg-gray-50 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 text-right ${getInputBorderClass('password')}`}
                    placeholder="••••••••"
                  />
                  <Lock className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                {showErrors && errors.password && (
                  <div className="flex items-center gap-1 text-red-500 text-sm">
                    <AlertCircle size={16} />
                    <span>{errors.password}</span>
                  </div>
                )}
              </div>

              {/* Password Requirements */}
              <div className="text-xs text-gray-500 text-right">
                يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل
              </div>

              {/* Terms and Conditions */}
              <div className="space-y-2">
                <div className="flex items-center justify-end gap-2">
                  <label className="text-sm text-gray-600 cursor-pointer">
                    أوافق على <span className="text-[#FF6500] hover:underline">شروط الخدمة</span> و <span className="text-[#FF6500] hover:underline">سياسة الخصوصية</span> <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="checkbox"
                    name="acceptTerms"
                    checked={formData.acceptTerms}
                    onChange={handleInputChange}
                    required
                    className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
                  />
                </div>
                {showErrors && errors.acceptTerms && (
                  <div className="flex items-center gap-1 text-red-500 text-sm justify-end">
                    <span>{errors.acceptTerms}</span>
                    <AlertCircle size={16} />
                  </div>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="button"
                onClick={handleStep}
                className="w-full bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white py-4 px-6 rounded-xl font-semibold text-lg hover:from-[#e55a00] hover:to-[#e67a2d] focus:outline-none focus:ring-4 focus:ring-[#FF6500]/20 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg"
              >
                المتابعة: تفاصيل الفرع
              </button>

              {/* Login Link */}
              <div className="text-center">
                <p className="text-sm text-gray-600">
                  لديك حساب بالفعل؟ <a href="/login" className="text-[#FF6500] hover:underline font-medium">تسجيل الدخول</a>
                </p>
              </div>
            </div>
          </div>
        </div>
        <BrandingRegister />
      </div>
    </div>
  )
}

export default PersonalRegister