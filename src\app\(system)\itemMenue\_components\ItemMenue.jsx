// "use client"
// import React, { useState, useEffect } from 'react';
// import { Search, Plus, Edit, Trash2, Filter, X } from 'lucide-react';

// const ItemMenue = () => {
//   // Sample initial data
//   const [categories, setCategories] = useState([
//     { id: 1, categoryName: 'Beverages' },
//     { id: 2, categoryName: 'Appetizers' },
//     { id: 3, categoryName: 'Main Course' },
//     { id: 4, categoryName: 'Desserts' },
//     { id: 5, categoryName: 'Salads' },
//     { id: 6, categoryName: 'Soups' },
//   ]);

//   const [filteredCategories, setFilteredCategories] = useState(categories);
//   const [searchTerm, setSearchTerm] = useState('');
//   const [showAddModal, setShowAddModal] = useState(false);
//   const [showEditModal, setShowEditModal] = useState(false);
//   const [showDeleteModal, setShowDeleteModal] = useState(false);
//   const [showFilterModal, setShowFilterModal] = useState(false);
//   const [currentCategory, setCurrentCategory] = useState(null);
//   const [newCategoryName, setNewCategoryName] = useState('');
//   const [filterOptions, setFilterOptions] = useState({
//     sortBy: 'id',
//     sortOrder: 'asc'
//   });

//   // Update filtered categories when search term or categories change
//   useEffect(() => {
//     let filtered = categories.filter(category =>
//       category.categoryName.toLowerCase().includes(searchTerm.toLowerCase())
//     );

//     // Apply sorting
//     filtered.sort((a, b) => {
//       const aValue = filterOptions.sortBy === 'id' ? a.id : a.categoryName.toLowerCase();
//       const bValue = filterOptions.sortBy === 'id' ? b.id : b.categoryName.toLowerCase();
      
//       if (filterOptions.sortOrder === 'asc') {
//         return aValue > bValue ? 1 : -1;
//       } else {
//         return aValue < bValue ? 1 : -1;
//       }
//     });

//     setFilteredCategories(filtered);
//   }, [categories, searchTerm, filterOptions]);

//   // Add new category
//   const handleAdd = () => {
//     if (newCategoryName.trim()) {
//       const newId = Math.max(...categories.map(c => c.id)) + 1;
//       setCategories([...categories, { id: newId, categoryName: newCategoryName.trim() }]);
//       setNewCategoryName('');
//       setShowAddModal(false);
//     }
//   };

//   // Edit category
//   const handleEdit = (category) => {
//     setCurrentCategory(category);
//     setNewCategoryName(category.categoryName);
//     setShowEditModal(true);
//   };

//   const handleUpdate = () => {
//     if (newCategoryName.trim() && currentCategory) {
//       setCategories(categories.map(cat => 
//         cat.id === currentCategory.id 
//           ? { ...cat, categoryName: newCategoryName.trim() }
//           : cat
//       ));
//       setNewCategoryName('');
//       setCurrentCategory(null);
//       setShowEditModal(false);
//     }
//   };

//   // Delete category
//   const handleDelete = (category) => {
//     setCurrentCategory(category);
//     setShowDeleteModal(true);
//   };

//   const confirmDelete = () => {
//     if (currentCategory) {
//       setCategories(categories.filter(cat => cat.id !== currentCategory.id));
//       setCurrentCategory(null);
//       setShowDeleteModal(false);
//     }
//   };

//   // Modal component
//   const Modal = ({ isOpen, onClose, title, children }) => {
//     if (!isOpen) return null;

//     return (
//       <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//         <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
//           <div className="flex justify-between items-center mb-4">
//             <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
//             <button
//               onClick={onClose}
//               className="text-gray-500 hover:text-gray-700"
//             >
//               <X size={20} />
//             </button>
//           </div>
//           {children}
//         </div>
//       </div>
//     );
//   };

//   return (
//     <div className="p-6 bg-gray-50 min-h-screen">
//       <div className="max-w-6xl mx-auto">
//         <h1 className="text-3xl font-bold text-gray-800 mb-6">Item Menu Categories</h1>
        
//         {/* Action Bar */}
//         <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
//           <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
//             <div className="flex flex-col sm:flex-row gap-3 flex-1">
//               {/* Search */}
//               <div className="relative flex-1">
//                 <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
//                 <input
//                   type="text"
//                   placeholder="Search categories..."
//                   value={searchTerm}
//                   onChange={(e) => setSearchTerm(e.target.value)}
//                 className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg transition-colors"
//                   style={{ 
//                     focusRingColor: '#FF6500',
//                     focusBorderColor: '#FF6500'
//                   }}
//                   onFocus={(e) => {
//                     e.target.style.outline = 'none';
//                     e.target.style.borderColor = '#FF6500';
//                     e.target.style.boxShadow = '0 0 0 2px rgba(255, 101, 0, 0.2)';
//                   }}
//                   onBlur={(e) => {
//                     e.target.style.borderColor = '#D1D5DB';
//                     e.target.style.boxShadow = 'none';
//                   }}
//                 />
//               </div>
              
//               {/* Filter Button */}
//               <button
//                 onClick={() => setShowFilterModal(true)}
//                 className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
//               >
//                 <Filter size={18} />
//                 Filter
//               </button>
//             </div>
            
//             {/* Add Button */}
//             <button
//               onClick={() => setShowAddModal(true)}
//               className="flex items-center gap-2 px-4 py-2 text-white rounded-lg transition-colors"
//               style={{ backgroundColor: '#FF6500' }}
//               onMouseEnter={(e) => e.target.style.backgroundColor = '#E55A00'}
//               onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6500'}
//             >
//               <Plus size={18} />
//               Add Category
//             </button>
//           </div>
//         </div>

//         {/* Data Grid Table */}
//         <div className="bg-white rounded-lg shadow-sm overflow-hidden">
//           <div className="overflow-x-auto">
//             <table className="w-full">
//               <thead className="bg-gray-50">
//                 <tr>
//                   <th className="px-6 py-3  text-xs font-medium text-gray-500 uppercase tracking-wider">
//                     ID
//                   </th>
//                   <th className="px-6 py-3  text-xs font-medium text-gray-500 uppercase tracking-wider">
//                     Category Name
//                   </th>
//                   <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
//                     Actions
//                   </th>
//                 </tr>
//               </thead>
//               <tbody className="bg-white divide-y divide-gray-200">
//                 {filteredCategories.length > 0 ? (
//                   filteredCategories.map((category, index) => (
//                     <tr key={category.id} className="hover:bg-gray-50">
//                       <td className="px-6 py-4 flex items-center justify-center whitespace-nowrap text-sm font-medium text-gray-900">
//                         {category.id}
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap text-sm  text-center text-gray-900 ">
//                         {category.categoryName}
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap text-center">
//                         <div className="flex items-center justify-center gap-2">
//                           <button
//                             onClick={() => handleEdit(category)}
//                             className="p-2 text-white rounded-lg transition-colors"
//                             style={{ backgroundColor: '#FF6500' }}
//                             onMouseEnter={(e) => e.target.style.backgroundColor = '#E55A00'}
//                             onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6500'}
//                             title="Edit"
//                           >
//                             <Edit size={16} />
//                           </button>
//                           <button
//                             onClick={() => handleDelete(category)}
//                             className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
//                             title="Delete"
//                           >
//                             <Trash2 size={16} />
//                           </button>
//                         </div>
//                       </td>
//                     </tr>
//                   ))
//                 ) : (
//                   <tr>
//                     <td colSpan="3" className="px-6 py-12 text-center text-gray-500">
//                       No categories found
//                     </td>
//                   </tr>
//                 )}
//               </tbody>
//             </table>
//           </div>
//         </div>

//         {/* Results Count */}
//         <div className="mt-4 text-sm text-gray-600">
//           Showing {filteredCategories.length} of {categories.length} categories
//         </div>

//         {/* Add Modal */}
//         <Modal
//           isOpen={showAddModal}
//           onClose={() => {
//             setShowAddModal(false);
//             setNewCategoryName('');
//           }}
//           title="Add New Category"
//         >
//           <div className="space-y-4">
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">
//                 Category Name
//               </label>
//               <input
//                 type="text"
//                 value={newCategoryName}
//                 onChange={(e) => setNewCategoryName(e.target.value)}
//                 placeholder="Enter category name"
//                 className="w-full px-3 py-2 border border-gray-300 rounded-lg transition-colors"
//                 onFocus={(e) => {
//                   e.target.style.outline = 'none';
//                   e.target.style.borderColor = '#FF6500';
//                   e.target.style.boxShadow = '0 0 0 2px rgba(255, 101, 0, 0.2)';
//                 }}
//                 onBlur={(e) => {
//                   e.target.style.borderColor = '#D1D5DB';
//                   e.target.style.boxShadow = 'none';
//                 }}
//               />
//             </div>
//             <div className="flex gap-3 justify-end">
//               <button
//                 onClick={() => {
//                   setShowAddModal(false);
//                   setNewCategoryName('');
//                 }}
//                 className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={handleAdd}
//                 className="px-4 py-2 text-white rounded-lg transition-colors"
//                 style={{ backgroundColor: '#FF6500' }}
//                 onMouseEnter={(e) => e.target.style.backgroundColor = '#E55A00'}
//                 onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6500'}
//               >
//                 Add Category
//               </button>
//             </div>
//           </div>
//         </Modal>

//         {/* Edit Modal */}
//         <Modal
//           isOpen={showEditModal}
//           onClose={() => {
//             setShowEditModal(false);
//             setCurrentCategory(null);
//             setNewCategoryName('');
//           }}
//           title="Edit Category"
//         >
//           <div className="space-y-4">
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">
//                 Category Name
//               </label>
//               <input
//                 type="text"
//                 value={newCategoryName}
//                 onChange={(e) => setNewCategoryName(e.target.value)}
//                 placeholder="Enter category name"
//                 className="w-full px-3 py-2 border border-gray-300 rounded-lg transition-colors"
//                 onFocus={(e) => {
//                   e.target.style.outline = 'none';
//                   e.target.style.borderColor = '#FF6500';
//                   e.target.style.boxShadow = '0 0 0 2px rgba(255, 101, 0, 0.2)';
//                 }}
//                 onBlur={(e) => {
//                   e.target.style.borderColor = '#D1D5DB';
//                   e.target.style.boxShadow = 'none';
//                 }}
//               />
//             </div>
//             <div className="flex gap-3 justify-end">
//               <button
//                 onClick={() => {
//                   setShowEditModal(false);
//                   setCurrentCategory(null);
//                   setNewCategoryName('');
//                 }}
//                 className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={handleUpdate}
//                 className="px-4 py-2 text-white rounded-lg transition-colors"
//                 style={{ backgroundColor: '#FF6500' }}
//                 onMouseEnter={(e) => e.target.style.backgroundColor = '#E55A00'}
//                 onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6500'}
//               >
//                 Update Category
//               </button>
//             </div>
//           </div>
//         </Modal>

//         {/* Delete Confirmation Modal */}
//         <Modal
//           isOpen={showDeleteModal}
//           onClose={() => {
//             setShowDeleteModal(false);
//             setCurrentCategory(null);
//           }}
//           title="Delete Category"
//         >
//           <div className="space-y-4">
//             <p className="text-gray-600">
//               Are you sure you want to delete the category "{currentCategory?.categoryName}"? This action cannot be undone.
//             </p>
//             <div className="flex gap-3 justify-end">
//               <button
//                 onClick={() => {
//                   setShowDeleteModal(false);
//                   setCurrentCategory(null);
//                 }}
//                 className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
//               >
//                 Cancel
//               </button>
//               <button
//                 onClick={confirmDelete}
//                 className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
//               >
//                 Delete
//               </button>
//             </div>
//           </div>
//         </Modal>

//         {/* Filter Modal */}
//         <Modal
//           isOpen={showFilterModal}
//           onClose={() => setShowFilterModal(false)}
//           title="Filter Options"
//         >
//           <div className="space-y-4">
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">
//                 Sort By
//               </label>
//               <select
//                 value={filterOptions.sortBy}
//                 onChange={(e) => setFilterOptions({...filterOptions, sortBy: e.target.value})}
//                 className="w-full px-3 py-2 border border-gray-300 rounded-lg transition-colors"
//                 onFocus={(e) => {
//                   e.target.style.outline = 'none';
//                   e.target.style.borderColor = '#FF6500';
//                   e.target.style.boxShadow = '0 0 0 2px rgba(255, 101, 0, 0.2)';
//                 }}
//                 onBlur={(e) => {
//                   e.target.style.borderColor = '#D1D5DB';
//                   e.target.style.boxShadow = 'none';
//                 }}
//               >
//                 <option value="id">ID</option>
//                 <option value="categoryName">Category Name</option>
//               </select>
//             </div>
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">
//                 Sort Order
//               </label>
//               <select
//                 value={filterOptions.sortOrder}
//                 onChange={(e) => setFilterOptions({...filterOptions, sortOrder: e.target.value})}
//                 className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
//               >
//                 <option value="asc">Ascending</option>
//                 <option value="desc">Descending</option>
//               </select>
//             </div>
//             <div className="flex gap-3 justify-end">
//               <button
//                 onClick={() => setShowFilterModal(false)}
//                 className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
//               >
//                 Close
//               </button>
//             </div>
//           </div>
//         </Modal>
//       </div>
//     </div>
//   );
// };

// export default ItemMenue;
"use client"
import React, { useState, useEffect } from 'react';
import { Search, Plus, Edit, Trash2, Filter, X, Eye, DollarSign, Clock, Zap } from 'lucide-react';
import { fetchMenuItems, fetchCategories } from '../../../../../lib/api';

const ItemMenue = () => {
  const [menuItems, setMenuItems] = useState([]);
  const [categories, setCategories] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [currentItem, setCurrentItem] = useState(null);
  const [filterOptions, setFilterOptions] = useState({
    category: 'all',
    spiceLevel: 'all',
    dietaryInfo: 'all',
    isActive: 'all',
    sortBy: 'name',
    sortOrder: 'asc'
  });

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [itemsData, categoriesData] = await Promise.all([
          fetchMenuItems(),
          fetchCategories()
        ]);
        setMenuItems(itemsData || []);
        setCategories(categoriesData || []);
      } catch (error) {
        console.error('Error loading data:', error);
        // Fallback to empty arrays if API fails
        setMenuItems([]);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Update filtered items when search term, items, or filters change
  useEffect(() => {
    let filtered = menuItems.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          item.code.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = filterOptions.category === 'all' || 
                            item.category_id.toString() === filterOptions.category;
      
      const matchesSpiceLevel = filterOptions.spiceLevel === 'all' || 
                              (item.is_spicy && filterOptions.spiceLevel === 'spicy') ||
                              (!item.is_spicy && filterOptions.spiceLevel === 'not_spicy');
      
      const matchesDietary = filterOptions.dietaryInfo === 'all' || 
                           (item.dietary_info && item.dietary_info.includes(filterOptions.dietaryInfo));
      
      const matchesActive = filterOptions.isActive === 'all' || 
                          item.is_active.toString() === filterOptions.isActive;

      return matchesSearch && matchesCategory && matchesSpiceLevel && matchesDietary && matchesActive;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;
      
      switch (filterOptions.sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'price':
          aValue = parseFloat(a.base_price);
          bValue = parseFloat(b.base_price);
          break;
        case 'category':
          aValue = a.category?.name?.toLowerCase() || '';
          bValue = b.category?.name?.toLowerCase() || '';
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        default:
          aValue = a.id;
          bValue = b.id;
      }
      
      if (filterOptions.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredItems(filtered);
  }, [menuItems, searchTerm, filterOptions]);

  // Get category name by ID
  const getCategoryName = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Unknown';
  };

  // Format price
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(parseFloat(price));
  };

  // Handle view item details
  const handleView = (item) => {
    setCurrentItem(item);
    setShowViewModal(true);
  };

  // Handle edit (placeholder)
  const handleEdit = (item) => {
    setCurrentItem(item);
    setShowEditModal(true);
  };

  // Handle delete (placeholder)
  const handleDelete = (item) => {
    setCurrentItem(item);
    setShowDeleteModal(true);
  };

  // Modal component
  const Modal = ({ isOpen, onClose, title, children, size = 'md' }) => {
    if (!isOpen) return null;

    const sizeClasses = {
      sm: 'max-w-md',
      md: 'max-w-lg',
      lg: 'max-w-2xl',
      xl: 'max-w-4xl'
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className={`bg-white rounded-lg w-full ${sizeClasses[size]} max-h-[90vh] overflow-y-auto`}>
          <div className="flex justify-between items-center p-6 border-b">
            <h3 className="text-xl font-semibold text-gray-800">{title}</h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={24} />
            </button>
          </div>
          <div className="p-6">
            {children}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-xl text-gray-600">Loading menu items...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-6">Menu Items</h1>
        
        {/* Action Bar */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-3 flex-1">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search items..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-colors"
                />
              </div>
              
              {/* Filter Button */}
              <button
                onClick={() => setShowFilterModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <Filter size={18} />
                Filter
              </button>
            </div>
            
            {/* Add Button */}
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              <Plus size={18} />
              Add Item
            </button>
          </div>
        </div>

        {/* Data Grid Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Info
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredItems.length > 0 ? (
                  filteredItems.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                              <span className="text-gray-600 font-medium">
                                {item.name.charAt(0)}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {item.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.code}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getCategoryName(item.category_id)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatPrice(item.base_price)}
                        </div>
                        {item.cost_price && (
                          <div className="text-sm text-gray-500">
                            Cost: {formatPrice(item.cost_price)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex flex-wrap gap-1">
                          {item.prep_time_minutes > 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                              <Clock size={12} className="mr-1" />
                              {item.prep_time_minutes}m
                            </span>
                          )}
                          {item.is_spicy && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                              <Zap size={12} className="mr-1" />
                              Spicy
                            </span>
                          )}
                          {item.calories && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                              {item.calories} cal
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          item.is_active 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {item.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex items-center justify-center gap-2">
                          <button
                            onClick={() => handleView(item)}
                            className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                            title="View Details"
                          >
                            <Eye size={16} />
                          </button>
                          <button
                            onClick={() => handleEdit(item)}
                            className="p-2 text-white bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors"
                            title="Edit"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(item)}
                            className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                            title="Delete"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className="px-6 py-12 text-center text-gray-500">
                      {searchTerm ? 'No items found matching your search' : 'No menu items available'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-4 text-sm text-gray-600">
          Showing {filteredItems.length} of {menuItems.length} items
        </div>

        {/* View Details Modal */}
        <Modal
          isOpen={showViewModal}
          onClose={() => {
            setShowViewModal(false);
            setCurrentItem(null);
          }}
          title="Item Details"
          size="lg"
        >
          {currentItem && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Basic Information</h4>
                  <div className="space-y-2">
                    <p><span className="font-medium">Name:</span> {currentItem.name}</p>
                    <p><span className="font-medium">Code:</span> {currentItem.code}</p>
                    <p><span className="font-medium">Category:</span> {getCategoryName(currentItem.category_id)}</p>
                    <p><span className="font-medium">Base Price:</span> {formatPrice(currentItem.base_price)}</p>
                    <p><span className="font-medium">Cost Price:</span> {formatPrice(currentItem.cost_price)}</p>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Additional Info</h4>
                  <div className="space-y-2">
                    <p><span className="font-medium">Prep Time:</span> {currentItem.prep_time_minutes} minutes</p>
                    <p><span className="font-medium">Calories:</span> {currentItem.calories || 'N/A'}</p>
                    <p><span className="font-medium">Spicy:</span> {currentItem.is_spicy ? 'Yes' : 'No'}</p>
                    <p><span className="font-medium">Featured:</span> {currentItem.is_featured ? 'Yes' : 'No'}</p>
                    <p><span className="font-medium">Active:</span> {currentItem.is_active ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </div>

              {currentItem.description && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                  <p className="text-gray-700">{currentItem.description}</p>
                </div>
              )}

              {currentItem.allergens && currentItem.allergens.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Allergens</h4>
                  <div className="flex flex-wrap gap-2">
                    {currentItem.allergens.map((allergen, index) => (
                      <span key={index} className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                        {allergen}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {currentItem.dietary_info && currentItem.dietary_info.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Dietary Information</h4>
                  <div className="flex flex-wrap gap-2">
                    {currentItem.dietary_info.map((diet, index) => (
                      <span key={index} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        {diet}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {currentItem.nutritional_info && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Nutritional Information</h4>
                  <div className="grid grid-cols-3 gap-4">
                    {Object.entries(currentItem.nutritional_info).map(([key, value]) => (
                      <div key={key} className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="font-medium capitalize">{key}</div>
                        <div className="text-sm text-gray-600">{value}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal>

        {/* Filter Modal */}
        <Modal
          isOpen={showFilterModal}
          onClose={() => setShowFilterModal(false)}
          title="Filter Options"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  value={filterOptions.category}
                  onChange={(e) => setFilterOptions({...filterOptions, category: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-200"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id.toString()}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Spice Level
                </label>
                <select
                  value={filterOptions.spiceLevel}
                  onChange={(e) => setFilterOptions({...filterOptions, spiceLevel: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-200"
                >
                  <option value="all">All</option>
                  <option value="spicy">Spicy</option>
                  <option value="not_spicy">Not Spicy</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={filterOptions.isActive}
                  onChange={(e) => setFilterOptions({...filterOptions, isActive: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-200"
                >
                  <option value="all">All</option>
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort By
                </label>
                <select
                  value={filterOptions.sortBy}
                  onChange={(e) => setFilterOptions({...filterOptions, sortBy: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-200"
                >
                  <option value="name">Name</option>
                  <option value="price">Price</option>
                  <option value="category">Category</option>
                  <option value="created_at">Created Date</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort Order
              </label>
              <select
                value={filterOptions.sortOrder}
                onChange={(e) => setFilterOptions({...filterOptions, sortOrder: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-200"
              >
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>

            <div className="flex gap-3 justify-end pt-4">
              <button
                onClick={() => setFilterOptions({
                  category: 'all',
                  spiceLevel: 'all',
                  dietaryInfo: 'all',
                  isActive: 'all',
                  sortBy: 'name',
                  sortOrder: 'asc'
                })}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Reset
              </button>
              <button
                onClick={() => setShowFilterModal(false)}
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </Modal>

        {/* Add Modal (Placeholder) */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Add New Menu Item"
          size="lg"
        >
          <div className="space-y-4">
            <p className="text-gray-600">Add item functionality will be implemented later.</p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowAddModal(false)}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </Modal>

        {/* Edit Modal (Placeholder) */}
        <Modal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setCurrentItem(null);
          }}
          title="Edit Menu Item"
          size="lg"
        >
          <div className="space-y-4">
            <p className="text-gray-600">Edit functionality will be implemented later.</p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setCurrentItem(null);
                }}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </Modal>

        {/* Delete Modal (Placeholder) */}
        <Modal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setCurrentItem(null);
          }}
          title="Delete Menu Item"
        >
          <div className="space-y-4">
            <p className="text-gray-600">
              Delete functionality will be implemented later.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setCurrentItem(null);
                }}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default ItemMenue;