"use client"
import React, { useState, useEffect } from 'react';
import { Search, Bell, ShoppingCart, Star, Heart, Plus } from 'lucide-react';

const GroceryApp = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [favorites, setFavorites] = useState(new Set());

  const deals = [
    {
      id: 1,
      title: "Fresh Vegetables & Fruits",
      discount: "25%",
      image: "🥕🥬🍅",
      color: "from-orange-500 to-red-500",
      customStyle: "linear-gradient(135deg, #FF6500, #FF8533)"
    },
    {
      id: 2,
      title: "Organic Products",
      discount: "30%",
      image: "🥑🌿🥒",
      color: "from-green-500 to-emerald-500",
      customStyle: "linear-gradient(135deg, #10B981, #059669)"
    },
    {
      id: 3,
      title: "Fresh Bakery Items",
      discount: "20%",
      image: "🥖🥐🍞",
      color: "from-amber-500 to-yellow-500",
      customStyle: "linear-gradient(135deg, #F59E0B, #EAB308)"
    },
    {
      id: 4,
      title: "Fresh Bakery Items",
      discount: "20%",
      image: "🥖🥐🍞",
      color: "from-amber-500 to-yellow-500",
      customStyle: "linear-gradient(135deg, #F59E0B, #EAB308)"
    }
  ];

  const categories = [
    { name: "Veggies", icon: "🥬", color: "bg-green-500", items: "120+ items" },
    { name: "Fruits", icon: "🍎", color: "bg-red-500", items: "80+ items" },
    { name: "Bakery", icon: "🥖", color: "#FF6500", items: "45+ items" },
    { name: "Dairy", icon: "🥛", color: "bg-blue-500", items: "35+ items" },
    { name: "Meat", icon: "🥩", color: "bg-red-600", items: "25+ items" },
    { name: "Snacks", icon: "🍿", color: "bg-purple-500", items: "60+ items" }
  ];


  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % deals.length);
    }, 4000);
    return () => clearInterval(timer);
  }, []);

 

  return (
    <div className="min-h-screen bg-gray-50 pb-20 border border-red-500">
      {/* Header */}
      <div className="bg-primary sticky w-full top-0 z-50 shadow-lg" style={{backgroundColor: '#FF6500'}}>
        <div className="px-6 py-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-md">
                <span className="font-bold text-xl" style={{color: '#FF6500'}}>H</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">Discover the</h1>
                <p className="text-lg font-medium text-white/90">Healthy With Us</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                  <Bell className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white"></div>
              </div>
              <div className="relative">
                <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center">
                  <ShoppingCart className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
                  <span className="text-sm font-bold" style={{color: '#FF6500'}}>3</span>
                </div>
              </div>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Discover the Healthy With Us"
              className="w-full pl-12 pr-4 py-4 bg-white/95 backdrop-blur-sm rounded-3xl text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:bg-white transition-all shadow-md"
              style={{'--tw-ring-color': '#FF6500'}}
              onFocus={(e) => e.target.style.boxShadow = '0 0 0 2px rgba(255, 101, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.1)'}
              onBlur={(e) => e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)'}
            />
          </div>
        </div>
      </div>

      {/* Hero Slider */}
      <div className="px-6 mt-6">
        <div className="relative overflow-hidden rounded-3xl h-48 shadow-lg">
          {deals.map((deal, index) => (
            <div
              key={deal.id}
              className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
                index === currentSlide ? 'translate-x-0' : 
                index < currentSlide ? '-translate-x-full' : 'translate-x-full'
              }`}
            >
              <div className={`h-full rounded-3xl p-6 flex items-center justify-between text-white relative overflow-hidden`}
                   style={{background: deal.customStyle}}>
                <div className="absolute inset-0 bg-gradient-to-r from-black/10 to-transparent rounded-3xl"></div>
                <div className="relative z-10">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-3 inline-block">
                    <span className="text-sm font-medium">Discount</span>
                  </div>
                  <div className="text-4xl font-bold mb-2">{deal.discount}</div>
                  <div className="text-sm opacity-90 mb-4">{deal.title}</div>
                  <button className="bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-2xl text-sm font-medium hover:bg-white/30 transition-all duration-300 border border-white/20">
                    See Detail
                  </button>
                </div>
                <div className="text-6xl opacity-80">{deal.image}</div>
              </div>
            </div>
          ))}
          
          {/* Slider Indicators */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {deals.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`h-2 rounded-full transition-all duration-300 ${
                  index === currentSlide ? 'bg-white w-8' : 'bg-white/50 w-2'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="px-6 mt-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Categories</h2>
          <button className="font-medium hover:text-orange-700 transition-colors"
                  style={{color: '#FF6500'}}>
            See all
          </button>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          {categories.map((category, index) => (
            <div
              key={index}
              className="bg-white rounded-3xl p-6 shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer"
            >
              <div className={`w-14 h-14 ${category.color.startsWith('#') ? '' : category.color} rounded-3xl flex items-center justify-center mb-4 mx-auto`}
                   style={category.color.startsWith('#') ? {backgroundColor: category.color} : {}}>
                <span className="text-2xl">{category.icon}</span>
              </div>
              <h3 className="font-semibold text-gray-800 text-center mb-2">{category.name}</h3>
              <p className="text-xs text-gray-500 text-center">{category.items}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Hot Deals */}
      

      {/* Menu Cards Section */}
      <div className="px-6 mt-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Menu</h2>
        </div>
        <div className="grid grid-cols-2 gap-4">
          {[{ id: 1, name: "BLT Sandwich", description: "Crispy bacon, lettuce", price: 15, image: "🍔" }, { id: 2, name: "BBQ Bacon", description: "Hickory BBQ glaze", price: 22, image: "🌭" }].map((item) => (
            <div
              key={item.id}
              className="bg-white rounded-3xl p-6 shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 flex flex-col items-center"
              style={{ border: '2px solid #FF6500' }}
            >
              <div className="w-20 h-20 rounded-full flex items-center justify-center mb-4 text-4xl bg-orange-100">
                <span role="img" aria-label={item.name}>{item.image}</span>
              </div>
              <h3 className="font-semibold text-gray-800 text-center mb-1">{item.name}</h3>
              <p className="text-xs text-gray-500 text-center mb-2">{item.description}</p>
              <span className="text-lg font-bold" style={{ color: '#FF6500' }}>${item.price}</span>
            </div>
          ))}
        </div>
      </div>

    </div>
  );
};

export default GroceryApp;