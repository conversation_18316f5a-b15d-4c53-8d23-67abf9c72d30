// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>, Cairo } from "next/font/google";
// import "../globals.css";
// import { cookies } from "next/headers";
// import Header from "./_CommonComponent/Header";
// import Sidebar from "./_CommonComponent/Sidebar";
// // import Sidebar from "./_CommonComponent/Sidebar";

// const geistSans = Geist({
//   variable: "--font-geist-sans",
//   subsets: ["latin"],
// });

// const geistMono = Geist_Mono({
//   variable: "--font-geist-mono",
//   subsets: ["latin"],
// });

// const cairo = Cairo({
//   weight: "500",
//   subsets: ["latin"],
// });

// export const metadata = {
//   title: "EPISYS",
//   description: "Generated by create next app",
// };

// const Layout = async ({ children }) => {
//   // Get the current locale from cookies (same logic as root layout)
//   const cookieStore = await cookies();
//   const langCookie = cookieStore.get("lang")?.value;
//   const supportedLocales = ["en", "ar"];
//   const defaultLocale = "ar";

//   let currentLocale = defaultLocale;
//   if (langCookie) {
//     currentLocale = supportedLocales.includes(langCookie)
//       ? langCookie
//       : defaultLocale;
//   }

//   return (
  

//     <div className="flex flex-col lg:flex-row h-screen bg-gray-50">
//     <Sidebar />

//     <div className="flex-1 flex flex-col overflow-hidden lg:pl-0">
//       <Header  />

//       <main className="overflow-y-auto p-3 relative flex-1">
//         {children}
//       </main>
//     </div>
//   </div>
//   );
// };

// export default Layout;

import "../globals.css";
// import { ReactNode } from "react";
import MainLayout from "./_CommonComponent/MainLayout";

export const metadata = {
  title: "EPISYS",
  description: "Generated by create next app",
};

export default function Layout({ children }) {
  return <MainLayout>{children}</MainLayout>;
}
