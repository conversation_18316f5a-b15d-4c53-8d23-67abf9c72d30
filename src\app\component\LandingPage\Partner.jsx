// import React from "react";

// const Partner = () => {
//   const brands = [
//     { name: "<PERSON> ", logo: "🍕" },
//     { name: "1/2M", logo: "🍔" },
//     { name: "The Crepe ", logo: "🥞" },
//     { name: "<PERSON><PERSON>", logo: "🥗" },
//     { name: "<PERSON> Do<PERSON>", logo: "☕" },
//     { name: "<PERSON><PERSON> ", logo: "🍳" },
//     { name: "<PERSON><PERSON><PERSON>", logo: "🍗" },
//     { name: "<PERSON> ", logo: "🥐" },
//   ];

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white py-20 px-6 mb-8">
//       <div className="max-w-7xl mx-auto">
//         {/* Header Section */}
//         <div className="text-center mb-20">
//           <div className="inline-block">
//             <h1 className="text-6xl md:text-7xl font-black mb-6 bg-gradient-to-r from-white via-orange-100 to-white bg-clip-text text-transparent">
//               شراكتنا مع
//             </h1>
//             <div className="relative">
//               <h1 className="text-6xl md:text-7xl font-black bg-gradient-to-r from-orange-400 via-orange-500 to-red-500 bg-clip-text text-transparent">
//                 المطاعم
//               </h1>
//               <div className="absolute inset-0 bg-gradient-to-r from-orange-400 via-orange-500 to-red-500 opacity-20 blur-xl"></div>
//             </div>
//             <h1 className="text-6xl md:text-7xl font-black mt-2 bg-gradient-to-r from-white via-orange-100 to-white bg-clip-text">
//               في كل مكان
//             </h1>
//           </div>

//           <div className="mt-12 max-w-4xl mx-auto">
//             <p className="text-xl md:text-2xl text-gray-300 leading-relaxed font-light">
//               آلاف العلامات التجارية المحلية والعالمية للأغذية والمشروبات تثق في{" "}
//               <span className="text-orange-500 font-semibold">إيبيسيس</span>{" "}
//               إدارة عملياتها من الطلبات إلي المدفوعات والمخزون وما بعد ذلك—كل
//               ذلك في {" "}
//               <span className="text-orange-400">
//                 نظام سحابي متكامل لنقاط البيع وإدارة المطاعم
//               </span>
//             </p>
//           </div>
//         </div>

//         {/* Brands Grid */}
//         <div className="relative">
//           {/* Background decoration */}
//           <div className="absolute inset-0 bg-gradient-to-r from-transparent via-orange-500/10 to-transparent blur-3xl"></div>

//           <div className="relative backdrop-blur-sm bg-white/5 rounded-3xl border border-white/10 p-8 md:p-12">
//             <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
//               {brands.map((brand, index) => (
//                 <div
//                   key={brand.name}
//                   className="group relative flex flex-col items-center justify-center p-6 rounded-2xl bg-gradient-to-br from-white/10 to-white/5 border border-white/20 hover:border-orange-400/50 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-orange-500/20"
//                   style={{
//                     animationDelay: `${index * 0.1}s`,
//                   }}
//                 >
//                   {/* Logo placeholder with emoji */}
//                   <div className="text-4xl md:text-5xl mb-3 group-hover:scale-110 transition-transform duration-300">
//                     {brand.logo}
//                   </div>

//                   {/* Brand name */}
//                   <div className="text-sm md:text-base font-semibold text-center group-hover:text-orange-400 transition-colors duration-300">
//                     {brand.name}
//                   </div>

//                   {/* Hover glow effect */}
//                   <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-orange-400/0 via-orange-500/0 to-orange-400/0 group-hover:from-orange-400/20 group-hover:via-orange-500/20 group-hover:to-orange-400/20 transition-all duration-500"></div>
//                 </div>
//               ))}
//             </div>
//           </div>
//         </div>

//         {/* Stats Section */}
//         <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
//           <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-400/30">
//             <div className="text-4xl md:text-5xl font-black text-orange-400 mb-2">
//               1000+
//             </div>
//             <div className="text-gray-300 font-medium">شريك موثوق </div>
//           </div>
//           <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-400/30">
//             <div className="text-4xl md:text-5xl font-black text-orange-400 mb-2">
//               24/7
//             </div>
//             <div className="text-gray-300 font-medium">دعم سحابي </div>
//           </div>
//           <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-400/30">
//             <div className="text-4xl md:text-5xl font-black text-orange-400 mb-2">
//               100%
//             </div>
//             <div className="text-gray-300 font-medium">تعامل سلس</div>
//           </div>
//         </div>

//         {/* CTA Section */}
//         <div className="mt-20 text-center">
//           <button className="group relative px-12 py-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-full font-bold text-lg text-white hover:shadow-2xl hover:shadow-orange-500/40 transition-all duration-300 hover:scale-105">
//             <span className="relative z-10"> نضم إلى شبكتنا</span>
//             <div className="absolute inset-0 rounded-full bg-gradient-to-r from-orange-400 to-red-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Partner;

import React from 'react';

const Partner = () => {
  const brands = [
    { name: 'ليتل سيزر', logo: '🍕' },
    { name: '1/2M', logo: '🍔' },
    { name: 'ذا كريب كافيه', logo: '🥞' },
    { name: 'فريشي', logo: '🥗' },
    { name: 'أوفر دوز', logo: '☕' },
    { name: 'مطبخ KLC', logo: '🍳' },
    { name: 'جاليتوس', logo: '🍗' },
    { name: 'لا فيفيان', logo: '🥐' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-gray-50 to-orange-50 text-gray-900 py-20 px-6 mb-8" dir="rtl">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-20">
          <div className="inline-block">
            <h1 className="text-6xl md:text-7xl font-black mb-6 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent">
              شراكتنا مع
            </h1>
            <div className="relative">
              <h1 className="text-6xl md:text-7xl font-black bg-gradient-to-r from-[#FF6500] via-[#FCB190] to-[#FFD5C7] bg-clip-text text-transparent">
                المطاعم
              </h1>
              <div className="absolute inset-0 bg-gradient-to-r from-[#FF6500] via-[#FCB190] to-[#FFD5C7] opacity-10 blur-xl"></div>
            </div>
            <h1 className="text-6xl md:text-7xl font-black mt-2 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text ">
              في كل مكان
            </h1>

          </div>

          <div className="mt-12 max-w-4xl mx-auto">
            <p className="text-xl md:text-2xl text-gray-600 leading-relaxed font-light">
              آلاف العلامات التجارية المحلية والعالمية للأغذية والمشروبات تثق في{' '}
              <span className="text-[#FF6500] font-semibold">إيبيسيس</span> لإدارة عملياتها من الطلبات إلى
              المدفوعات والمخزون وما بعد ذلك—كل ذلك في{' '}
              <span className="text-[#FCB190] font-medium">نظام سحابي متكامل لنقاط البيع وإدارة المطاعم</span>
            </p>
          </div>
        </div>

        {/* Brands Grid */}
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#FFD5C7]/20 to-transparent blur-3xl"></div>

          <div className="relative backdrop-blur-sm bg-white/80 rounded-3xl border border-[#FFD5C7]/30 shadow-xl shadow-[#FF6500]/5 p-8 md:p-12">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
              {brands.map((brand, index) => (
                <div
                  key={brand.name}
                  className="group relative flex flex-col items-center justify-center p-6 rounded-2xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-[#FCB190] transition-all duration-500 hover:scale-105 hover:shadow-xl hover:shadow-[#FF6500]/10"
                  style={{
                    animationDelay: `${index * 0.1}s`
                  }}
                >
                  {/* Logo placeholder with emoji */}
                  <div className="text-4xl md:text-5xl mb-3 group-hover:scale-110 transition-transform duration-300">
                    {brand.logo}
                  </div>

                  {/* Brand name */}
                  <div className="text-sm md:text-base whitespace-nowrap font-semibold text-center text-gray-700 group-hover:text-[#FF6500] transition-colors duration-300">
                    {brand.name}
                  </div>

                  {/* Hover glow effect */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#FF6500]/0 via-[#FCB190]/0 to-[#FFD5C7]/0 group-hover:from-[#FF6500]/5 group-hover:via-[#FCB190]/10 group-hover:to-[#FFD5C7]/5 transition-all duration-500"></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-[#FF6500]/10 to-[#FCB190]/10 border border-[#FCB190]/20 shadow-lg">
            <div className="text-4xl md:text-5xl font-black text-[#FF6500] mb-2">+1000</div>
            <div className="text-gray-600 font-medium">شريك موثوق</div>
          </div>
          <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-[#FCB190]/10 to-[#FFD5C7]/10 border border-[#FCB190]/20 shadow-lg">
            <div className="text-4xl md:text-5xl font-black text-[#FF6500] mb-2">24/7</div>
            <div className="text-gray-600 font-medium">دعم سحابي</div>
          </div>
          <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-[#FFD5C7]/10 to-[#FF6500]/10 border border-[#FCB190]/20 shadow-lg">
            <div className="text-4xl md:text-5xl font-black text-[#FF6500] mb-2">%100</div>
            <div className="text-gray-600 font-medium">تكامل سلس</div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <button className="group relative px-12 py-4 bg-[#FF6500] to-[#FCB190] rounded-full font-bold text-lg text-white hover:shadow-2xl hover:shadow-[#FF6500]/30 transition-all duration-300 hover:scale-105">
            <span className="relative z-10">انضم إلى شبكتنا</span>
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#FCB190] to-[#FFD5C7] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>
      </div>

    </div>
  );
};

export default Partner;
