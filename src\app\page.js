import { fetchCategories } from "@/component/sevices/serviceFeatching";
import Header from "./component/LandingPage/Header";
import Footer from "./component/LandingPage/Footer";
import Pricing from "./component/LandingPage/Pricing";
import PaySystem from "./component/LandingPage/PaySystem";
import Control from "./component/LandingPage/Control";
import Flexbility from "./component/LandingPage/Flexbility";
import YourGists from "./component/LandingPage/YourGists";
import StoreIsips from "./component/LandingPage/StoreIsips";
import Partner from "./component/LandingPage/Partner";
import EpisysFeatures from "./component/LandingPage/EpisysFeatures";
import CustomerTestimonials from "./component/LandingPage/CustomerTestimonials";
import ArabicFAQSection from "./component/LandingPage/ArabicFAQSection";



export default async function Home() {
  const proucts = await fetchCategories()
  return (
  
       <div className="">
       <Header/>
       <Partner />
       
          <Pricing />
          <Control/>
          <Flexbility />
          <YourGists/>
          {/* <StoreIsips/> */}
          {/* <PaySystem /> */}
          <EpisysFeatures />
          <CustomerTestimonials />
          <ArabicFAQSection />
       <Footer/>
       </div>
    
 
    
  );
}
