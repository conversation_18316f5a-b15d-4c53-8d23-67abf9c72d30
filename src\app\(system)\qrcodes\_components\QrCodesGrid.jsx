"use client";
import React, { useState } from "react";
import { Download, Share2, Co<PERSON>, QrCode } from "lucide-react";
import Link from "next/link";

const QrCodesGrid = () => {
  const [activeTab, setActiveTab] = useState("INDOOR");

  // Realistic QR code pattern
  const QRPattern = ({ size = "w-48 h-48" }) => {
    // Generate a more realistic QR code pattern
    const generateQRPattern = () => {
      const patterns = [
        "■■■■■■■□■□■□■■■■■■■",
        "■□□□□□■□■■□■■□□□□□■",
        "■□■■■□■□□■■■□■□■■■□■",
        "■□■■■□■□■□□■■■□■■■□■",
        "■□■■■□■□■■■□□■□■■■□■",
        "■□□□□□■□■□■■■■□□□□□■",
        "■■■■■■■□■□■□■■■■■■■",
        "□□□□□□□□■□■■□□□□□□□",
        "■■□■■□■■■□■■□■■□■■□",
        "■□■■□■□■■■□□■■□■■□■",
        "□■■□■■■□■■■□■□■■□■■",
        "■□■■■□■■□■■■□■■■□■□",
        "□■□■■■□■■□■■■□■■■□■",
        "■■■□□■■■□■□■■■■□□■■",
        "□■■■□■□■■■□■■□■■■□□",
        "■□■■■□■□■■■□■■□■■■□",
        "■□□□□□■□■□■■■■□□□□□",
        "■■■■■■■□■□■□■■■■■■■",
      ];

      return patterns.map((row, i) => (
        <div key={i} className="flex">
          {row.split("").map((cell, j) => (
            <div
              key={`${i}-${j}`}
              className={`w-2 h-2 ${cell === "■" ? "bg-black" : "bg-white"}`}
            />
          ))}
        </div>
      ));
    };

    return (
      <div
        className={`${size} bg-white p-4 rounded-xl shadow-lg border border-gray-100`}
      >
        <div className="w-full h-full flex flex-col justify-center items-center bg-white p-2">
          <div className="grid grid-rows-18 gap-0">{generateQRPattern()}</div>
        </div>
      </div>
    );
  };

  const tables = [
    { id: "IN01", name: "Table in01", seats: 4 },
    { id: "IN02", name: "Table in02", seats: 4 },
    { id: "IN03", name: "Table in03", seats: 4 },
    { id: "IN04", name: "Table in04", seats: 4 },
    { id: "IN05", name: "Table in05", seats: 4 },
    { id: "IN06", name: "Table in06", seats: 6 },
    { id: "IN07", name: "Table in07", seats: 6 },
    { id: "IN08", name: "Table in08", seats: 2 },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-6 ">
            <div className="p-3 bg-[#FF6500] rounded-xl shadow-lg">
              <QrCode className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                QR Code Management
              </h1>
              <p className="text-gray-600 mt-1">
                Generate and manage table QR codes
              </p>
            </div>
          </div>

          {/* Main QR Code Display */}
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-200">
            <div className="flex flex-col lg:flex-row items-center gap-8">
              <div className="flex-shrink-0">
                <QRPattern size="w-64 h-64" />
              </div>
              <div className="flex-1 text-center lg:text-left">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Master QR Code
                </h2>
                <p className="text-gray-600 mb-6">
                  Scan this code to access the complete menu and table selection
                </p>
                <div className="flex flex-wrap gap-3 justify-center lg:justify-start">
                  <a
                    href="http://localhost:3001/menueMobile"
                    target="_blank"
                     className="flex items-center border border-red-500 gap-2 bg-[#FF6500] hover:bg-[#E55A00] text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    rel="noopener noreferrer"
                  >
                    Visit
                     <Download className="w-5 h-5" />
                  </a>
                 
                  <button className="flex items-center gap-2 bg-white hover:bg-gray-50 text-gray-700 px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg border border-gray-200 hover:shadow-xl transform hover:-translate-y-0.5">
                    <Share2 className="w-5 h-5" />
                    Share
                  </button>
                  <button className="flex items-center gap-2 bg-white hover:bg-gray-50 text-gray-700 px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg border border-gray-200 hover:shadow-xl transform hover:-translate-y-0.5">
                    <Copy className="w-5 h-5" />
                    Copy
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex gap-2 mb-8">
          {["All Areas", "INDOOR", "TERRACE"].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                activeTab === tab
                  ? "bg-[#FF6500] text-white shadow-lg"
                  : "bg-white text-gray-600 hover:bg-gray-50 shadow-md border border-gray-200"
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Table Count */}
        <div className="mb-6">
          <div className="flex items-center gap-2 text-[#FF6500] font-semibold">
            <span className="text-2xl font-bold">{tables.length}</span>
            <span>Tables</span>
          </div>
        </div>

        {/* Tables Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {tables.map((table) => (
            <div
              key={table.id}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 overflow-hidden group hover:-translate-y-1"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span className="text-sm font-medium text-[#FF6500]">
                      {table.id}
                    </span>
                  </div>
                  <span className="text-sm text-gray-500">
                    {table.seats} Seats
                  </span>
                </div>

                <div className="flex justify-center mb-4">
                  <QRPattern size="w-32 h-32" />
                </div>

                <h3 className="text-lg font-semibold text-gray-900 text-center mb-4">
                  {table.name}
                </h3>

                <div className="flex gap-2">
                  <button className="flex-1 flex items-center justify-center gap-2 bg-gray-50 hover:bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
                    <Download className="w-4 h-4" />
                  </button>
                  <button className="flex-1 flex items-center justify-center gap-2 bg-gray-50 hover:bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
                    <Share2 className="w-4 h-4" />
                  </button>
                  <button className="flex-1 flex items-center justify-center gap-2 bg-gray-50 hover:bg-gray-100 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium transition-colors duration-200">
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 bg-white px-6 py-3 rounded-xl shadow-lg border border-gray-200">
            <div className="w-2 h-2 bg-[#FF6500] rounded-full animate-pulse"></div>
            <span className="text-gray-600 font-medium">
              All QR codes are active and ready to use
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QrCodesGrid;
