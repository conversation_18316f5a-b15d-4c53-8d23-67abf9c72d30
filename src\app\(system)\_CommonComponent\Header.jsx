// // // "use client";
// // // import React, { useState } from "react";
// // // import { ArrowLeft, ArrowRight, Bell, ChevronDown, Menu, User, X } from "lucide-react";
// // // import Image from "next/image";
// // // import Link from "next/link";
// // // import { LanguageSwitcher } from "@/app/component/LanguageSwitcher";
// // // import { useSidebar } from "@/context/SidebarContext";

// // // const Header = () => {
// // //   const [showProfileDropdown, setShowProfileDropdown] = useState(false);
// // //   const { isOpen, toggleSidebar } = useSidebar();

// // //   return (
// // //     <header className="bg-white border-b  px-4 sm:px-6 py-3 sm:py-4 relative z-40 shadow-lg shadow-orange-100/20">
// // //       <div className="max-w-7xl mx-auto flex items-center justify-between">
        
// // //         {/* Left side - Logo and Brand */}
// // //         <div className="flex items-center space-x-4 relative left-[-120px]">
// // //           <Link href={"/"} className="flex items-center group hover:scale-105 transition-transform duration-200">
// // //             <div className="relative w-12 h-12 rounded-xl overflow-hidden shadow-lg shadow-orange-200/50 group-hover:shadow-orange-300/60 transition-shadow duration-300">
// // //               <Image
// // //                 src="/images/EPISYS.png"
// // //                 alt="EPISYS Logo"
// // //                 width={48}
// // //                 height={48}
// // //                 className="object-contain p-1"
// // //                 priority
// // //               />
// // //               <div className="absolute inset-0 bg-gradient-to-br from-orange-400/10 to-orange-600/10 group-hover:from-orange-400/20 group-hover:to-orange-600/20 transition-all duration-300"></div>
// // //             </div>
// // //             <div className="ml-3 flex flex-col">
// // //               <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent group-hover:from-orange-600 group-hover:to-orange-700 transition-all duration-300">
// // //                 EPISYS
// // //               </h1>
// // //               <div className="h-0.5 bg-gradient-to-r from-orange-400 to-orange-600 w-0 group-hover:w-full transition-all duration-300"></div>
// // //             </div>
// // //           </Link>
          
// // //           {/* Sidebar Toggle Button */}
// // //           <button
// // //             onClick={toggleSidebar}
// // //             className="ml-4 p-2.5 rounded-xl border-2 border-orange-200/50 bg-white/80 hover:bg-orange-50 hover:border-orange-300 active:scale-95 transition-all duration-200 text-orange-600 hover:text-orange-700 shadow-md hover:shadow-lg"
// // //             aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
// // //             type="button"
// // //           >
// // //             {isOpen ? (
// // //               <ArrowLeft className="h-5 w-5 transform transition-transform duration-200" />
// // //             ) : (
// // //               <ArrowRight className="h-5 w-5 transform transition-transform duration-200" />
// // //             )}
// // //           </button>
// // //         </div>

// // //         {/* Right side - Actions */}
// // //         <div className="flex items-center space-x-3 sm:space-x-4">
          
// // //           {/* Notifications */}
// // //           <button className="relative p-2.5 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-xl transition-all duration-200 group">
// // //             <Bell className="h-5 w-5 group-hover:animate-pulse" />
// // //             <span className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center animate-pulse shadow-lg">
// // //               3
// // //             </span>
// // //           </button>

// // //           {/* Profile Dropdown */}
// // //           <div className="relative">
// // //             <button
// // //               onClick={() => setShowProfileDropdown(!showProfileDropdown)}
// // //               className="flex items-center space-x-2 p-2 hover:bg-orange-50 rounded-xl transition-all duration-200 group"
// // //             >
// // //               <div className="h-8 w-8 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-200">
// // //                 <User className="h-4 w-4 text-white" />
// // //               </div>
// // //               <span className="text-sm font-medium text-gray-700 hidden sm:inline group-hover:text-orange-700 transition-colors duration-200">
// // //                 مدير
// // //               </span>
// // //               <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${showProfileDropdown ? 'rotate-180' : ''}`} />
// // //             </button>

// // //             {showProfileDropdown && (
// // //               <>
// // //                 {/* Backdrop */}
// // //                 <div
// // //                   className="fixed inset-0 z-40 bg-black/10 backdrop-blur-sm"
// // //                   onClick={() => setShowProfileDropdown(false)}
// // //                 />
                
// // //                 {/* Dropdown Menu */}
// // //                 <div className="absolute left-0 sm:right-0 top-full mt-2 w-56 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-orange-100/50 py-3 z-50 animate-in slide-in-from-top-2 duration-200">
// // //                   <div className="px-4 py-2 border-b border-orange-100/50">
// // //                     <div className="flex items-center space-x-3">
// // //                       <div className="h-10 w-10 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
// // //                         <User className="h-5 w-5 text-white" />
// // //                       </div>
// // //                       <div>
// // //                         <p className="font-medium text-gray-800">مدير النظام</p>
// // //                         <p className="text-sm text-gray-500"><EMAIL></p>
// // //                       </div>
// // //                     </div>
// // //                   </div>
                  
// // //                   <div className="py-2">
// // //                     <button
// // //                       className="block w-full text-right px-4 py-2.5 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200"
// // //                       onClick={() => setShowProfileDropdown(false)}
// // //                     >
// // //                       الملف الشخصي
// // //                     </button>
// // //                     <button
// // //                       className="block w-full text-right px-4 py-2.5 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200"
// // //                       onClick={() => setShowProfileDropdown(false)}
// // //                     >
// // //                       الإعدادات
// // //                     </button>
// // //                   </div>
                  
// // //                   <div className="border-t border-orange-100/50 pt-2">
// // //                     <button
// // //                       className="block w-full text-right px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200"
// // //                       onClick={() => setShowProfileDropdown(false)}
// // //                     >
// // //                       تسجيل الخروج
// // //                     </button>
// // //                   </div>
// // //                 </div>
// // //               </>
// // //             )}
// // //           </div>

// // //           {/* Language Switcher */}
// // //           <div className="border-l border-orange-200/50 pl-3">
// // //             <LanguageSwitcher />
// // //           </div>
// // //         </div>
// // //       </div>
// // //     </header>
// // //   );
// // // };

// // // export default Header;
// // "use client";
// // import React, { useState } from "react";
// // import { ArrowLeft, ArrowRight, Bell, ChevronDown, Menu, User, X, Moon, Sun, Calendar, UserPlus, Coffee } from "lucide-react";
// // import Image from "next/image";
// // import Link from "next/link";
// // import { LanguageSwitcher } from "@/app/component/LanguageSwitcher";
// // import { useSidebar } from "@/context/SidebarContext";

// // const Header = () => {
// //   const [showProfileDropdown, setShowProfileDropdown] = useState(false);
// //   const [isDarkMode, setIsDarkMode] = useState(false);
// //   const { isOpen, toggleSidebar } = useSidebar();

// //   // Mock data for demonstration
// //   const todayOrders = 24;
// //   const newReservations = 8;
// //   const waiterRequests = 3;

// //   const toggleTheme = () => {
// //     setIsDarkMode(!isDarkMode);
// //     // هنا يمكنك إضافة logic لتغيير الثيم في التطبيق
// //   };

// //   return (
// //     <header className="bg-white border-b  px-4 sm:px-6 py-3 sm:py-4 relative z-40 shadow-lg shadow-orange-100/20">
// //       <div className="max-w-7xl mx-auto flex items-center justify-between">
        
// //         {/* Left side - Logo and Brand */}
// //         <div className="flex items-center space-x-4 relative left-[-120px]">
// //           <Link href={"/"} className="flex items-center group hover:scale-105 transition-transform duration-200">
// //             <div className="relative w-12 h-12 rounded-xl overflow-hidden shadow-lg shadow-orange-200/50 group-hover:shadow-orange-300/60 transition-shadow duration-300">
// //               <Image
// //                 src="/images/EPISYS.png"
// //                 alt="EPISYS Logo"
// //                 width={48}
// //                 height={48}
// //                 className="object-contain p-1"
// //                 priority
// //               />
// //               <div className="absolute inset-0 bg-gradient-to-br from-orange-400/10 to-orange-600/10 group-hover:from-orange-400/20 group-hover:to-orange-600/20 transition-all duration-300"></div>
// //             </div>
// //             <div className="ml-3 flex flex-col">
// //               <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent group-hover:from-orange-600 group-hover:to-orange-700 transition-all duration-300">
// //                 EPISYS
// //               </h1>
// //               <div className="h-0.5 bg-gradient-to-r from-orange-400 to-orange-600 w-0 group-hover:w-full transition-all duration-300"></div>
// //             </div>
// //           </Link>
          
// //           {/* Sidebar Toggle Button */}
// //           <button
// //             onClick={toggleSidebar}
// //             className="ml-4 p-2.5 rounded-xl border-2 border-orange-200/50 bg-white/80 hover:bg-orange-50 hover:border-orange-300 active:scale-95 transition-all duration-200 text-orange-600 hover:text-orange-700 shadow-md hover:shadow-lg"
// //             aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
// //             type="button"
// //           >
// //             {isOpen ? (
// //               <ArrowLeft className="h-5 w-5 transform transition-transform duration-200" />
// //             ) : (
// //               <ArrowRight className="h-5 w-5 transform transition-transform duration-200" />
// //             )}
// //           </button>
// //         </div>

// //         {/* Center - Action Buttons */}
// //         <div className="flex items-center space-x-3 sm:space-x-4">
          
// //           {/* Today Orders Button */}
// //           <button className="relative flex items-center space-x-2 px-4 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg group">
// //             <Calendar className="h-4 w-4 group-hover:animate-pulse" />
// //             <span className="text-sm font-medium hidden sm:inline">طلبات اليوم</span>
// //             <div className="bg-blue-800/30 text-white text-xs px-2 py-1 rounded-full font-bold">
// //               {todayOrders}
// //             </div>
// //           </button>

// //           {/* New Reservations Button */}
// //           <button className="relative flex items-center space-x-2 px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg group">
// //             <UserPlus className="h-4 w-4 group-hover:animate-pulse" />
// //             <span className="text-sm font-medium hidden sm:inline">حجوزات جديدة</span>
// //             <div className="bg-green-800/30 text-white text-xs px-2 py-1 rounded-full font-bold">
// //               {newReservations}
// //             </div>
// //           </button>

// //           {/* New Waiter Request Button */}
// //           <button className="relative flex items-center space-x-2 px-4 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg group">
// //             <Coffee className="h-4 w-4 group-hover:animate-pulse" />
// //             <span className="text-sm font-medium hidden sm:inline">طلبات الخدمة</span>
// //             <div className="bg-purple-800/30 text-white text-xs px-2 py-1 rounded-full font-bold">
// //               {waiterRequests}
// //             </div>
// //           </button>
// //         </div>

// //         {/* Right side - Actions */}
// //         <div className="flex items-center space-x-3 sm:space-x-4">
          
// //           {/* Theme Toggle Button */}
// //           <button
// //             onClick={toggleTheme}
// //             className="relative p-2.5 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-xl transition-all duration-200 group"
// //             aria-label="Toggle theme"
// //           >
// //             {isDarkMode ? (
// //               <Sun className="h-5 w-5 group-hover:animate-spin" />
// //             ) : (
// //               <Moon className="h-5 w-5 group-hover:animate-pulse" />
// //             )}
// //           </button>

// //           {/* Notifications */}
// //           <button className="relative p-2.5 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-xl transition-all duration-200 group">
// //             <Bell className="h-5 w-5 group-hover:animate-pulse" />
// //             <span className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center animate-pulse shadow-lg">
// //               3
// //             </span>
// //           </button>

// //           {/* Profile Dropdown */}
// //           <div className="relative">
// //             <button
// //               onClick={() => setShowProfileDropdown(!showProfileDropdown)}
// //               className="flex items-center space-x-2 p-2 hover:bg-orange-50 rounded-xl transition-all duration-200 group"
// //             >
// //               <div className="h-8 w-8 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-200">
// //                 <User className="h-4 w-4 text-white" />
// //               </div>
// //               <span className="text-sm font-medium text-gray-700 hidden sm:inline group-hover:text-orange-700 transition-colors duration-200">
// //                 مدير
// //               </span>
// //               <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${showProfileDropdown ? 'rotate-180' : ''}`} />
// //             </button>

// //             {showProfileDropdown && (
// //               <>
// //                 {/* Backdrop */}
// //                 <div
// //                   className="fixed inset-0 z-40 bg-black/10 backdrop-blur-sm"
// //                   onClick={() => setShowProfileDropdown(false)}
// //                 />
                
// //                 {/* Dropdown Menu */}
// //                 <div className="absolute left-0 sm:right-0 top-full mt-2 w-56 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-orange-100/50 py-3 z-50 animate-in slide-in-from-top-2 duration-200">
// //                   <div className="px-4 py-2 border-b border-orange-100/50">
// //                     <div className="flex items-center space-x-3">
// //                       <div className="h-10 w-10 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
// //                         <User className="h-5 w-5 text-white" />
// //                       </div>
// //                       <div>
// //                         <p className="font-medium text-gray-800">مدير النظام</p>
// //                         <p className="text-sm text-gray-500"><EMAIL></p>
// //                       </div>
// //                     </div>
// //                   </div>
                  
// //                   <div className="py-2">
// //                     <button
// //                       className="block w-full text-right px-4 py-2.5 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200"
// //                       onClick={() => setShowProfileDropdown(false)}
// //                     >
// //                       الملف الشخصي
// //                     </button>
// //                     <button
// //                       className="block w-full text-right px-4 py-2.5 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200"
// //                       onClick={() => setShowProfileDropdown(false)}
// //                     >
// //                       الإعدادات
// //                     </button>
// //                   </div>
                  
// //                   <div className="border-t border-orange-100/50 pt-2">
// //                     <button
// //                       className="block w-full text-right px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200"
// //                       onClick={() => setShowProfileDropdown(false)}
// //                     >
// //                       تسجيل الخروج
// //                     </button>
// //                   </div>
// //                 </div>
// //               </>
// //             )}
// //           </div>

// //           {/* Language Switcher */}
// //           <div className="border-l border-orange-200/50 pl-3">
// //             <LanguageSwitcher />
// //           </div>
// //         </div>
// //       </div>
// //     </header>
// //   );
// // };

// // export default Header;
// "use client";
// import React, { useState } from "react";
// import { ArrowLeft, ArrowRight, Bell, ChevronDown, Menu, User, X, Moon, Sun, Calendar, UserPlus, Coffee } from "lucide-react";
// import Image from "next/image";
// import Link from "next/link";
// import { LanguageSwitcher } from "@/app/component/LanguageSwitcher";
// import { useSidebar } from "@/context/SidebarContext";

// const Header = () => {
//   const [showProfileDropdown, setShowProfileDropdown] = useState(false);
//   const [isDarkMode, setIsDarkMode] = useState(false);
//   const { isOpen, toggleSidebar } = useSidebar();

//   // Mock data for demonstration
//   const todayOrders = 24;
//   const newReservations = 8;
//   const waiterRequests = 3;

//   const toggleTheme = () => {
//     setIsDarkMode(!isDarkMode);
//     // هنا يمكنك إضافة logic لتغيير الثيم في التطبيق
//   };

//   return (
//     <header className="bg-white border-b  px-4 sm:px-6 py-3 sm:py-4 relative z-40 shadow-lg shadow-orange-100/20">
//       <div className="max-w-7xl mx-auto flex items-center justify-between">
        
//         {/* Left side - Logo and Brand */}
//         <div className="flex items-center space-x-4 relative">
//           <Link href={"/"} className="flex items-center group hover:scale-105 transition-transform duration-200">
//             <div className="relative w-12 h-12 rounded-xl overflow-hidden shadow-lg shadow-orange-200/50 group-hover:shadow-orange-300/60 transition-shadow duration-300">
//               <Image
//                 src="/images/EPISYS.png"
//                 alt="EPISYS Logo"
//                 width={48}
//                 height={48}
//                 className="object-contain p-1"
//                 priority
//               />
//               <div className="absolute inset-0 bg-gradient-to-br from-orange-400/10 to-orange-600/10 group-hover:from-orange-400/20 group-hover:to-orange-600/20 transition-all duration-300"></div>
//             </div>
//             <div className="ml-3 flex flex-col">
//               <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent group-hover:from-orange-600 group-hover:to-orange-700 transition-all duration-300">
//                 EPISYS
//               </h1>
//               <div className="h-0.5 bg-gradient-to-r from-orange-400 to-orange-600 w-0 group-hover:w-full transition-all duration-300"></div>
//             </div>
//           </Link>
          
//           {/* Sidebar Toggle Button */}
//           <button
//             onClick={toggleSidebar}
//             className="ml-4 p-2.5 rounded-xl border-2 border-orange-200/50 bg-white/80 hover:bg-orange-50 hover:border-orange-300 active:scale-95 transition-all duration-200 text-orange-600 hover:text-orange-700 shadow-md hover:shadow-lg"
//             aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
//             type="button"
//           >
//             {isOpen ? (
//               <ArrowLeft className="h-5 w-5 transform transition-transform duration-200" />
//             ) : (
//               <ArrowRight className="h-5 w-5 transform transition-transform duration-200" />
//             )}
//           </button>
//         </div>

//         {/* Center - Action Buttons */}
//         <div className="flex items-center space-x-3 sm:space-x-4">
          
//           {/* Today Orders Button */}
//           <button className="relative flex items-center space-x-2 px-4 py-2.5 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg group">
//             <Calendar className="h-4 w-4 group-hover:animate-pulse" />
//             <span className="text-sm font-medium hidden sm:inline">Today's Orders</span>
//             <div className="bg-orange-800/80 text-white text-xs px-2 py-1 rounded-full font-bold">
//               {todayOrders}
//             </div>
//           </button>

//           {/* New Reservations Button */}
//           <button className="relative flex items-center space-x-2 px-4 py-2.5 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg group">
//             <UserPlus className="h-4 w-4 group-hover:animate-pulse" />
//             <span className="text-sm font-medium hidden sm:inline">New Reservations</span>
//             <div className="bg-orange-800/80 text-white text-xs px-2 py-1 rounded-full font-bold">
//               {newReservations}
//             </div>
//           </button>

//           {/* New Waiter Request Button */}
//           <button className="relative flex items-center space-x-2 px-4 py-2.5 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg group">
//             <Coffee className="h-4 w-4 group-hover:animate-pulse" />
//             <span className="text-sm font-medium hidden sm:inline">Waiter Requests</span>
//             <div className="bg-orange-800/80 text-white text-xs px-2 py-1 rounded-full font-bold">
//               {waiterRequests}
//             </div>
//           </button>
//         </div>

//         {/* Right side - Actions */}
//         <div className="flex items-center space-x-2">
          
//           {/* Language Switcher */}
//           <div className="flex items-center">
//             <div className="p-2 hover:bg-white/80 rounded-xl transition-all duration-200 group">
//               <LanguageSwitcher />
//             </div>
//           </div>

//           {/* Divider */}
//           <div className="h-8 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>

//           {/* Theme Toggle Button */}
//           <button
//             onClick={toggleTheme}
//             className="relative p-2.5 text-gray-600 hover:text-orange-600"
//             aria-label="Toggle theme"
//             title={isDarkMode ? "تبديل للوضع الفاتح" : "تبديل للوضع الداكن"}
//           >
//             {isDarkMode ? (
//               <Sun className="h-5 w-5 group-hover:animate-spin text-yellow-500" />
//             ) : (
//               <Moon className="h-5 w-5 group-hover:animate-pulse text-indigo-500" />
//             )}
//           </button>

//           {/* Notifications */}
//           <button 
//             className="relative p-2.5 text-gray-600 hover:text-orange-600"
//             title="الإشعارات"
//           >
//             <Bell className="h-5 w-5 group-hover:animate-pulse" />
//             <span className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center animate-pulse shadow-lg">
//               3
//             </span>
//           </button>

//           {/* Divider */}
//           <div className="h-8 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>

//           {/* Profile Dropdown */}
//           <div className="relative">
//             <button
//               onClick={() => setShowProfileDropdown(!showProfileDropdown)}
//               className="flex items-center space-x-2"
//               title="الملف الشخصي"
//             >
//               <div className="h-9 w-9 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-105 transition-all duration-200 ring-2 ring-white/50">
//                 <User className="h-4 w-4 text-white" />
//               </div>
//               <div className="hidden lg:flex flex-col items-start">
//                 <span className="text-sm font-medium text-gray-700 group-hover:text-orange-700 transition-colors duration-200">
//                   System Administrator
//                 </span>
//                 <span className="text-xs text-gray-500 group-hover:text-orange-600 transition-colors duration-200">
//                   Admin
//                 </span>
//               </div>
//               <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${showProfileDropdown ? 'rotate-180' : ''} group-hover:text-orange-600`} />
//             </button>

//             {showProfileDropdown && (
//               <>
//                 {/* Backdrop */}
//                 <div
//                   className="fixed inset-0 z-40 bg-black/10 backdrop-blur-sm"
//                   onClick={() => setShowProfileDropdown(false)}
//                 />
                
//                 {/* Dropdown Menu */}
//                 <div className="absolute left-0 sm:right-0 top-full mt-3 w-64 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-orange-100/50 py-3 z-50 animate-in slide-in-from-top-2 duration-200">
//                   <div className="px-4 py-3 border-b border-orange-100/50">
//                     <div className="flex items-center space-x-3">
//                       <div className="h-12 w-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center ring-2 ring-orange-200/50">
//                         <User className="h-6 w-6 text-white" />
//                       </div>
//                       <div className="flex-1">
//                         <p className="font-semibold text-gray-800 text-base">مدير النظام</p>
//                         <p className="text-sm text-gray-500"><EMAIL></p>
//                         <div className="flex items-center mt-1">
//                           <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
//                           <span className="text-xs text-green-600 ml-1">متصل</span>
//                         </div>
//                       </div>
//                     </div>
//                   </div>
                  
//                   <div className="py-2">
//                     <button
//                       className="block w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200 flex items-center justify-end space-x-2"
//                       onClick={() => setShowProfileDropdown(false)}
//                     >
//                       <span>الملف الشخصي</span>
//                       <User className="h-4 w-4" />
//                     </button>
//                     <button
//                       className="block w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200 flex items-center justify-end space-x-2"
//                       onClick={() => setShowProfileDropdown(false)}
//                     >
//                       <span>الإعدادات</span>
//                       <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
//                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
//                       </svg>
//                     </button>
//                   </div>
                  
//                   <div className="border-t border-orange-100/50 pt-2">
//                     <button
//                       className="block w-full text-right px-4 py-3 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200 flex items-center justify-end space-x-2"
//                       onClick={() => setShowProfileDropdown(false)}
//                     >
//                       <span>تسجيل الخروج</span>
//                       <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
//                       </svg>
//                     </button>
//                   </div>
//                 </div>
//               </>
//             )}
//           </div>
//         </div>
//       </div>
//     </header>
//   );
// };

// export default Header;
import React, { useState } from "react";
import { ArrowLeft, ArrowRight, Bell, ChevronDown, Menu, User, X, Moon, Sun, Calendar, UserPlus, Coffee } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { LanguageSwitcher } from "@/app/component/LanguageSwitcher";
import { useSidebar } from "@/context/SidebarContext";

const Header = () => {
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const { isOpen, toggleSidebar } = useSidebar();

  // Mock data for demonstration
  const todayOrders = 24;
  const newReservations = 8;
  const waiterRequests = 3;

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    // هنا يمكنك إضافة logic لتغيير الثيم في التطبيق
  };

  return (
    <header className="bg-white px-4 sm:px-6 py-3 sm:py-4 relative z-40 shadow-sm shadow-black/50">
      <div className="flex items-center justify-between">
        
        {/* Left side - Logo and Brand */}
        <div className="flex items-center space-x-2 sm:space-x-4 relative">
          <Link href={"/"} className="flex items-center group hover:scale-105 transition-transform duration-200">
            <div className="relative w-10 h-10 sm:w-12 sm:h-12 rounded-xl overflow-hidden shadow-lg shadow-orange-200/50 group-hover:shadow-orange-300/60 transition-shadow duration-300">
              <Image
                src="/images/EPISYS.png"
                alt="EPISYS Logo"
                width={48}
                height={48}
                className="object-contain p-1"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-br from-orange-400/10 to-orange-600/10 group-hover:from-orange-400/20 group-hover:to-orange-600/20 transition-all duration-300"></div>
            </div>
            <div className="ml-2 sm:ml-3 flex flex-col">
              <h1 className="text-lg sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent group-hover:from-orange-600 group-hover:to-orange-700 transition-all duration-300">
                EPISYS
              </h1>
              <div className="h-0.5 bg-gradient-to-r from-orange-400 to-orange-600 w-0 group-hover:w-full transition-all duration-300"></div>
            </div>
          </Link>
          
          {/* Sidebar Toggle Button */}
          <button
            onClick={toggleSidebar}
            className="p-2 sm:p-2.5 rounded-xl border-2 border-orange-200/50 bg-white/80 hover:bg-orange-50 hover:border-orange-300 active:scale-95 transition-all duration-200 text-orange-600 hover:text-orange-700 shadow-md hover:shadow-lg"
            aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
            type="button"
          >
            {isOpen ? (
              <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5 transform transition-transform duration-200" />
            ) : (
              <ArrowRight className="h-4 w-4 sm:h-5 sm:w-5 transform transition-transform duration-200" />
            )}
          </button>
        </div>

        {/* Center - Action Buttons (Hidden on mobile, shown on md+) */}
        <div className="hidden md:flex items-center space-x-3 lg:space-x-4">
          
          {/* Today Orders Button */}
          <button className="relative flex items-center space-x-2 px-3 lg:px-4 py-2.5 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg group">
            <Calendar className="h-4 w-4 group-hover:animate-pulse" />
            <span className="text-sm font-medium hidden lg:inline">Today's Orders</span>
            <div className="bg-orange-800/80 text-white text-xs px-2 py-1 rounded-full font-bold">
              {todayOrders}
            </div>
          </button>

          {/* New Reservations Button */}
          <button className="relative flex items-center space-x-2 px-3 lg:px-4 py-2.5 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg group">
            <UserPlus className="h-4 w-4 group-hover:animate-pulse" />
            <span className="text-sm font-medium hidden lg:inline">New Reservations</span>
            <div className="bg-orange-800/80 text-white text-xs px-2 py-1 rounded-full font-bold">
              {newReservations}
            </div>
          </button>

          {/* New Waiter Request Button */}
          <button className="relative flex items-center space-x-2 px-3 lg:px-4 py-2.5 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg group">
            <Coffee className="h-4 w-4 group-hover:animate-pulse" />
            <span className="text-sm font-medium hidden lg:inline">Waiter Requests</span>
            <div className="bg-orange-800/80 text-white text-xs px-2 py-1 rounded-full font-bold">
              {waiterRequests}
            </div>
          </button>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-2">
          
          {/* Mobile Menu Button (visible on small screens) */}
          <button
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            className="md:hidden p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200"
            aria-label="Toggle mobile menu"
          >
            {showMobileMenu ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </button>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-2">
            {/* Language Switcher */}
            <div className="flex items-center">
              <div className="p-2 hover:bg-white/80 rounded-xl transition-all duration-200 group">
                <LanguageSwitcher />
              </div>
            </div>

            {/* Divider */}
            <div className="h-8 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>

            {/* Theme Toggle Button */}
            <button
              onClick={toggleTheme}
              className="relative p-2.5 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200"
              aria-label="Toggle theme"
              title={isDarkMode ? "تبديل للوضع الفاتح" : "تبديل للوضع الداكن"}
            >
              {isDarkMode ? (
                <Sun className="h-5 w-5 group-hover:animate-spin text-yellow-500" />
              ) : (
                <Moon className="h-5 w-5 group-hover:animate-pulse text-indigo-500" />
              )}
            </button>

            {/* Notifications */}
            <button 
              className="relative p-2.5 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200"
              title="الإشعارات"
            >
              <Bell className="h-5 w-5 group-hover:animate-pulse" />
              <span className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center animate-pulse shadow-lg">
                3
              </span>
            </button>

            {/* Divider */}
            <div className="h-8 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
          </div>

          {/* Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              className="flex items-center space-x-2 group"
              title="الملف الشخصي"
            >
              <div className="h-8 w-8 sm:h-9 sm:w-9 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-105 transition-all duration-200 ring-2 ring-white/50">
                <User className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
              </div>
              <div className="hidden lg:flex flex-col items-start">
                <span className="text-sm font-medium text-gray-700 group-hover:text-orange-700 transition-colors duration-200">
                  System Administrator
                </span>
                <span className="text-xs text-gray-500 group-hover:text-orange-600 transition-colors duration-200">
                  Admin
                </span>
              </div>
              <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform duration-200 ${showProfileDropdown ? 'rotate-180' : ''} group-hover:text-orange-600`} />
            </button>

            {showProfileDropdown && (
              <>
                {/* Backdrop */}
                <div
                  className="fixed inset-0 z-40 bg-black/10 backdrop-blur-sm"
                  onClick={() => setShowProfileDropdown(false)}
                />
                
                {/* Dropdown Menu */}
                <div className="absolute left-0 sm:right-0 top-full mt-3 w-64 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-orange-100/50 py-3 z-50 animate-in slide-in-from-top-2 duration-200">
                  <div className="px-4 py-3 border-b border-orange-100/50">
                    <div className="flex items-center space-x-3">
                      <div className="h-12 w-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center ring-2 ring-orange-200/50">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold text-gray-800 text-base">مدير النظام</p>
                        <p className="text-sm text-gray-500"><EMAIL></p>
                        <div className="flex items-center mt-1">
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                          <span className="text-xs text-green-600 ml-1">متصل</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="py-2">
                    <button
                      className="block w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200 flex items-center justify-end space-x-2"
                      onClick={() => setShowProfileDropdown(false)}
                    >
                      <span>الملف الشخصي</span>
                      <User className="h-4 w-4" />
                    </button>
                    <button
                      className="block w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 transition-colors duration-200 flex items-center justify-end space-x-2"
                      onClick={() => setShowProfileDropdown(false)}
                    >
                      <span>الإعدادات</span>
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </button>
                  </div>
                  
                  <div className="border-t border-orange-100/50 pt-2">
                    <button
                      className="block w-full text-right px-4 py-3 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200 flex items-center justify-end space-x-2"
                      onClick={() => setShowProfileDropdown(false)}
                    >
                      <span>تسجيل الخروج</span>
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {showMobileMenu && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm md:hidden"
            onClick={() => setShowMobileMenu(false)}
          />
          
          {/* Mobile Menu Content */}
          <div className="absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md shadow-2xl border-t border-orange-100/50 py-4 z-50 md:hidden animate-in slide-in-from-top-2 duration-200">
            <div className="px-4 space-y-3">
              
              {/* Action Buttons */}
              <div className="grid grid-cols-1 gap-3">
                <button className="flex items-center justify-between px-4 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5" />
                    <span className="font-medium">Today's Orders</span>
                  </div>
                  <div className="bg-orange-800/80 text-white text-sm px-3 py-1 rounded-full font-bold">
                    {todayOrders}
                  </div>
                </button>

                <button className="flex items-center justify-between px-4 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-md">
                  <div className="flex items-center space-x-3">
                    <UserPlus className="h-5 w-5" />
                    <span className="font-medium">New Reservations</span>
                  </div>
                  <div className="bg-orange-800/80 text-white text-sm px-3 py-1 rounded-full font-bold">
                    {newReservations}
                  </div>
                </button>

                <button className="flex items-center justify-between px-4 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-md">
                  <div className="flex items-center space-x-3">
                    <Coffee className="h-5 w-5" />
                    <span className="font-medium">Waiter Requests</span>
                  </div>
                  <div className="bg-orange-800/80 text-white text-sm px-3 py-1 rounded-full font-bold">
                    {waiterRequests}
                  </div>
                </button>
              </div>

              {/* Divider */}
              <div className="h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent my-4"></div>

              {/* Additional Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Language Switcher */}
                  <div className="p-2 hover:bg-orange-50 rounded-xl transition-all duration-200">
                    <LanguageSwitcher />
                  </div>

                  {/* Theme Toggle */}
                  <button
                    onClick={toggleTheme}
                    className="p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200"
                    title={isDarkMode ? "تبديل للوضع الفاتح" : "تبديل للوضع الداكن"}
                  >
                    {isDarkMode ? (
                      <Sun className="h-5 w-5 text-yellow-500" />
                    ) : (
                      <Moon className="h-5 w-5 text-indigo-500" />
                    )}
                  </button>

                  {/* Notifications */}
                  <button className="relative p-2 text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200">
                    <Bell className="h-5 w-5" />
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center animate-pulse shadow-lg">
                      3
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </header>
  );
};

export default Header;