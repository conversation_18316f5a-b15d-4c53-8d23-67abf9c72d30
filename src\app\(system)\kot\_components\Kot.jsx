// // // "use client"
// // // import { useState, useEffect } from "react"
// // // import { Calendar, ChevronDown, Printer, ChefHat, CheckCircle, X, User, Eye, EyeOff } from "lucide-react" // Added Eye, EyeOff icons
// // // // Removed Swiper imports:
// // // // import { Swiper, SwiperSlide } from "swiper/react"
// // // // import { Navigation } from "swiper/modules"
// // // // import "swiper/css"
// // // // import "swiper/css/navigation"

// // // const KOTManagementInterface = () => {
// // //   const [dateFrom, setDateFrom] = useState("07/06/2025")
// // //   const [dateTo, setDateTo] = useState("07/06/2025")
// // //   const [activeTab, setActiveTab] = useState("pending")
// // //   const [kotItems, setKotItems] = useState([])
// // //   const [aggregatedProducts, setAggregatedProducts] = useState([])
// // //   const [isClient, setIsClient] = useState(false)
// // //   const [showProductSummary, setShowProductSummary] = useState(true) // New state for toggling product summary visibility

// // //   // Effect لتحميل KOTs وتجميع المنتجات عند التحميل الأولي وتغيير localStorage
// // //   useEffect(() => {
// // //     setIsClient(true)
// // //     const loadAndAggregateKOTs = () => {
// // //       try {
// // //         const storedKOTs = localStorage.getItem("kotItems")
// // //         const kots = storedKOTs ? JSON.parse(storedKOTs) : []
// // //         setKotItems(kots) // تحديث KOTs الفردية

// // //         // منطق تجميع المنتجات
// // //         const productMap = new Map()
// // //         kots.forEach((kot) => {
// // //           kot.items.forEach((item) => {
// // //             // استخدام اسم المنتج + الحجم كمعرف فريد للتجميع
// // //             const productIdentifier = `${item.name}${item.size ? ` (${item.size})` : ""}`
// // //             if (productMap.has(productIdentifier)) {
// // //               const existingProduct = productMap.get(productIdentifier)
// // //               productMap.set(productIdentifier, {
// // //                 ...existingProduct,
// // //                 quantity: existingProduct.quantity + item.quantity,
// // //               })
// // //             } else {
// // //               productMap.set(productIdentifier, {
// // //                 name: item.name,
// // //                 arabicName: item.arabicName,
// // //                 quantity: item.quantity,
// // //                 image: item.image || "/placeholder.svg?height=60&width=60",
// // //                 category: item.category,
// // //                 size: item.size || "", // التأكد من وجود الحجم
// // //               })
// // //             }
// // //           })
// // //         })
// // //         setAggregatedProducts(Array.from(productMap.values())) // تحديث المنتجات المجمعة
// // //       } catch (error) {
// // //         console.error("Failed to load or aggregate KOTs from localStorage:", error)
// // //         setKotItems([])
// // //         setAggregatedProducts([])
// // //       }
// // //     }

// // //     loadAndAggregateKOTs()
// // //     window.addEventListener("storage", loadAndAggregateKOTs)
// // //     return () => {
// // //       window.removeEventListener("storage", loadAndAggregateKOTs)
// // //     }
// // //   }, [])

// // //   // Effect لحفظ KOTs في localStorage عند التغيير
// // //   useEffect(() => {
// // //     if (isClient) {
// // //       try {
// // //         localStorage.setItem("kotItems", JSON.stringify(kotItems))
// // //       } catch (error) {
// // //         console.error("Failed to save KOTs to localStorage:", error)
// // //       }
// // //     }
// // //   }, [kotItems, isClient])

// // //   const handleStartCooking = (kotId, itemId = null) => {
// // //     setKotItems((prev) =>
// // //       prev.map((kot) => {
// // //         if (kot.id === kotId) {
// // //           if (itemId) {
// // //             return {
// // //               ...kot,
// // //               items: kot.items.map((item) =>
// // //                 item.id === itemId
// // //                   ? {
// // //                       ...item,
// // //                       status: "cooking",
// // //                       cookingStartTime: Date.now(),
// // //                     }
// // //                   : item,
// // //               ),
// // //             }
// // //           } else {
// // //             return {
// // //               ...kot,
// // //               status: "cooking",
// // //               items: kot.items.map((item) => ({
// // //                 ...item,
// // //                 status: "cooking",
// // //                 cookingStartTime: Date.now(),
// // //               })),
// // //             }
// // //           }
// // //         }
// // //         return kot
// // //       }),
// // //     )
// // //   }

// // //   const handleMarkReady = (kotId, itemId = null) => {
// // //     setKotItems((prev) => {
// // //       const updatedKots = prev.map((kot) => {
// // //         if (kot.id === kotId) {
// // //           if (itemId) {
// // //             const updatedKotItems = kot.items.map((item) =>
// // //               item.id === itemId
// // //                 ? {
// // //                     ...item,
// // //                     status: "ready",
// // //                     cookingEndTime: Date.now(),
// // //                   }
// // //                 : item,
// // //             )
// // //             const allReady = updatedKotItems.every((item) => item.status === "ready")
// // //             return {
// // //               ...kot,
// // //               status: allReady ? "ready" : kot.status,
// // //               items: updatedKotItems,
// // //             }
// // //           } else {
// // //             return {
// // //               ...kot,
// // //               status: "ready",
// // //               items: kot.items.map((item) => ({
// // //                 ...item,
// // //                 status: "ready",
// // //                 cookingEndTime: Date.now(),
// // //               })),
// // //             }
// // //           }
// // //         }
// // //         return kot
// // //       })
// // //       return updatedKots
// // //     })
// // //   }

// // //   const handleCancel = (kotId) => {
// // //     setKotItems((prev) => prev.map((kot) => (kot.id === kotId ? { ...kot, status: "cancelled" } : kot)))
// // //   }

// // //   const handlePrint = (kotId) => {
// // //     console.log(`Printing KOT #${kotId}`)
// // //   }

// // //   const getElapsedTime = (startTime) => {
// // //     if (!startTime) return ""
// // //     const elapsed = Date.now() - startTime
// // //     const minutes = Math.floor(elapsed / 60000)
// // //     const seconds = Math.floor((elapsed % 60000) / 1000)
// // //     return `${minutes}:${seconds.toString().padStart(2, "0")}`
// // //   }

// // //   const getStatusBadge = (status) => {
// // //     switch (status) {
// // //       case "pending":
// // //         return (
// // //           <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
// // //             PENDING CONFIRMATION
// // //           </span>
// // //         )
// // //       case "cooking":
// // //         return <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">COOKING</span>
// // //       case "ready":
// // //         return <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">READY</span>
// // //       case "cancelled":
// // //         return <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">CANCELLED</span>
// // //       default:
// // //         return null
// // //     }
// // //   }

// // //   const getItemStatusIcon = (status) => {
// // //     switch (status) {
// // //       case "cooking":
// // //         return <ChefHat className="w-4 h-4 text-yellow-600" />
// // //       case "ready":
// // //         return <CheckCircle className="w-4 h-4 text-green-600" />
// // //       default:
// // //         return null
// // //     }
// // //   }

// // //   const getFilteredKots = () => {
// // //     return kotItems.filter((kot) => kot.status === activeTab)
// // //   }

// // //   const getTabCounts = () => {
// // //     return {
// // //       pending: kotItems.filter((kot) => kot.status === "pending").length,
// // //       cooking: kotItems.filter((kot) => kot.status === "cooking").length,
// // //       ready: kotItems.filter((kot) => kot.status === "ready").length,
// // //       cancelled: kotItems.filter((kot) => kot.status === "cancelled").length,
// // //     }
// // //   }

// // //   const tabCounts = getTabCounts()

// // //   if (!isClient) {
// // //     return (
// // //       <div className="min-h-screen bg-gray-50 p-6">
// // //         <div className="max-w-7xl mx-auto">
// // //           <div className="flex items-center justify-center h-64">
// // //             <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500]"></div>
// // //           </div>
// // //         </div>
// // //       </div>
// // //     )
// // //   }

// // //   return (
// // //     <div className="min-h-screen bg-gray-50 p-6">
// // //       <div className="max-w-7xl mx-auto">
// // //         {/* Header */}
// // //         <div className="mb-8">
// // //           <h1 className="text-3xl font-bold text-gray-900 mb-6">KOT</h1>

// // //           {/* Date Filters */}
// // //           <div className="flex flex-wrap items-center gap-4 mb-6">
// // //             <div className="flex items-center gap-2">
// // //               <span className="text-sm font-medium text-gray-700">Today</span>
// // //               <ChevronDown className="w-4 h-4 text-gray-500" />
// // //             </div>
// // //             <div className="flex items-center gap-2">
// // //               <Calendar className="w-4 h-4 text-gray-500" />
// // //               <input
// // //                 type="text"
// // //                 value={dateFrom}
// // //                 onChange={(e) => setDateFrom(e.target.value)}
// // //                 className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// // //               />
// // //             </div>
// // //             <span className="text-sm text-gray-500">To</span>
// // //             <div className="flex items-center gap-2">
// // //               <Calendar className="w-4 h-4 text-gray-500" />
// // //               <input
// // //                 type="text"
// // //                 value={dateTo}
// // //                 onChange={(e) => setDateTo(e.target.value)}
// // //                 className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// // //               />
// // //             </div>
// // //           </div>
// // //           {/* Status Tabs */}
// // //           <div className="flex gap-2 mb-6">
// // //             <button
// // //               onClick={() => setActiveTab("pending")}
// // //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// // //                 activeTab === "pending"
// // //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// // //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// // //               }`}
// // //             >
// // //               Pending ({tabCounts.pending})
// // //             </button>
// // //             <button
// // //               onClick={() => setActiveTab("cooking")}
// // //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// // //                 activeTab === "cooking"
// // //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// // //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// // //               }`}
// // //             >
// // //               In Kitchen ({tabCounts.cooking})
// // //             </button>
// // //             <button
// // //               onClick={() => setActiveTab("ready")}
// // //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// // //                 activeTab === "ready"
// // //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// // //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// // //               }`}
// // //             >
// // //               Food is Ready ({tabCounts.ready})
// // //             </button>
// // //             <button
// // //               onClick={() => setActiveTab("cancelled")}
// // //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// // //                 activeTab === "cancelled"
// // //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// // //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// // //               }`}
// // //             >
// // //               Cancelled ({tabCounts.cancelled})
// // //             </button>
// // //           </div>
// // //         </div>

// // //         {/* Product Summary Tickets - الكرت الأول (ملخص المنتجات المجمعة) */}
// // //         <div className="p-6 bg-gray-50">
// // //           <div className="flex justify-end items-center mb-6">
// // //             <button
// // //               onClick={() => setShowProductSummary(!showProductSummary)}
// // //               className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg flex items-center gap-2 hover:bg-gray-300 transition-colors"
// // //             >
// // //               {showProductSummary ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
// // //               {showProductSummary ? "Hide" : "Show"}
// // //             </button>
// // //           </div>

// // //           {showProductSummary &&
// // //             (aggregatedProducts.length === 0 ? (
// // //               <div className="text-center py-12">
// // //                 <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
// // //                   <ChefHat className="w-12 h-12 text-gray-400" />
// // //                 </div>
// // //                 <h3 className="text-lg font-semibold text-gray-900 mb-2">No products to display</h3>
// // //                 <p className="text-gray-500">Add items to cart and generate KOTs to see them here.</p>
// // //               </div>
// // //             ) : (
// // //               <div className="flex overflow-x-auto space-x-8 p-4 pb-8 -mb-4 scrollbar-hide">
// // //                 {" "}
// // //                 {/* Added overflow-x-auto and space-x-8 */}
// // //                 {aggregatedProducts.map((product, index) => (
// // //                   <div
// // //                     key={product.name + product.size} // استخدام الاسم والحجم كمفتاح فريد
// // //                     className="relative bg-white p-4 rounded-lg shadow-md border border-gray-200 flex flex-col items-center text-center flex-shrink-0" // Added flex-shrink-0
// // //                     style={{
// // //                       width: "180px", // حجم ثابت للتذكرة
// // //                       transform: `rotate(${index % 2 === 0 ? -3 : 3}deg)`, // ميلان بسيط
// // //                       marginTop: "50px", // مسافة من الأعلى للحبل
// // //                     }}
// // //                   >
// // //                     {/* الحبل العلوي */}
// // //                     <div className="absolute -top-12 left-1/2 -translate-x-1/2 w-0.5 h-12 bg-gray-400"></div>
// // //                     {/* الدبوس */}
// // //                     <div className="absolute -top-14 left-1/2 -translate-x-1/2 w-3 h-3 bg-gray-600 rounded-full"></div>

// // //                     <img
// // //                       src={product.image || "/placeholder.svg"}
// // //                       alt={product.name}
// // //                       className="w-24 h-24 object-cover rounded-full mb-3 border-2 border-gray-100"
// // //                     />
// // //                     <h4 className="font-semibold text-lg text-gray-900 mb-1">{product.name}</h4>
// // //                     {product.size && <p className="text-sm text-gray-600 mb-1">({product.size})</p>}
// // //                     {product.arabicName && <p className="text-sm text-gray-600 mb-2">{product.arabicName}</p>}
// // //                     <span className="bg-[#FF6500] text-white text-xl font-bold px-4 py-2 rounded-full">
// // //                       x{product.quantity}
// // //                     </span>
// // //                   </div>
// // //                 ))}
// // //               </div>
// // //             ))}
// // //         </div>

// // //         {/* KOT Items - الكرت الثاني (كروت KOT الفردية) - تم تعديل التصميم هنا */}
// // //         <div className="flex flex-wrap justify-center gap-8 p-4 mt-8">
// // //           {getFilteredKots().length === 0 ? (
// // //             <div className="text-center py-12">
// // //               <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
// // //                 <ChefHat className="w-12 h-12 text-gray-400" />
// // //               </div>
// // //               <h3 className="text-lg font-semibold text-gray-900 mb-2">No {activeTab} orders</h3>
// // //               <p className="text-gray-500">
// // //                 {activeTab === "pending" && "No orders waiting for confirmation"}
// // //                 {activeTab === "cooking" && "No orders currently being prepared"}
// // //                 {activeTab === "ready" && "No orders ready for pickup"}
// // //                 {activeTab === "cancelled" && "No cancelled orders"}
// // //               </p>
// // //             </div>
// // //           ) : (
// // //             getFilteredKots().map((kot) => (
// // //               <div
// // //                 key={kot.id}
// // //                 className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
// // //                 style={{ width: "300px" }}
// // //               >
// // //                 {/* KOT Header */}
// // //                 <div className="p-6 border-b border-gray-100">
// // //                   <div className="flex justify-between items-start mb-4">
// // //                     <div>
// // //                       <h3 className="text-lg font-semibold text-[#FF6500] mb-1">KOT #{kot.id}</h3>
// // //                       <p className="text-sm text-gray-600">{kot.items.length} Item(s)</p>
// // //                     </div>
// // //                     <div className="text-right">
// // //                       <p className="text-sm font-medium text-gray-900 mb-1">Order #{kot.orderId}</p>
// // //                       <p className="text-sm text-gray-500">{kot.date}</p>
// // //                     </div>
// // //                   </div>
// // //                   <div className="flex items-center gap-2 mb-4">
// // //                     <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
// // //                       <User className="w-4 h-4 text-gray-600" />
// // //                     </div>
// // //                     <span className="text-sm font-medium text-gray-900">{kot.customerName}</span>
// // //                   </div>
// // //                   {getStatusBadge(kot.status)}
// // //                 </div>
// // //                 {/* Items Section */}
// // //                 <div className="p-6 border-b border-gray-100">
// // //                   <h4 className="text-sm font-medium text-gray-700 mb-3">ITEM NAME</h4>
// // //                   {kot.items.map((item) => (
// // //                     <div key={item.id} className="flex items-center justify-between mb-3">
// // //                       <div className="flex items-center gap-2">
// // //                         {getItemStatusIcon(item.status)}
// // //                         <div>
// // //                           <p className="text-sm text-gray-900">
// // //                             {item.quantity}x {item.name}
// // //                             {item.size ? ` (${item.size})` : ""}
// // //                           </p>
// // //                           {item.notes && <p className="text-xs text-gray-500">Note: {item.notes}</p>}
// // //                           {/* عرض الـ addons */}
// // //                           {item.addons && item.addons.length > 0 && (
// // //                             <div className="mt-1 space-y-1">
// // //                               {item.addons.map((addon, addonIndex) => (
// // //                                 <div key={addonIndex} className="text-xs text-blue-600 flex justify-between">
// // //                                   <span className="truncate pr-1">
// // //                                     + {addon.name} x{addon.quantity}
// // //                                   </span>
// // //                                   <span className="flex-shrink-0">{(addon.price * addon.quantity).toFixed(2)} L.E</span>
// // //                                 </div>
// // //                               ))}
// // //                             </div>
// // //                           )}
// // //                         </div>
// // //                       </div>
// // //                       {item.status === "pending" && (
// // //                         <button
// // //                           onClick={() => handleStartCooking(kot.id, item.id)}
// // //                           className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
// // //                         >
// // //                           Start Cooking
// // //                         </button>
// // //                       )}
// // //                       {item.status === "cooking" && (
// // //                         <button
// // //                           onClick={() => handleMarkReady(kot.id, item.id)}
// // //                           className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
// // //                         >
// // //                           Mark Ready
// // //                         </button>
// // //                       )}
// // //                     </div>
// // //                   ))}
// // //                 </div>
// // //                 {/* Action Buttons */}
// // //                 <div className="p-4 bg-gray-50 rounded-b-xl">
// // //                   <div className="flex gap-2">
// // //                     <button
// // //                       onClick={() => handlePrint(kot.id)}
// // //                       className="flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
// // //                     >
// // //                       <Printer className="w-4 h-4" />
// // //                       Print
// // //                     </button>

// // //                     {kot.status === "pending" && (
// // //                       <>
// // //                         <button
// // //                           onClick={() => handleStartCooking(kot.id)}
// // //                           className="flex items-center justify-center gap-2 px-4 py-2 bg-yellow-100 text-yellow-800 rounded-lg text-sm font-medium hover:bg-yellow-200 transition-colors"
// // //                         >
// // //                           <ChefHat className="w-4 h-4" />
// // //                           Start All
// // //                         </button>
// // //                         <button
// // //                           onClick={() => handleCancel(kot.id)}
// // //                           className="flex items-center justify-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
// // //                         >
// // //                           <X className="w-4 h-4" />
// // //                           Cancel
// // //                         </button>
// // //                       </>
// // //                     )}

// // //                     {kot.status === "cooking" && (
// // //                       <>
// // //                         <button
// // //                           onClick={() => handleMarkReady(kot.id)}
// // //                           className="flex items-center justify-center gap-2 px-4 py-2 bg-green-100 text-green-800 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors"
// // //                         >
// // //                           <CheckCircle className="w-4 h-4" />
// // //                           Mark Ready
// // //                         </button>
// // //                         <button
// // //                           onClick={() => handleCancel(kot.id)}
// // //                           className="flex items-center justify-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
// // //                         >
// // //                           <X className="w-4 h-4" />
// // //                           Cancel
// // //                         </button>
// // //                       </>
// // //                     )}
// // //                   </div>
// // //                 </div>
// // //               </div>
// // //             ))
// // //           )}
// // //         </div>
// // //       </div>
// // //     </div>
// // //   )
// // // }

// // // export default KOTManagementInterface
// // "use client"
// // import { useState, useEffect } from "react"
// // import { Calendar, ChevronDown, Printer, ChefHat, CheckCircle, X, User, Eye, EyeOff, Clock } from "lucide-react" // Added Clock icon

// // const KOTManagementInterface = () => {
// //   const [dateFrom, setDateFrom] = useState("07/06/2025")
// //   const [dateTo, setDateTo] = useState("07/06/2025")
// //   const [activeTab, setActiveTab] = useState("pending")
// //   const [kotItems, setKotItems] = useState([])
// //   const [aggregatedProducts, setAggregatedProducts] = useState([])
// //   const [isClient, setIsClient] = useState(false)
// //   const [showProductSummary, setShowProductSummary] = useState(true)
// //   const [currentTime, setCurrentTime] = useState(Date.now()) // حالة لتحديث المؤقتات في الوقت الفعلي

// //   // Effect لتحميل KOTs وتجميع المنتجات عند التحميل الأولي وتغيير localStorage
// //   useEffect(() => {
// //     setIsClient(true)
// //     const loadAndAggregateKOTs = () => {
// //       try {
// //         const storedKOTs = localStorage.getItem("kotItems")
// //         const kots = storedKOTs ? JSON.parse(storedKOTs) : []
// //         setKotItems(kots) // تحديث KOTs الفردية

// //         // منطق تجميع المنتجات
// //         const productMap = new Map()
// //         kots.forEach((kot) => {
// //           kot.items.forEach((item) => {
// //             const productIdentifier = `${item.name}${item.size ? ` (${item.size})` : ""}`
// //             if (productMap.has(productIdentifier)) {
// //               const existingProduct = productMap.get(productIdentifier)
// //               productMap.set(productIdentifier, {
// //                 ...existingProduct,
// //                 quantity: existingProduct.quantity + item.quantity,
// //               })
// //             } else {
// //               productMap.set(productIdentifier, {
// //                 name: item.name,
// //                 arabicName: item.arabicName,
// //                 quantity: item.quantity,
// //                 image: item.image || "/placeholder.svg?height=60&width=60",
// //                 category: item.category,
// //                 size: item.size || "",
// //               })
// //             }
// //           })
// //         })
// //         setAggregatedProducts(Array.from(productMap.values())) // تحديث المنتجات المجمعة
// //       } catch (error) {
// //         console.error("Failed to load or aggregate KOTs from localStorage:", error)
// //         setKotItems([])
// //         setAggregatedProducts([])
// //       }
// //     }

// //     loadAndAggregateKOTs()
// //     window.addEventListener("storage", loadAndAggregateKOTs)
// //     return () => {
// //       window.removeEventListener("storage", loadAndAggregateKOTs)
// //     }
// //   }, [])

// //   // Effect لحفظ KOTs في localStorage عند التغيير
// //   useEffect(() => {
// //     if (isClient) {
// //       try {
// //         localStorage.setItem("kotItems", JSON.stringify(kotItems))
// //       } catch (error) {
// //         console.error("Failed to save KOTs to localStorage:", error)
// //       }
// //     }
// //   }, [kotItems, isClient])

// //   // Effect لتحديث الوقت الحالي كل ثانية للمؤقتات
// //   useEffect(() => {
// //     const interval = setInterval(() => {
// //       setCurrentTime(Date.now())
// //     }, 1000) // تحديث كل ثانية
// //     return () => clearInterval(interval)
// //   }, [])

// //   const handleStartCooking = (kotId) => {
// //     setKotItems((prev) =>
// //       prev.map((kot) => {
// //         if (kot.id === kotId && kot.status === "pending") {
// //           return {
// //             ...kot,
// //             status: "cooking",
// //             cookingStartTime: Date.now(), // تسجيل وقت بدء الطبخ للـ KOT بالكامل
// //             items: kot.items.map((item) => ({
// //               ...item,
// //               status: "cooking", // تحديث حالة العناصر أيضًا
// //             })),
// //           }
// //         }
// //         return kot
// //       }),
// //     )
// //   }

// //   const handleMarkReady = (kotId) => {
// //     setKotItems((prev) => {
// //       const updatedKots = prev.map((kot) => {
// //         if (kot.id === kotId && kot.status === "cooking") {
// //           return {
// //             ...kot,
// //             status: "ready",
// //             cookingEndTime: Date.now(), // تسجيل وقت انتهاء الطبخ للـ KOT بالكامل
// //             items: kot.items.map((item) => ({
// //               ...item,
// //               status: "ready", // تحديث حالة العناصر أيضًا
// //             })),
// //           }
// //         }
// //         return kot
// //       })
// //       return updatedKots
// //     })
// //   }

// //   const handleServed = (kotId) => {
// //     setKotItems((prev) =>
// //       prev.map((kot) => {
// //         if (kot.id === kotId && kot.status === "ready") {
// //           return {
// //             ...kot,
// //             status: "served",
// //             servedTime: Date.now(), // تسجيل وقت التقديم للـ KOT بالكامل
// //           }
// //         }
// //         return kot
// //       }),
// //     )
// //   }

// //   const handleCancel = (kotId) => {
// //     setKotItems((prev) => prev.map((kot) => (kot.id === kotId ? { ...kot, status: "cancelled" } : kot)))
// //   }

// //   const handlePrint = (kotId) => {
// //     console.log(`Printing KOT #${kotId}`)
// //   }

// //   // دالة لتنسيق المدة الزمنية (الدقائق:الثواني)
// //   const formatDuration = (ms) => {
// //     if (ms < 0 || isNaN(ms)) return "00:00"
// //     const totalSeconds = Math.floor(ms / 1000)
// //     const minutes = Math.floor(totalSeconds / 60)
// //     const seconds = totalSeconds % 60
// //     return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
// //   }

// //   // حساب مدة الطبخ
// //   const getCookingDuration = (kot) => {
// //     if (!kot.cookingStartTime) return "00:00"
// //     const endTime = kot.cookingEndTime || (kot.status === "cooking" ? currentTime : kot.cookingStartTime)
// //     return formatDuration(endTime - kot.cookingStartTime)
// //   }

// //   // حساب مدة الجاهزية
// //   const getReadyDuration = (kot) => {
// //     if (!kot.cookingEndTime) return "00:00"
// //     const endTime = kot.servedTime || (kot.status === "ready" ? currentTime : kot.cookingEndTime)
// //     return formatDuration(endTime - kot.cookingEndTime)
// //   }

// //   const getStatusBadge = (status) => {
// //     switch (status) {
// //       case "pending":
// //         return (
// //           <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
// //             PENDING CONFIRMATION
// //           </span>
// //         )
// //       case "cooking":
// //         return <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">COOKING</span>
// //       case "ready":
// //         return <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">READY</span>
// //       case "served":
// //         return <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">SERVED</span>
// //       case "cancelled":
// //         return <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">CANCELLED</span>
// //       default:
// //         return null
// //     }
// //   }

// //   const getItemStatusIcon = (status) => {
// //     switch (status) {
// //       case "cooking":
// //         return <ChefHat className="w-4 h-4 text-yellow-600" />
// //       case "ready":
// //         return <CheckCircle className="w-4 h-4 text-green-600" />
// //       default:
// //         return null
// //     }
// //   }

// //   const getFilteredKots = () => {
// //     return kotItems.filter((kot) => kot.status === activeTab)
// //   }

// //   const getTabCounts = () => {
// //     return {
// //       pending: kotItems.filter((kot) => kot.status === "pending").length,
// //       cooking: kotItems.filter((kot) => kot.status === "cooking").length,
// //       ready: kotItems.filter((kot) => kot.status === "ready").length,
// //       served: kotItems.filter((kot) => kot.status === "served").length, // إضافة عدد الطلبات المقدمة
// //       cancelled: kotItems.filter((kot) => kot.status === "cancelled").length,
// //     }
// //   }

// //   const tabCounts = getTabCounts()

// //   if (!isClient) {
// //     return (
// //       <div className="min-h-screen bg-gray-50 p-6">
// //         <div className="max-w-7xl mx-auto">
// //           <div className="flex items-center justify-center h-64">
// //             <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500]"></div>
// //           </div>
// //         </div>
// //       </div>
// //     )
// //   }

// //   return (
// //     <div className="min-h-screen bg-gray-50 p-6">
// //       <div className="max-w-7xl mx-auto">
// //         {/* Header */}
// //         <div className="mb-8">
// //           <h1 className="text-3xl font-bold text-gray-900 mb-6">KOT</h1>

// //           {/* Date Filters */}
// //           <div className="flex flex-wrap items-center gap-4 mb-6">
// //             <div className="flex items-center gap-2">
// //               <span className="text-sm font-medium text-gray-700">Today</span>
// //               <ChevronDown className="w-4 h-4 text-gray-500" />
// //             </div>
// //             <div className="flex items-center gap-2">
// //               <Calendar className="w-4 h-4 text-gray-500" />
// //               <input
// //                 type="text"
// //                 value={dateFrom}
// //                 onChange={(e) => setDateFrom(e.target.value)}
// //                 className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// //               />
// //             </div>
// //             <span className="text-sm text-gray-500">To</span>
// //             <div className="flex items-center gap-2">
// //               <Calendar className="w-4 h-4 text-gray-500" />
// //               <input
// //                 type="text"
// //                 value={dateTo}
// //                 onChange={(e) => setDateTo(e.target.value)}
// //                 className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
// //               />
// //             </div>
// //           </div>
// //           {/* Status Tabs */}
// //           <div className="flex gap-2 mb-6">
// //             <button
// //               onClick={() => setActiveTab("pending")}
// //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// //                 activeTab === "pending"
// //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// //               }`}
// //             >
// //               Pending ({tabCounts.pending})
// //             </button>
// //             <button
// //               onClick={() => setActiveTab("cooking")}
// //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// //                 activeTab === "cooking"
// //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// //               }`}
// //             >
// //               In Kitchen ({tabCounts.cooking})
// //             </button>
// //             <button
// //               onClick={() => setActiveTab("ready")}
// //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// //                 activeTab === "ready"
// //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// //               }`}
// //             >
// //               Food is Ready ({tabCounts.ready})
// //             </button>
// //             <button
// //               onClick={() => setActiveTab("served")}
// //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// //                 activeTab === "served"
// //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// //               }`}
// //             >
// //               Served ({tabCounts.served})
// //             </button>
// //             <button
// //               onClick={() => setActiveTab("cancelled")}
// //               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
// //                 activeTab === "cancelled"
// //                   ? "bg-[#FF6500] text-white border-[#FF6500]"
// //                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
// //               }`}
// //             >
// //               Cancelled ({tabCounts.cancelled})
// //             </button>
// //           </div>
// //         </div>

// //         {/* Product Summary Tickets - الكرت الأول (ملخص المنتجات المجمعة) */}
// //         <div className="p-6 bg-gray-50">
// //           <div className="flex justify-end items-center mb-6">
// //             <button
// //               onClick={() => setShowProductSummary(!showProductSummary)}
// //               className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg flex items-center gap-2 hover:bg-gray-300 transition-colors"
// //             >
// //               {showProductSummary ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
// //               {showProductSummary ? "Hide" : "Show"}
// //             </button>
// //           </div>

// //           {showProductSummary &&
// //             (aggregatedProducts.length === 0 ? (
// //               <div className="text-center py-12">
// //                 <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
// //                   <ChefHat className="w-12 h-12 text-gray-400" />
// //                 </div>
// //                 <h3 className="text-lg font-semibold text-gray-900 mb-2">No products to display</h3>
// //                 <p className="text-gray-500">Add items to cart and generate KOTs to see them here.</p>
// //               </div>
// //             ) : (
// //               <div className="flex overflow-x-auto space-x-8 p-4 pb-8 -mb-4 scrollbar-hide">
// //                 {aggregatedProducts.map((product, index) => (
// //                   <div
// //                     key={product.name + product.size}
// //                     className="relative bg-white p-4 rounded-lg shadow-md border border-gray-200 flex flex-col items-center text-center flex-shrink-0"
// //                     style={{
// //                       width: "180px",
// //                       transform: `rotate(${index % 2 === 0 ? -3 : 3}deg)`,
// //                       marginTop: "50px",
// //                     }}
// //                   >
// //                     {/* الحبل العلوي */}
// //                     <div className="absolute -top-12 left-1/2 -translate-x-1/2 w-0.5 h-12 bg-gray-400"></div>
// //                     {/* الدبوس */}
// //                     <div className="absolute -top-14 left-1/2 -translate-x-1/2 w-3 h-3 bg-gray-600 rounded-full"></div>

// //                     <img
// //                       src={product.image || "/placeholder.svg"}
// //                       alt={product.name}
// //                       className="w-24 h-24 object-cover rounded-full mb-3 border-2 border-gray-100"
// //                     />
// //                     <h4 className="font-semibold text-lg text-gray-900 mb-1">{product.name}</h4>
// //                     {product.size && <p className="text-sm text-gray-600 mb-1">({product.size})</p>}
// //                     {product.arabicName && <p className="text-sm text-gray-600 mb-2">{product.arabicName}</p>}
// //                     <span className="bg-[#FF6500] text-white text-xl font-bold px-4 py-2 rounded-full">
// //                       x{product.quantity}
// //                     </span>
// //                   </div>
// //                 ))}
// //               </div>
// //             ))}
// //         </div>

// //         {/* KOT Items - الكرت الثاني (كروت KOT الفردية) */}
// //         <div className="flex flex-wrap justify-center gap-8 p-4 mt-8">
// //           {getFilteredKots().length === 0 ? (
// //             <div className="text-center py-12">
// //               <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
// //                 <ChefHat className="w-12 h-12 text-gray-400" />
// //               </div>
// //               <h3 className="text-lg font-semibold text-gray-900 mb-2">No {activeTab} orders</h3>
// //               <p className="text-gray-500">
// //                 {activeTab === "pending" && "No orders waiting for confirmation"}
// //                 {activeTab === "cooking" && "No orders currently being prepared"}
// //                 {activeTab === "ready" && "No orders ready for pickup"}
// //                 {activeTab === "served" && "No orders have been served"}
// //                 {activeTab === "cancelled" && "No cancelled orders"}
// //               </p>
// //             </div>
// //           ) : (
// //             getFilteredKots().map((kot) => (
// //               <div
// //                 key={kot.id}
// //                 className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
// //                 style={{ width: "300px" }}
// //               >
// //                 {/* KOT Header */}
// //                 <div className="p-6 border-b border-gray-100">
// //                   <div className="flex justify-between items-start mb-4">
// //                     <div>
// //                       <h3 className="text-lg font-semibold text-[#FF6500] mb-1">KOT #{kot.id}</h3>
// //                       <p className="text-sm text-gray-600">{kot.items.length} Item(s)</p>
// //                     </div>
// //                     <div className="text-right">
// //                       <p className="text-sm font-medium text-gray-900 mb-1">Order #{kot.orderId}</p>
// //                       <p className="text-sm text-gray-500">{kot.date}</p>
// //                     </div>
// //                   </div>
// //                   {/* Display Waiter, Cashier, Table, Pax */}
// //                   <div className="space-y-1 mb-4 text-sm text-gray-700">
// //                     {kot.cashier && (
// //                       <div className="flex items-center gap-2">
// //                         <User className="w-4 h-4 text-gray-500" />
// //                         <span>Cashier: {kot.cashier}</span>
// //                       </div>
// //                     )}
// //                     {kot.waiter && (
// //                       <div className="flex items-center gap-2">
// //                         <User className="w-4 h-4 text-gray-500" />
// //                         <span>Waiter: {kot.waiter}</span>
// //                       </div>
// //                     )}
// //                     {kot.tableNumber && (
// //                       <div className="flex items-center gap-2">
// //                         <span className="text-gray-500">🍽️</span>
// //                         <span>Table: {kot.tableNumber}</span>
// //                       </div>
// //                     )}
// //                     {kot.pax && (
// //                       <div className="flex items-center gap-2">
// //                         <span className="text-gray-500">👥</span>
// //                         <span>Pax: {kot.pax}</span>
// //                       </div>
// //                     )}
// //                   </div>
// //                   {getStatusBadge(kot.status)}
// //                   {/* Timers */}
// //                   {(kot.status === "cooking" || kot.status === "ready" || kot.status === "served") && (
// //                     <div className="mt-4 space-y-2">
// //                       <div className="flex items-center gap-2 text-sm text-gray-700">
// //                         <Clock className="w-4 h-4 text-yellow-600" />
// //                         <span>Cooking Time: {getCookingDuration(kot)}</span>
// //                       </div>
// //                       {(kot.status === "ready" || kot.status === "served") && (
// //                         <div className="flex items-center gap-2 text-sm text-gray-700">
// //                           <Clock className="w-4 h-4 text-green-600" />
// //                           <span>Ready Time: {getReadyDuration(kot)}</span>
// //                         </div>
// //                       )}
// //                     </div>
// //                   )}
// //                 </div>
// //                 {/* Items Section */}
// //                 <div className="p-6 border-b border-gray-100">
// //                   <h4 className="text-sm font-medium text-gray-700 mb-3">ITEM NAME</h4>
// //                   {kot.items.map((item) => (
// //                     <div key={item.id} className="flex items-center justify-between mb-3">
// //                       <div className="flex items-center gap-2">
// //                         {getItemStatusIcon(item.status)}
// //                         <div>
// //                           <p className="text-sm text-gray-900">
// //                             {item.quantity}x {item.name}
// //                             {item.size ? ` (${item.size})` : ""}
// //                           </p>
// //                           {item.notes && <p className="text-xs text-gray-500">Note: {item.notes}</p>}
// //                           {/* عرض الـ addons */}
// //                           {item.addons && item.addons.length > 0 && (
// //                             <div className="mt-1 space-y-1">
// //                               {item.addons.map((addon, addonIndex) => (
// //                                 <div key={addonIndex} className="text-xs text-blue-600 flex justify-between">
// //                                   <span className="truncate pr-1">
// //                                     + {addon.name} x{addon.quantity}
// //                                   </span>
// //                                   <span className="flex-shrink-0">{(addon.price * addon.quantity).toFixed(2)} L.E</span>
// //                                 </div>
// //                               ))}
// //                             </div>
// //                           )}
// //                         </div>
// //                       </div>
// //                       {/* Individual item actions (optional, if needed) */}
// //                       {/* {item.status === "pending" && (
// //                         <button
// //                           onClick={() => handleStartCooking(kot.id, item.id)}
// //                           className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
// //                         >
// //                           Start Cooking
// //                         </button>
// //                       )}
// //                       {item.status === "cooking" && (
// //                         <button
// //                           onClick={() => handleMarkReady(kot.id, item.id)}
// //                           className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
// //                         >
// //                           Mark Ready
// //                         </button>
// //                       )} */}
// //                     </div>
// //                   ))}
// //                 </div>
// //                 {/* Action Buttons */}
// //                 <div className="p-4 bg-gray-50 rounded-b-xl">
// //                   <div className="flex gap-2">
// //                     <button
// //                       onClick={() => handlePrint(kot.id)}
// //                       className="flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
// //                     >
// //                       <Printer className="w-4 h-4" />
// //                     </button>

// //                     {kot.status === "pending" && (
// //                       <>
// //                         <button
// //                           onClick={() => handleStartCooking(kot.id)}
// //                           className="flex items-center justify-center gap-2 px-4 py-2 bg-yellow-100 text-yellow-800 rounded-lg text-sm font-medium hover:bg-yellow-200 transition-colors"
// //                         >
// //                           <ChefHat className="w-4 h-4" />
// //                           Start All
// //                         </button>
// //                         <button
// //                           onClick={() => handleCancel(kot.id)}
// //                           className="flex items-center justify-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
// //                         >
// //                           <X className="w-4 h-4" />
// //                           Cancel
// //                         </button>
// //                       </>
// //                     )}

// //                     {kot.status === "cooking" && (
// //                       <>
// //                         <button
// //                           onClick={() => handleMarkReady(kot.id)}
// //                           className="flex items-center justify-center gap-2 px-4 py-2 bg-green-100 text-green-800 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors"
// //                         >
// //                           <CheckCircle className="w-4 h-4" />
// //                           Mark Ready
// //                         </button>
// //                         <button
// //                           onClick={() => handleCancel(kot.id)}
// //                           className="flex items-center justify-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
// //                         >
// //                           <X className="w-4 h-4" />
// //                           Cancel
// //                         </button>
// //                       </>
// //                     )}

// //                     {kot.status === "ready" && (
// //                       <button
// //                         onClick={() => handleServed(kot.id)}
// //                         className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors"
// //                       >
// //                         <CheckCircle className="w-4 h-4" />
// //                         Mark Served
// //                       </button>
// //                     )}
// //                   </div>
// //                 </div>
// //               </div>
// //             ))
// //           )}
// //         </div>
// //       </div>
// //     </div>
// //   )
// // }

// // export default KOTManagementInterface
// "use client"
// import { useState, useEffect } from "react"
// import { Calendar, ChevronDown, Printer, ChefHat, CheckCircle, X, User, Eye, EyeOff, Clock } from "lucide-react" // Added Clock icon

// const KOTManagementInterface = () => {
//   const [dateFrom, setDateFrom] = useState("07/06/2025")
//   const [dateTo, setDateTo] = useState("07/06/2025")
//   const [activeTab, setActiveTab] = useState("pending")
//   const [kotItems, setKotItems] = useState([])
//   const [aggregatedProducts, setAggregatedProducts] = useState([])
//   const [isClient, setIsClient] = useState(false)
//   const [showProductSummary, setShowProductSummary] = useState(true)
//   const [currentTime, setCurrentTime] = useState(Date.now()) // حالة لتحديث المؤقتات في الوقت الفعلي

//   // Effect لتحميل KOTs وتجميع المنتجات عند التحميل الأولي وتغيير localStorage
//   useEffect(() => {
//     setIsClient(true)
//     const loadAndAggregateKOTs = () => {
//       try {
//         const storedKOTs = localStorage.getItem("kotItems")
//         const kots = storedKOTs ? JSON.parse(storedKOTs) : []
//         setKotItems(kots) // تحديث KOTs الفردية

//         // منطق تجميع المنتجات: فقط من الطلبات التي ليست "served" أو "cancelled"
//         const productMap = new Map()
//         kots.forEach((kot) => {
//           if (kot.status !== "served" && kot.status !== "cancelled") {
//             kot.items.forEach((item) => {
//               const productIdentifier = `${item.name}${item.size ? ` (${item.size})` : ""}`
//               if (productMap.has(productIdentifier)) {
//                 const existingProduct = productMap.get(productIdentifier)
//                 productMap.set(productIdentifier, {
//                   ...existingProduct,
//                   quantity: existingProduct.quantity + item.quantity,
//                 })
//               } else {
//                 productMap.set(productIdentifier, {
//                   name: item.name,
//                   arabicName: item.arabicName,
//                   quantity: item.quantity,
//                   image: item.image || "/placeholder.svg?height=60&width=60",
//                   category: item.category,
//                   size: item.size || "",
//                 })
//               }
//             })
//           }
//         })
//         // تصفية المنتجات التي أصبحت كميتها صفر بعد التجميع
//         setAggregatedProducts(Array.from(productMap.values()).filter((product) => product.quantity > 0))
//       } catch (error) {
//         console.error("Failed to load or aggregate KOTs from localStorage:", error)
//         setKotItems([])
//         setAggregatedProducts([])
//       }
//     }
//     loadAndAggregateKOTs()
//     window.addEventListener("storage", loadAndAggregateKOTs)
//     return () => {
//       window.removeEventListener("storage", loadAndAggregateKOTs)
//     }
//   }, [])

//   // Effect لحفظ KOTs في localStorage عند التغيير
//   useEffect(() => {
//     if (isClient) {
//       try {
//         localStorage.setItem("kotItems", JSON.stringify(kotItems))
//       } catch (error) {
//         console.error("Failed to save KOTs to localStorage:", error)
//       }
//     }
//   }, [kotItems, isClient])

//   // Effect لتحديث الوقت الحالي كل ثانية للمؤقتات
//   useEffect(() => {
//     const interval = setInterval(() => {
//       setCurrentTime(Date.now())
//     }, 1000) // تحديث كل ثانية
//     return () => clearInterval(interval)
//   }, [])

//   const handleStartCooking = (kotId) => {
//     setKotItems((prev) =>
//       prev.map((kot) => {
//         if (kot.id === kotId && kot.status === "pending") {
//           return {
//             ...kot,
//             status: "cooking",
//             cookingStartTime: Date.now(), // تسجيل وقت بدء الطبخ للـ KOT بالكامل
//             items: kot.items.map((item) => ({
//               ...item,
//               status: "cooking", // تحديث حالة العناصر أيضًا
//             })),
//           }
//         }
//         return kot
//       }),
//     )
//   }

//   const handleMarkReady = (kotId) => {
//     setKotItems((prev) => {
//       const updatedKots = prev.map((kot) => {
//         if (kot.id === kotId && kot.status === "cooking") {
//           return {
//             ...kot,
//             status: "ready",
//             cookingEndTime: Date.now(), // تسجيل وقت انتهاء الطبخ للـ KOT بالكامل
//             items: kot.items.map((item) => ({
//               ...item,
//               status: "ready", // تحديث حالة العناصر أيضًا
//             })),
//           }
//         }
//         return kot
//       })
//       return updatedKots
//     })
//   }

//   const handleServed = (kotId) => {
//     setKotItems((prev) =>
//       prev.map((kot) => {
//         if (kot.id === kotId && kot.status === "ready") {
//           return {
//             ...kot,
//             status: "served",
//             servedTime: Date.now(), // تسجيل وقت التقديم للـ KOT بالكامل
//           }
//         }
//         return kot
//       }),
//     )
//   }

//   const handleCancel = (kotId) => {
//     setKotItems((prev) => prev.map((kot) => (kot.id === kotId ? { ...kot, status: "cancelled" } : kot)))
//   }

//   const handlePrint = (kotId) => {
//     console.log(`Printing KOT #${kotId}`)
//   }

//   // دالة لتنسيق المدة الزمنية (الدقائق:الثواني)
//   const formatDuration = (ms) => {
//     if (ms < 0 || isNaN(ms)) return "00:00"
//     const totalSeconds = Math.floor(ms / 1000)
//     const minutes = Math.floor(totalSeconds / 60)
//     const seconds = totalSeconds % 60
//     return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
//   }

//   // حساب مدة الطبخ
//   const getCookingDuration = (kot) => {
//     if (!kot.cookingStartTime) return "00:00"
//     const endTime = kot.cookingEndTime || (kot.status === "cooking" ? currentTime : kot.cookingStartTime)
//     return formatDuration(endTime - kot.cookingStartTime)
//   }

//   // حساب مدة الجاهزية
//   const getReadyDuration = (kot) => {
//     if (!kot.cookingEndTime) return "00:00"
//     const endTime = kot.servedTime || (kot.status === "ready" ? currentTime : kot.cookingEndTime)
//     return formatDuration(endTime - kot.cookingEndTime)
//   }

//   const getStatusBadge = (status) => {
//     switch (status) {
//       case "pending":
//         return (
//           <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
//             PENDING CONFIRMATION
//           </span>
//         )
//       case "cooking":
//         return <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">COOKING</span>
//       case "ready":
//         return <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">READY</span>
//       case "served":
//         return <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">SERVED</span>
//       case "cancelled":
//         return <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">CANCELLED</span>
//       default:
//         return null
//     }
//   }

//   const getItemStatusIcon = (status) => {
//     switch (status) {
//       case "cooking":
//         return <ChefHat className="w-4 h-4 text-yellow-600" />
//       case "ready":
//         return <CheckCircle className="w-4 h-4 text-green-600" />
//       default:
//         return null
//     }
//   }

//   const getFilteredKots = () => {
//     return kotItems.filter((kot) => kot.status === activeTab)
//   }

//   const getTabCounts = () => {
//     return {
//       pending: kotItems.filter((kot) => kot.status === "pending").length,
//       cooking: kotItems.filter((kot) => kot.status === "cooking").length,
//       ready: kotItems.filter((kot) => kot.status === "ready").length,
//       served: kotItems.filter((kot) => kot.status === "served").length, // إضافة عدد الطلبات المقدمة
//       cancelled: kotItems.filter((kot) => kot.status === "cancelled").length,
//     }
//   }

//   const tabCounts = getTabCounts()

//   if (!isClient) {
//     return (
//       <div className="min-h-screen bg-gray-50 p-6">
//         <div className="max-w-7xl mx-auto">
//           <div className="flex items-center justify-center h-64">
//             <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500]"></div>
//           </div>
//         </div>
//       </div>
//     )
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 p-6">
//       <div className="max-w-7xl mx-auto">
//         {/* Header */}
//         <div className="mb-8">
//           <h1 className="text-3xl font-bold text-gray-900 mb-6">KOT</h1>
//           {/* Date Filters */}
//           <div className="flex flex-wrap items-center gap-4 mb-6">
//             <div className="flex items-center gap-2">
//               <span className="text-sm font-medium text-gray-700">Today</span>
//               <ChevronDown className="w-4 h-4 text-gray-500" />
//             </div>
//             <div className="flex items-center gap-2">
//               <Calendar className="w-4 h-4 text-gray-500" />
//               <input
//                 type="text"
//                 value={dateFrom}
//                 onChange={(e) => setDateFrom(e.target.value)}
//                 className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
//               />
//             </div>
//             <span className="text-sm text-gray-500">To</span>
//             <div className="flex items-center gap-2">
//               <Calendar className="w-4 h-4 text-gray-500" />
//               <input
//                 type="text"
//                 value={dateTo}
//                 onChange={(e) => setDateTo(e.target.value)}
//                 className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
//               />
//             </div>
//           </div>
//           {/* Status Tabs */}
//           <div className="flex gap-2 mb-6">
//             <button
//               onClick={() => setActiveTab("pending")}
//               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
//                 activeTab === "pending"
//                   ? "bg-[#FF6500] text-white border-[#FF6500]"
//                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
//               }`}
//             >
//               Pending ({tabCounts.pending})
//             </button>
//             <button
//               onClick={() => setActiveTab("cooking")}
//               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
//                 activeTab === "cooking"
//                   ? "bg-[#FF6500] text-white border-[#FF6500]"
//                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
//               }`}
//             >
//               In Kitchen ({tabCounts.cooking})
//             </button>
//             <button
//               onClick={() => setActiveTab("ready")}
//               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
//                 activeTab === "ready"
//                   ? "bg-[#FF6500] text-white border-[#FF6500]"
//                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
//               }`}
//             >
//               Food is Ready ({tabCounts.ready})
//             </button>
//             <button
//               onClick={() => setActiveTab("served")}
//               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
//                 activeTab === "served"
//                   ? "bg-[#FF6500] text-white border-[#FF6500]"
//                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
//               }`}
//             >
//               Served ({tabCounts.served})
//             </button>
//             <button
//               onClick={() => setActiveTab("cancelled")}
//               className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
//                 activeTab === "cancelled"
//                   ? "bg-[#FF6500] text-white border-[#FF6500]"
//                   : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
//               }`}
//             >
//               Cancelled ({tabCounts.cancelled})
//             </button>
//           </div>
//         </div>
//         {/* Product Summary Tickets - الكرت الأول (ملخص المنتجات المجمعة) */}
//         <div className="p-6 bg-gray-50">
//           <div className="flex justify-end items-center mb-6">
//             <button
//               onClick={() => setShowProductSummary(!showProductSummary)}
//               className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg flex items-center gap-2 hover:bg-gray-300 transition-colors"
//             >
//               {showProductSummary ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
//               {showProductSummary ? "Hide" : "Show"}
//             </button>
//           </div>
//           {showProductSummary &&
//             (aggregatedProducts.length === 0 ? (
//               <div className="text-center py-12">
//                 <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
//                   <ChefHat className="w-12 h-12 text-gray-400" />
//                 </div>
//                 <h3 className="text-lg font-semibold text-gray-900 mb-2">No products to display</h3>
//                 <p className="text-gray-500">Add items to cart and generate KOTs to see them here.</p>
//               </div>
//             ) : (
//               <div className="flex overflow-x-auto space-x-8 p-4 pb-8 -mb-4 scrollbar-hide">
//                 {aggregatedProducts.map((product, index) => (
//                   <div
//                     key={product.name + product.size}
//                     className="relative bg-white p-4 rounded-lg shadow-md border border-gray-200 flex flex-col items-center text-center flex-shrink-0"
//                     style={{
//                       width: "180px",
//                       transform: `rotate(${index % 2 === 0 ? -3 : 3}deg)`,
//                       marginTop: "50px",
//                     }}
//                   >
//                     {/* الحبل العلوي */}
//                     <div className="absolute -top-12 left-1/2 -translate-x-1/2 w-0.5 h-12 bg-gray-400"></div>
//                     {/* الدبوس */}
//                     <div className="absolute -top-14 left-1/2 -translate-x-1/2 w-3 h-3 bg-gray-600 rounded-full"></div>
//                     <img
//                       src={product.image || "/placeholder.svg"}
//                       alt={product.name}
//                       className="w-24 h-24 object-cover rounded-full mb-3 border-2 border-gray-100"
//                     />
//                     <h4 className="font-semibold text-lg text-gray-900 mb-1">{product.name}</h4>
//                     {product.size && <p className="text-sm text-gray-600 mb-1">({product.size})</p>}
//                     {product.arabicName && <p className="text-sm text-gray-600 mb-2">{product.arabicName}</p>}
//                     <span className="bg-[#FF6500] text-white text-xl font-bold px-4 py-2 rounded-full">
//                       x{product.quantity}
//                     </span>
//                   </div>
//                 ))}
//               </div>
//             ))}
//         </div>
//         {/* KOT Items - الكرت الثاني (كروت KOT الفردية) */}
//         <div className="flex flex-wrap justify-center gap-8 p-4 mt-8">
//           {getFilteredKots().length === 0 ? (
//             <div className="text-center py-12">
//               <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
//                 <ChefHat className="w-12 h-12 text-gray-400" />
//               </div>
//               <h3 className="text-lg font-semibold text-gray-900 mb-2">No {activeTab} orders</h3>
//               <p className="text-gray-500">
//                 {activeTab === "pending" && "No orders waiting for confirmation"}
//                 {activeTab === "cooking" && "No orders currently being prepared"}
//                 {activeTab === "ready" && "No orders ready for pickup"}
//                 {activeTab === "served" && "No orders have been served"}
//                 {activeTab === "cancelled" && "No cancelled orders"}
//               </p>
//             </div>
//           ) : (
//             getFilteredKots().map((kot) => (
//               <div
//                 key={kot.id}
//                 className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
//                 style={{ width: "300px" }}
//               >
//                 {/* KOT Header */}
//                 <div className="p-6 border-b border-gray-100">
//                   <div className="flex justify-between items-start mb-4">
//                     <div>
//                       <h3 className="text-lg font-semibold text-[#FF6500] mb-1">KOT #{kot.id}</h3>
//                       <p className="text-sm text-gray-600">{kot.items.length} Item(s)</p>
//                     </div>
//                     <div className="text-right">
//                       <p className="text-sm font-medium text-gray-900 mb-1">Order #{kot.orderId}</p>
//                       <p className="text-sm text-gray-500">{kot.date}</p>
//                     </div>
//                   </div>
//                   {/* Display Waiter, Cashier, Table, Pax */}
//                   <div className="space-y-1 mb-4 text-sm text-gray-700">
//                     {kot.cashier && (
//                       <div className="flex items-center gap-2">
//                         <User className="w-4 h-4 text-gray-500" />
//                         <span>Cashier: {kot.cashier}</span>
//                       </div>
//                     )}
//                     {kot.waiter && (
//                       <div className="flex items-center gap-2">
//                         <User className="w-4 h-4 text-gray-500" />
//                         <span>Waiter: {kot.waiter}</span>
//                       </div>
//                     )}
//                     {kot.tableNumber && (
//                       <div className="flex items-center gap-2">
//                         <span className="text-gray-500">🍽️</span>
//                         <span>Table: {kot.tableNumber}</span>
//                       </div>
//                     )}
//                     {kot.pax && (
//                       <div className="flex items-center gap-2">
//                         <span className="text-gray-500">👥</span>
//                         <span>Pax: {kot.pax}</span>
//                       </div>
//                     )}
//                   </div>
//                   {getStatusBadge(kot.status)}
//                   {/* Timers */}
//                   {(kot.status === "cooking" || kot.status === "ready" || kot.status === "served") && (
//                     <div className="mt-4 space-y-2">
//                       <div className="flex items-center gap-2 text-sm text-gray-700">
//                         <Clock className="w-4 h-4 text-yellow-600" />
//                         <span>Cooking Time: {getCookingDuration(kot)}</span>
//                       </div>
//                       {(kot.status === "ready" || kot.status === "served") && (
//                         <div className="flex items-center gap-2 text-sm text-gray-700">
//                           <Clock className="w-4 h-4 text-green-600" />
//                           <span>Ready Time: {getReadyDuration(kot)}</span>
//                         </div>
//                       )}
//                     </div>
//                   )}
//                 </div>
//                 {/* Items Section */}
//                 <div className="p-6 border-b border-gray-100">
//                   <h4 className="text-sm font-medium text-gray-700 mb-3">ITEM NAME</h4>
//                   {kot.items.map((item) => (
//                     <div key={item.id} className="flex items-center justify-between mb-3">
//                       <div className="flex items-center gap-2">
//                         {getItemStatusIcon(item.status)}
//                         <div>
//                           <p className="text-sm text-gray-900">
//                             {item.quantity}x {item.name}
//                             {item.size ? ` (${item.size})` : ""}
//                           </p>
//                           {item.notes && <p className="text-xs text-gray-500">Note: {item.notes}</p>}
//                           {/* عرض الـ addons */}
//                           {item.addons && item.addons.length > 0 && (
//                             <div className="mt-1 space-y-1">
//                               {item.addons.map((addon, addonIndex) => (
//                                 <div key={addonIndex} className="text-xs text-blue-600 flex justify-between">
//                                   <span className="truncate pr-1">
//                                     + {addon.name} x{addon.quantity}
//                                   </span>
//                                   <span className="flex-shrink-0">{(addon.price * addon.quantity).toFixed(2)} L.E</span>
//                                 </div>
//                               ))}
//                             </div>
//                           )}
//                         </div>
//                       </div>
//                       {/* Individual item actions (optional, if needed) */}
//                       {/* {item.status === "pending" && (
//                         <button
//                           onClick={() => handleStartCooking(kot.id, item.id)}
//                           className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
//                         >
//                           Start Cooking
//                         </button>
//                       )}
//                       {item.status === "cooking" && (
//                         <button
//                           onClick={() => handleMarkReady(kot.id, item.id)}
//                           className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
//                         >
//                           Mark Ready
//                         </button>
//                       )} */}
//                     </div>
//                   ))}
//                 </div>
//                 {/* Action Buttons */}
//                 <div className="p-4 bg-gray-50 rounded-b-xl">
//                   <div className="flex gap-2">
//                     <button
//                       onClick={() => handlePrint(kot.id)}
//                       className="flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
//                     >
//                       <Printer className="w-4 h-4" />
//                     </button>
//                     {kot.status === "pending" && (
//                       <>
//                         <button
//                           onClick={() => handleStartCooking(kot.id)}
//                           className="flex items-center justify-center gap-2 px-4 py-2 bg-yellow-100 text-yellow-800 rounded-lg text-sm font-medium hover:bg-yellow-200 transition-colors"
//                         >
//                           <ChefHat className="w-4 h-4" />
//                           Start All
//                         </button>
//                         <button
//                           onClick={() => handleCancel(kot.id)}
//                           className="flex items-center justify-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
//                         >
//                           <X className="w-4 h-4" />
//                           Cancel
//                         </button>
//                       </>
//                     )}
//                     {kot.status === "cooking" && (
//                       <>
//                         <button
//                           onClick={() => handleMarkReady(kot.id)}
//                           className="flex items-center justify-center gap-2 px-4 py-2 bg-green-100 text-green-800 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors"
//                         >
//                           <CheckCircle className="w-4 h-4" />
//                           Mark Ready
//                         </button>
//                         <button
//                           onClick={() => handleCancel(kot.id)}
//                           className="flex items-center justify-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
//                         >
//                           <X className="w-4 h-4" />
//                           Cancel
//                         </button>
//                       </>
//                     )}
//                     {kot.status === "ready" && (
//                       <button
//                         onClick={() => handleServed(kot.id)}
//                         className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors"
//                       >
//                         <CheckCircle className="w-4 h-4" />
//                         Mark Served
//                       </button>
//                     )}
//                   </div>
//                 </div>
//               </div>
//             ))
//           )}
//         </div>
//       </div>
//     </div>
//   )
// }

// export default KOTManagementInterface
"use client"
import { useState, useEffect, useCallback } from "react" // إضافة useCallback
import { Calendar, ChevronDown, Printer, ChefHat, CheckCircle, X, User, Eye, EyeOff, Clock } from "lucide-react"

const KOTManagementInterface = () => {
  const [dateFrom, setDateFrom] = useState("07/06/2025")
  const [dateTo, setDateTo] = useState("07/06/2025")
  const [activeTab, setActiveTab] = useState("pending")
  const [kotItems, setKotItems] = useState([])
  const [aggregatedProducts, setAggregatedProducts] = useState([])
  const [isClient, setIsClient] = useState(false)
  const [showProductSummary, setShowProductSummary] = useState(true)
  const [currentTime, setCurrentTime] = useState(Date.now())

  // دالة لتجميع المنتجات بناءً على حالة kotItems الحالية
  const aggregateProducts = useCallback((kots) => {
    const productMap = new Map()
    kots.forEach((kot) => {
      if (kot.status !== "served" && kot.status !== "cancelled") {
        kot.items.forEach((item) => {
          const productIdentifier = `${item.name}${item.size ? ` (${item.size})` : ""}`
          if (productMap.has(productIdentifier)) {
            const existingProduct = productMap.get(productIdentifier)
            productMap.set(productIdentifier, {
              ...existingProduct,
              quantity: existingProduct.quantity + item.quantity,
            })
          } else {
            productMap.set(productIdentifier, {
              name: item.name,
              arabicName: item.arabicName,
              quantity: item.quantity,
              image: item.image || "/placeholder.svg?height=60&width=60",
              category: item.category,
              size: item.size || "",
            })
          }
        })
      }
    })
    return Array.from(productMap.values()).filter((product) => product.quantity > 0)
  }, [])

  // Effect لتحميل KOTs وتجميع المنتجات عند التحميل الأولي
  useEffect(() => {
    setIsClient(true)
    const loadKOTs = () => {
      try {
        const storedKOTs = localStorage.getItem("kotItems")
        const kots = storedKOTs ? JSON.parse(storedKOTs) : []
        setKotItems(kots)
        setAggregatedProducts(aggregateProducts(kots)) // تحديث المنتجات المجمعة عند التحميل
      } catch (error) {
        console.error("Failed to load KOTs from localStorage:", error)
        setKotItems([])
        setAggregatedProducts([])
      }
    }

    loadKOTs()
    // الاستماع لتغييرات localStorage من مصادر أخرى (مثل علامة تبويب أخرى)
    window.addEventListener("storage", loadKOTs)
    return () => {
      window.removeEventListener("storage", loadKOTs)
    }
  }, [aggregateProducts]) // إضافة aggregateProducts كاعتمادية

  // Effect لحفظ KOTs في localStorage وتحديث المنتجات المجمعة عند تغيير kotItems
  useEffect(() => {
    if (isClient) {
      try {
        localStorage.setItem("kotItems", JSON.stringify(kotItems))
        setAggregatedProducts(aggregateProducts(kotItems)) // تحديث المنتجات المجمعة فورًا
      } catch (error) {
        console.error("Failed to save KOTs to localStorage:", error)
      }
    }
  }, [kotItems, isClient, aggregateProducts]) // إضافة aggregateProducts كاعتمادية

  // Effect لتحديث الوقت الحالي كل ثانية للمؤقتات
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now())
    }, 1000)
    return () => clearInterval(interval)
  }, [])

  const handleStartCooking = (kotId) => {
    setKotItems((prev) =>
      prev.map((kot) => {
        if (kot.id === kotId && kot.status === "pending") {
          return {
            ...kot,
            status: "cooking",
            cookingStartTime: Date.now(),
            items: kot.items.map((item) => ({
              ...item,
              status: "cooking",
            })),
          }
        }
        return kot
      }),
    )
  }

  const handleMarkReady = (kotId) => {
    setKotItems((prev) => {
      const updatedKots = prev.map((kot) => {
        if (kot.id === kotId && kot.status === "cooking") {
          return {
            ...kot,
            status: "ready",
            cookingEndTime: Date.now(),
            items: kot.items.map((item) => ({
              ...item,
              status: "ready",
            })),
          }
        }
        return kot
      })
      return updatedKots
    })
  }

  const handleServed = (kotId) => {
    setKotItems((prev) =>
      prev.map((kot) => {
        if (kot.id === kotId && kot.status === "ready") {
          return {
            ...kot,
            status: "served",
            servedTime: Date.now(),
          }
        }
        return kot
      }),
    )
  }

  const handleCancel = (kotId) => {
    setKotItems((prev) => prev.map((kot) => (kot.id === kotId ? { ...kot, status: "cancelled" } : kot)))
  }

  const handlePrint = (kotId) => {
    console.log(`Printing KOT #${kotId}`)
  }

  const formatDuration = (ms) => {
    if (ms < 0 || isNaN(ms)) return "00:00"
    const totalSeconds = Math.floor(ms / 1000)
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = totalSeconds % 60
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  const getCookingDuration = (kot) => {
    if (!kot.cookingStartTime) return "00:00"
    const endTime = kot.cookingEndTime || (kot.status === "cooking" ? currentTime : kot.cookingStartTime)
    return formatDuration(endTime - kot.cookingStartTime)
  }

  const getReadyDuration = (kot) => {
    if (!kot.cookingEndTime) return "00:00"
    const endTime = kot.servedTime || (kot.status === "ready" ? currentTime : kot.cookingEndTime)
    return formatDuration(endTime - kot.cookingEndTime)
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return (
          <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
            PENDING CONFIRMATION
          </span>
        )
      case "cooking":
        return <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">COOKING</span>
      case "ready":
        return <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">READY</span>
      case "served":
        return <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">SERVED</span>
      case "cancelled":
        return <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">CANCELLED</span>
      default:
        return null
    }
  }

  const getItemStatusIcon = (status) => {
    switch (status) {
      case "cooking":
        return <ChefHat className="w-4 h-4 text-yellow-600" />
      case "ready":
        return <CheckCircle className="w-4 h-4 text-green-600" />
      default:
        return null
    }
  }

  const getFilteredKots = () => {
    return kotItems.filter((kot) => kot.status === activeTab)
  }

  const getTabCounts = () => {
    return {
      pending: kotItems.filter((kot) => kot.status === "pending").length,
      cooking: kotItems.filter((kot) => kot.status === "cooking").length,
      ready: kotItems.filter((kot) => kot.status === "ready").length,
      served: kotItems.filter((kot) => kot.status === "served").length,
      cancelled: kotItems.filter((kot) => kot.status === "cancelled").length,
    }
  }

  const tabCounts = getTabCounts()

  if (!isClient) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6500]"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">KOT</h1>
          {/* Date Filters */}
          <div className="flex flex-wrap items-center gap-4 mb-6">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Today</span>
              <ChevronDown className="w-4 h-4 text-gray-500" />
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <input
                type="text"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              />
            </div>
            <span className="text-sm text-gray-500">To</span>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <input
                type="text"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              />
            </div>
          </div>
          {/* Status Tabs */}
          <div className="flex gap-2 mb-6">
            <button
              onClick={() => setActiveTab("pending")}
              className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
                activeTab === "pending"
                  ? "bg-[#FF6500] text-white border-[#FF6500]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              Pending ({tabCounts.pending})
            </button>
            <button
              onClick={() => setActiveTab("cooking")}
              className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
                activeTab === "cooking"
                  ? "bg-[#FF6500] text-white border-[#FF6500]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              In Kitchen ({tabCounts.cooking})
            </button>
            <button
              onClick={() => setActiveTab("ready")}
              className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
                activeTab === "ready"
                  ? "bg-[#FF6500] text-white border-[#FF6500]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              Food is Ready ({tabCounts.ready})
            </button>
            <button
              onClick={() => setActiveTab("served")}
              className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
                activeTab === "served"
                  ? "bg-[#FF6500] text-white border-[#FF6500]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              Served ({tabCounts.served})
            </button>
            <button
              onClick={() => setActiveTab("cancelled")}
              className={`px-4 py-2 rounded-lg border font-medium transition-colors ${
                activeTab === "cancelled"
                  ? "bg-[#FF6500] text-white border-[#FF6500]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              Cancelled ({tabCounts.cancelled})
            </button>
          </div>
        </div>
        {/* Product Summary Tickets - الكرت الأول (ملخص المنتجات المجمعة) */}
        <div className="p-6 bg-gray-50">
          <div className="flex justify-end items-center mb-6">
            <button
              onClick={() => setShowProductSummary(!showProductSummary)}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg flex items-center gap-2 hover:bg-gray-300 transition-colors"
            >
              {showProductSummary ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              {showProductSummary ? "Hide" : "Show"}
            </button>
          </div>
          {showProductSummary &&
            (aggregatedProducts.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ChefHat className="w-12 h-12 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No products to display</h3>
                <p className="text-gray-500">Add items to cart and generate KOTs to see them here.</p>
              </div>
            ) : (
              <div className="flex overflow-x-auto space-x-8 p-4 pb-8 -mb-4 scrollbar-hide">
                {aggregatedProducts.map((product, index) => (
                  <div
                    key={product.name + product.size}
                    className="relative bg-white p-4 rounded-lg shadow-md border border-gray-200 flex flex-col items-center text-center flex-shrink-0"
                    style={{
                      width: "180px",
                      transform: `rotate(${index % 2 === 0 ? -3 : 3}deg)`,
                      marginTop: "50px",
                    }}
                  >
                    {/* الحبل العلوي */}
                    <div className="absolute -top-12 left-1/2 -translate-x-1/2 w-0.5 h-12 bg-gray-400"></div>
                    {/* الدبوس */}
                    <div className="absolute -top-14 left-1/2 -translate-x-1/2 w-3 h-3 bg-gray-600 rounded-full"></div>
                    <img
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      className="w-24 h-24 object-cover rounded-full mb-3 border-2 border-gray-100"
                    />
                    <h4 className="font-semibold text-lg text-gray-900 mb-1">{product.name}</h4>
                    {product.size && <p className="text-sm text-gray-600 mb-1">({product.size})</p>}
                    {product.arabicName && <p className="text-sm text-gray-600 mb-2">{product.arabicName}</p>}
                    <span className="bg-[#FF6500] text-white text-xl font-bold px-4 py-2 rounded-full">
                      x{product.quantity}
                    </span>
                  </div>
                ))}
              </div>
            ))}
        </div>
        {/* KOT Items - الكرت الثاني (كروت KOT الفردية) */}
        <div className="flex flex-wrap justify-center gap-8 p-4 mt-8">
          {getFilteredKots().length === 0 ? (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ChefHat className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No {activeTab} orders</h3>
              <p className="text-gray-500">
                {activeTab === "pending" && "No orders waiting for confirmation"}
                {activeTab === "cooking" && "No orders currently being prepared"}
                {activeTab === "ready" && "No orders ready for pickup"}
                {activeTab === "served" && "No orders have been served"}
                {activeTab === "cancelled" && "No cancelled orders"}
              </p>
            </div>
          ) : (
            getFilteredKots().map((kot) => (
              <div
                key={kot.id}
                className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
                style={{ width: "300px" }}
              >
                {/* KOT Header */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-[#FF6500] mb-1">KOT #{kot.id}</h3>
                      <p className="text-sm text-gray-600">{kot.items.length} Item(s)</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900 mb-1">Order #{kot.orderId}</p>
                      <p className="text-sm text-gray-500">{kot.date}</p>
                    </div>
                  </div>
                  {/* Display Waiter, Cashier, Table, Pax */}
                  <div className="space-y-1 mb-4 text-sm text-gray-700">
                    {kot.cashier && (
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-500" />
                        <span>Cashier: {kot.cashier}</span>
                      </div>
                    )}
                    {kot.waiter && (
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-gray-500" />
                        <span>Waiter: {kot.waiter}</span>
                      </div>
                    )}
                    {kot.tableNumber && (
                      <div className="flex items-center gap-2">
                        <span className="text-gray-500">🍽️</span>
                        <span>Table: {kot.tableNumber}</span>
                      </div>
                    )}
                    {kot.pax && (
                      <div className="flex items-center gap-2">
                        <span className="text-gray-500">👥</span>
                        <span>Pax: {kot.pax}</span>
                      </div>
                    )}
                  </div>
                  {getStatusBadge(kot.status)}
                  {/* Timers */}
                  {(kot.status === "cooking" || kot.status === "ready" || kot.status === "served") && (
                    <div className="mt-4 space-y-2">
                      <div className="flex items-center gap-2 text-sm text-gray-700">
                        <Clock className="w-4 h-4 text-yellow-600" />
                        <span>Cooking Time: {getCookingDuration(kot)}</span>
                      </div>
                      {(kot.status === "ready" || kot.status === "served") && (
                        <div className="flex items-center gap-2 text-sm text-gray-700">
                          <Clock className="w-4 h-4 text-green-600" />
                          <span>Ready Time: {getReadyDuration(kot)}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                {/* Items Section */}
                <div className="p-6 border-b border-gray-100">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">ITEM NAME</h4>
                  {kot.items.map((item) => (
                    <div key={item.id} className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {getItemStatusIcon(item.status)}
                        <div>
                          <p className="text-sm text-gray-900">
                            {item.quantity}x {item.name}
                            {item.size ? ` (${item.size})` : ""}
                          </p>
                          {item.notes && <p className="text-xs text-gray-500">Note: {item.notes}</p>}
                          {/* عرض الـ addons */}
                          {item.addons && item.addons.length > 0 && (
                            <div className="mt-1 space-y-1">
                              {item.addons.map((addon, addonIndex) => (
                                <div key={addonIndex} className="text-xs text-blue-600 flex justify-between">
                                  <span className="truncate pr-1">
                                    + {addon.name} x{addon.quantity}
                                  </span>
                                  <span className="flex-shrink-0">{(addon.price * addon.quantity).toFixed(2)} L.E</span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                      {/* Individual item actions (optional, if needed) */}
                      {/* {item.status === "pending" && (
                        <button
                          onClick={() => handleStartCooking(kot.id, item.id)}
                          className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          Start Cooking
                        </button>
                      )}
                      {item.status === "cooking" && (
                        <button
                          onClick={() => handleMarkReady(kot.id, item.id)}
                          className="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          Mark Ready
                        </button>
                      )} */}
                    </div>
                  ))}
                </div>
                {/* Action Buttons */}
                <div className="p-4 bg-gray-50 rounded-b-xl">
                  <div className="flex gap-2">
                    <button
                      onClick={() => handlePrint(kot.id)}
                      className="flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                      <Printer className="w-4 h-4" />
                    </button>
                    {kot.status === "pending" && (
                      <>
                        <button
                          onClick={() => handleStartCooking(kot.id)}
                          className="flex items-center justify-center gap-2 px-4 py-2 bg-yellow-100 text-yellow-800 rounded-lg text-sm font-medium hover:bg-yellow-200 transition-colors"
                        >
                          <ChefHat className="w-4 h-4" />
                          Start All
                        </button>
                        <button
                          onClick={() => handleCancel(kot.id)}
                          className="flex items-center justify-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
                        >
                          <X className="w-4 h-4" />
                          Cancel
                        </button>
                      </>
                    )}
                    {kot.status === "cooking" && (
                      <>
                        <button
                          onClick={() => handleMarkReady(kot.id)}
                          className="flex items-center justify-center gap-2 px-4 py-2 bg-green-100 text-green-800 rounded-lg text-sm font-medium hover:bg-green-200 transition-colors"
                        >
                          <CheckCircle className="w-4 h-4" />
                          Mark Ready
                        </button>
                        <button
                          onClick={() => handleCancel(kot.id)}
                          className="flex items-center justify-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
                        >
                          <X className="w-4 h-4" />
                          Cancel
                        </button>
                      </>
                    )}
                    {kot.status === "ready" && (
                      <button
                        onClick={() => handleServed(kot.id)}
                        className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium hover:bg-blue-200 transition-colors"
                      >
                        <CheckCircle className="w-4 h-4" />
                        Mark Served
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default KOTManagementInterface
