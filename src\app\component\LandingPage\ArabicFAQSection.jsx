"use client";
import React, { useState } from "react";
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  MessageCircle,
  Shield,
  Settings,
  CreditCard,
  MapPin,
} from "lucide-react";
import ReusabelSubbortBtn from "../ReusabelComponent/ReusabelSubbortBtn";

const ArabicFAQSection = () => {
  const [openFAQ, setOpenFAQ] = useState(null);

  const faqs = [
    {
      id: 1,
      icon: MessageCircle,
      question: "كيف يمكنني التواصل مع فريق الدعم الفني؟",
      answer:
        "فريق الدعم الفني المختص متاح على مدار الساعة عبر البريد الإلكتروني والهاتف لمساعدتك في أي استفسارات أو مشاكل تقنية قد تواجهها.",
    },
    {
      id: 2,
      icon: Settings,
      question: "هل البرنامج سهل الاستخدام؟",
      answer:
        "نعم، تم تصميم برنامجنا ليكون بسيطاً وسهل الاستخدام. واجهة المستخدم مصممة خصيصاً للمطاعم العربية مع دعم كامل للغة العربية وتجربة مستخدم سلسة.",
    },
    {
      id: 3,
      icon: MapPin,
      question: "هل يمكنني إدارة عدة فروع من خلال النظام؟",
      answer:
        "بالطبع! يتيح لك episys إدارة عدة مواقع ومطاعم من لوحة تحكم واحدة موحدة، مع إمكانية متابعة المبيعات والتقارير لكل فرع على حدة.",
    },
    {
      id: 4,
      icon: CreditCard,
      question: "هل يدعم النظام المدفوعات الإلكترونية؟",
      answer:
        "نعم، episys يتكامل مع جميع وسائل الدفع الإلكتروني الرائدة مثل فيزا وماستركارد والمحافظ الرقمية لضمان معاملات آمنة وسريعة.",
    },
    {
      id: 5,
      icon: Shield,
      question: "هل بيانات مطعمي آمنة ومحمية؟",
      answer:
        "نستخدم أحدث معايير الأمان في الصناعة لضمان حماية بياناتك بالكامل. جميع البيانات مشفرة ومحمية على مدار الساعة مع نسخ احتياطية يومية.",
    },
    {
      id: 6,
      icon: Settings,
      question: "هل يمكنني تخصيص القائمة وتصميم المطعم؟",
      answer:
        "نعم، يمكنك بسهولة تخصيص قوائم الطعام وتصميم مخطط المطعم وترتيب الطاولات بما يناسب احتياجاتك الخاصة مع إمكانيات تخصيص لا محدودة.",
    },
  ];

  const toggleFAQ = (id) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <div className="min-h-screen my-8 bg-white py-20 px-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-4 mb-8">
            <HelpCircle className="w-10 h-10 text-[#FF6500]" />
            <h1 className="text-5xl md:text-6xl font-black bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent">
              الأسئلة الشائعة
            </h1>
          </div>

          <div className="max-w-3xl mx-auto">
            <p className="text-xl md:text-2xl text-gray-600 leading-relaxed font-light">
              إجابات شاملة على جميع استفساراتك حول{" "}
              <span className="text-[#FF6500] font-semibold">episys</span>
            </p>
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => {
            const IconComponent = faq.icon;
            const isOpen = openFAQ === faq.id;

            return (
              <div
                key={faq.id}
                className="group relative"
                style={{
                  animationDelay: `${index * 0.1}s`,
                }}
              >
                <div className="relative rounded-2xl bg-white backdrop-blur-sm border border-gray-200/50 hover:border-[#FCB190]/50 shadow-lg hover:shadow-xl hover:shadow-[#FF6500]/10 transition-all duration-300 overflow-hidden">
                  {/* Question Header */}
                  <button
                    onClick={() => toggleFAQ(faq.id)}
                    className="w-full p-6 text-right flex items-center justify-between hover:bg-gradient-to-r hover:from-[#FF6500]/5 hover:to-transparent transition-all duration-300"
                  >
                    <div className="flex items-center gap-4">
                      <div
                        className={`p-3 rounded-full transition-all duration-300 ${
                          isOpen
                            ? "bg-[#FF6500] text-white"
                            : "bg-[#FF6500]/10 text-[#FF6500] group-hover:bg-[#FF6500]/20"
                        }`}
                      >
                        <IconComponent className="w-5 h-5" />
                      </div>
                      <h3
                        className={`text-lg md:text-xl font-bold transition-colors duration-300 ${
                          isOpen
                            ? "text-[#FF6500]"
                            : "text-gray-800 group-hover:text-[#FF6500]"
                        }`}
                      >
                        {faq.question}
                      </h3>
                    </div>

                    <div
                      className={`transition-all duration-300 ${
                        isOpen
                          ? "text-[#FF6500] rotate-180"
                          : "text-gray-400 group-hover:text-[#FF6500]"
                      }`}
                    >
                      <ChevronDown className="w-6 h-6" />
                    </div>
                  </button>

                  {/* Answer Content */}
                  <div
                    className={`transition-all duration-300 ease-in-out ${
                      isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                    } overflow-hidden`}
                  >
                    <div className="px-6 pb-6 pt-0">
                      <div className="pr-16 border-r-4 border-[#FF6500]/20">
                        <p className="text-gray-700 leading-relaxed text-base font-medium">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Contact Support Section */}
        <div className="mt-16 text-center">
          <div className="relative p-8 rounded-2xl  border border-[#FCB190]/30 shadow-lg backdrop-blur-sm">
            <div className="max-w-2xl mx-auto mb-6">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                لم تجد إجابة سؤالك؟
              </h2>
              <p className="text-gray-600 text-lg">
                فريق الدعم الفني جاهز لمساعدتك على مدار الساعة
              </p>
            </div>

            <div className="inline-flex flex-col sm:flex-row gap-4">
             
              <ReusabelSubbortBtn
                phone={"01503135009"}
                text="تحدث مع فريق الدعم"
                background={true}
              />

              <button className="group cursor-pointer relative px-8 py-4 bg-white border-2 border-[#FF6500] rounded-full font-bold text-lg text-[#FF6500] hover:bg-[#FF6500] hover:text-white transition-all duration-300 hover:scale-105">
                <span className="relative z-10 flex items-center gap-2">
                  <HelpCircle className="w-5 h-5" />
                  مركز المساعدة
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArabicFAQSection;
