// "use client";
// import React, { useState, useEffect } from "react";
// const ProductSwiper = ({ items, kotId, onStartCooking, onMarkReady }) => {
//   const [currentIndex, setCurrentIndex] = useState(0);
//   const [cookingTimers, setCookingTimers] = useState({});

//   const handleStartCooking = (itemId) => {
//     onStartCooking(kotId, itemId);

//     // إعداد مؤقت الطبخ
//     const timerId = setTimeout(() => {
//       onMarkReady(kotId, itemId);
//       setCookingTimers((prev) => {
//         const newTimers = { ...prev };
//         delete newTimers[itemId];
//         return newTimers;
//       });
//     }, 13000); // 13 ثانية

//     setCookingTimers((prev) => ({
//       ...prev,
//       [itemId]: timerId,
//     }));
//   };

//   const nextItem = () => {
//     setCurrentIndex((prev) => (prev + 1) % items.length);
//   };

//   const prevItem = () => {
//     setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
//   };

//   if (items.length === 0) return null;

//   const currentItem = items[currentIndex];
  
//   const getItemStatusIcon = (status) => {
//     switch (status) {
//       case "cooking":
//         return <ChefHat className="w-4 h-4 text-yellow-600" />;
//       case "ready":
//         return <CheckCircle className="w-4 h-4 text-green-600" />;
//       default:
//         return null;
//     }
//   };
//   return (
//     <div className="relative">
//       {/* عرض المنتج الحالي */}
//       <div className="border rounded-lg p-4 bg-white">
//         <div className="flex items-start gap-3">
//           <img
//             src={currentItem.image}
//             alt={currentItem.name}
//             className="w-16 h-16 rounded-lg object-cover border-2 border-gray-200"
//           />

//           <div className="flex-1">
//             <h4 className="font-medium text-gray-900">{currentItem.name}</h4>
//             {currentItem.size && (
//               <p className="text-sm text-gray-600">Size: {currentItem.size}</p>
//             )}

//             {/* عرض الإضافات */}
//             {currentItem.addons && currentItem.addons.length > 0 && (
//               <div className="mt-2">
//                 <p className="text-xs font-medium text-gray-700 mb-1">
//                   Addons:
//                 </p>
//                 {currentItem.addons.map((addon, index) => (
//                   <div
//                     key={index}
//                     className="text-xs text-blue-600 flex justify-between"
//                   >
//                     <span>
//                       + {addon.name} x{addon.quantity}
//                     </span>
//                     <span>{(addon.price * addon.quantity).toFixed(2)} L.E</span>
//                   </div>
//                 ))}
//               </div>
//             )}

//             {/* عرض التعليق */}
//             {currentItem.comment && (
//               <div className="mt-2">
//                 <p className="text-xs text-orange-600 italic">
//                   "{currentItem.comment}"
//                 </p>
//               </div>
//             )}

//             {/* حالة المنتج وأزرار التحكم */}
//             <div className="mt-3 flex items-center justify-between">
//               <div className="flex items-center gap-2">
//                 {getItemStatusIcon(currentItem.status)}
//                 <span className="text-sm font-medium capitalize">
//                   {currentItem.status}
//                 </span>
//                 {cookingTimers[currentItem.id] && (
//                   <span className="text-xs text-yellow-600">(Cooking...)</span>
//                 )}
//               </div>

//               {currentItem.status === "pending" && (
//                 <button
//                   onClick={() => handleStartCooking(currentItem.id)}
//                   className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md text-sm hover:bg-yellow-200 transition-colors"
//                 >
//                   Start Cooking
//                 </button>
//               )}

//               {currentItem.status === "cooking" &&
//                 !cookingTimers[currentItem.id] && (
//                   <button
//                     onClick={() => onMarkReady(kotId, currentItem.id)}
//                     className="px-3 py-1 bg-green-100 text-green-800 rounded-md text-sm hover:bg-green-200 transition-colors"
//                   >
//                     Mark Ready
//                   </button>
//                 )}
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* أزرار التنقل */}
//       {items.length > 1 && (
//         <div className="absolute inset-y-0 flex items-center">
//           <button
//             onClick={prevItem}
//             className="absolute -left-4 w-8 h-8 bg-white border border-gray-300 rounded-full flex items-center justify-center shadow-sm hover:bg-gray-50"
//           >
//             ←
//           </button>
//           <button
//             onClick={nextItem}
//             className="absolute -right-4 w-8 h-8 bg-white border border-gray-300 rounded-full flex items-center justify-center shadow-sm hover:bg-gray-50"
//           >
//             →
//           </button>
//         </div>
//       )}

//       {/* مؤشر الصفحات */}
//       {items.length > 1 && (
//         <div className="flex justify-center mt-2 gap-1">
//           {items.map((_, index) => (
//             <button
//               key={index}
//               onClick={() => setCurrentIndex(index)}
//               className={`w-2 h-2 rounded-full ${
//                 index === currentIndex ? "bg-[#FF6500]" : "bg-gray-300"
//               }`}
//             />
//           ))}
//         </div>
//       )}

//       {/* عداد المنتجات */}
//       <div className="text-center mt-2">
//         <span className="text-sm text-gray-600">
//           {currentIndex + 1} of {items.length}
//         </span>
//       </div>
//     </div>
//   );
// };

// export default ProductSwiper
"use client";
import React, { useState, useEffect } from "react";
import { ChefHat, CheckCircle, Clock, ArrowLeft, ArrowRight } from "lucide-react";

const ProductSwiper = ({ items, kotId, onStartCooking, onMarkReady }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [cookingTimers, setCookingTimers] = useState({});
  const [timeLeft, setTimeLeft] = useState({});

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeLeft(prev => {
        const newTimeLeft = { ...prev };
        Object.keys(newTimeLeft).forEach(itemId => {
          if (newTimeLeft[itemId] > 0) {
            newTimeLeft[itemId] -= 1;
          } else {
            delete newTimeLeft[itemId];
          }
        });
        return newTimeLeft;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleStartCooking = (itemId) => {
    onStartCooking(kotId, itemId);
    
    // إعداد العداد التنازلي
    setTimeLeft(prev => ({
      ...prev,
      [itemId]: 13
    }));

    const timerId = setTimeout(() => {
      onMarkReady(kotId, itemId);
      setCookingTimers((prev) => {
        const newTimers = { ...prev };
        delete newTimers[itemId];
        return newTimers;
      });
      setTimeLeft(prev => {
        const newTimeLeft = { ...prev };
        delete newTimeLeft[itemId];
        return newTimeLeft;
      });
    }, 13000);

    setCookingTimers((prev) => ({
      ...prev,
      [itemId]: timerId,
    }));
  };

  const nextItem = () => {
    setCurrentIndex((prev) => (prev + 1) % items.length);
  };

  const prevItem = () => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
  };

  if (items.length === 0) return null;

  const currentItem = items[currentIndex];
  
  const getStatusBadge = (status, itemId) => {
    switch (status) {
      case "pending":
        return (
          <div className="flex items-center gap-1 px-2 py-1 bg-red-50 text-red-700 rounded-full text-xs font-medium">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            Pending
          </div>
        );
      case "cooking":
        return (
          <div className="flex items-center gap-1 px-2 py-1 bg-yellow-50 text-yellow-700 rounded-full text-xs font-medium">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            Cooking
            {timeLeft[itemId] && (
              <span className="ml-1 text-xs">({timeLeft[itemId]}s)</span>
            )}
          </div>
        );
      case "ready":
        return (
          <div className="flex items-center gap-1 px-2 py-1 bg-green-50 text-green-700 rounded-full text-xs font-medium">
            <CheckCircle className="w-3 h-3" />
            Ready
          </div>
        );
      default:
        return null;
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="relative bg-gradient-to-br from-gray-50 to-white rounded-xl p-4 border shadow-sm">
      {/* الشريط العلوي */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
            <ChefHat className="w-4 h-4 text-white" />
          </div>
          <span className="font-semibold text-gray-800">Items</span>
        </div>
        {items.length > 1 && (
          <div className="text-sm text-gray-500 font-medium">
            {currentIndex + 1} / {items.length}
          </div>
        )}
      </div>

      {/* عرض المنتج الحالي */}
      <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-md">
        <div className="flex gap-4">
          {/* صورة المنتج */}
          <div className="relative">
            <img
              src={currentItem.image}
              alt={currentItem.name}
              className="w-20 h-20 rounded-lg object-cover border-2 border-gray-100 shadow-sm"
            />
            {/* مؤشر الحالة على الصورة */}
            <div className="absolute -top-1 -right-1">
              {currentItem.status === "cooking" && (
                <div className="w-4 h-4 bg-yellow-500 rounded-full animate-pulse"></div>
              )}
              {currentItem.status === "ready" && (
                <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-3 h-3 text-white" />
                </div>
              )}
            </div>
          </div>

          <div className="flex-1 min-w-0">
            {/* اسم المنتج والحالة */}
            <div className="flex flex-col items-start justify-between mb-2">
              <h4 className="font-semibold text-gray-900 text-sm truncate pr-2">
                {currentItem.name}
              </h4>
              {getStatusBadge(currentItem.status, currentItem.id)}
            </div>

            {/* تفاصيل المنتج */}
            {currentItem.size && (
              <div className="flex items-center gap-1 mb-2">
                <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                <span className="text-xs text-gray-600">Size: {currentItem.size}</span>
              </div>
            )}

            {/* الإضافات */}
            {currentItem.addons && currentItem.addons.length > 0 && (
              <div className="mb-2">
                <div className="flex flex-wrap gap-1">
                  {currentItem.addons.slice(0, 2).map((addon, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-md font-medium"
                    >
                      +{addon.name} x{addon.quantity}
                    </span>
                  ))}
                  {currentItem.addons.length > 2 && (
                    <span className="px-2 py-1 bg-gray-50 text-gray-600 text-xs rounded-md">
                      +{currentItem.addons.length - 2} more
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* التعليق */}
            {currentItem.comment && (
              <div className="mb-3">
                <div className="bg-orange-50 border-l-2 border-orange-300 p-2 rounded">
                  <p className="text-xs text-orange-700 italic">
                    "{currentItem.comment}"
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            {timeLeft[currentItem.id] && (
              <div className="flex items-center gap-1 text-xs text-yellow-600 bg-yellow-50 px-2 py-1 rounded-full">
                <Clock className="w-3 h-3" />
                {formatTime(timeLeft[currentItem.id])}
              </div>
            )}
          </div>

          <div className="flex gap-2">
            {currentItem.status === "pending" && (
              <button
                onClick={() => handleStartCooking(currentItem.id)}
                className="flex items-center gap-1 px-3 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-lg text-sm font-medium hover:from-yellow-500 hover:to-orange-600 transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
              >
                <ChefHat className="w-3 h-3" />
                Start Cooking
              </button>
            )}

            {currentItem.status === "cooking" && !cookingTimers[currentItem.id] && (
              <button
                onClick={() => onMarkReady(kotId, currentItem.id)}
                className="flex items-center gap-1 px-3 py-2 bg-gradient-to-r from-green-400 to-green-600 text-white rounded-lg text-sm font-medium hover:from-green-500 hover:to-green-700 transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
              >
                <CheckCircle className="w-3 h-3" />
                Mark Ready
              </button>
            )}
          </div>
        </div>
      </div>

      {/* مؤشر التنقل */}
      {items.length > 1 && (
        <div className="flex justify-center mt-4 gap-2">
          {items.map((item, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`transition-all duration-300 ${
                index === currentIndex
                  ? "w-8 h-2 bg-gradient-to-r from-orange-400 to-red-500 rounded-full"
                  : "w-2 h-2 bg-gray-300 rounded-full hover:bg-gray-400"
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductSwiper;