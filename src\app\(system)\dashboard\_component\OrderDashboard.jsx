// import React from 'react'

// const OrderDashboard = () => {
//   return (
//       <div className="bg-white mt-8 p-6 rounded-2xl shadow-sm border border-gray-100">
//         <h3 className="text-lg font-semibold text-gray-900 mb-4">طلبات اليوم</h3>
//         {/* Example orders data */}
//         {false ? (
//           <div className="text-center text-gray-500 py-8">
//             <span className="text-md font-medium">لا توجد طلبات اليوم</span>
//           </div>
//         ) : (
//           <div className="overflow-x-auto">
//             <table className="min-w-full divide-y divide-gray-200 text-right">
//               <thead className="bg-gray-50">
//                 <tr>
//                   <th className="px-4 py-2 text-xs font-bold text-gray-600">رقم الطلب</th>
//                   <th className="px-4 py-2 text-xs font-bold text-gray-600">العميل</th>
//                   <th className="px-4 py-2 text-xs font-bold text-gray-600">الوقت</th>
//                   <th className="px-4 py-2 text-xs font-bold text-gray-600">الإجمالي</th>
//                   <th className="px-4 py-2 text-xs font-bold text-gray-600">الحالة</th>
//                 </tr>
//               </thead>
//               <tbody className="bg-white divide-y divide-gray-100">
//                 <tr>
//                   <td className="px-4 py-2">#1023</td>
//                   <td className="px-4 py-2">أحمد علي</td>
//                   <td className="px-4 py-2">10:30 ص</td>
//                   <td className="px-4 py-2">L.E 250</td>
//                   <td className="px-4 py-2"><span className="inline-block px-2 py-1 text-xs rounded bg-green-100 text-green-700">مكتمل</span></td>
//                 </tr>
//                 <tr>
//                   <td className="px-4 py-2">#1024</td>
//                   <td className="px-4 py-2">منى حسن</td>
//                   <td className="px-4 py-2">11:15 ص</td>
//                   <td className="px-4 py-2">L.E 180</td>
//                   <td className="px-4 py-2"><span className="inline-block px-2 py-1 text-xs rounded bg-yellow-100 text-yellow-700">قيد التنفيذ</span></td>
//                 </tr>
//                 <tr>
//                   <td className="px-4 py-2">#1025</td>
//                   <td className="px-4 py-2">سارة محمد</td>
//                   <td className="px-4 py-2">12:05 م</td>
//                   <td className="px-4 py-2">L.E 320</td>
//                   <td className="px-4 py-2"><span className="inline-block px-2 py-1 text-xs rounded bg-red-100 text-red-700">ملغي</span></td>
//                 </tr>
//               </tbody>
//             </table>
//           </div>
//         )}
//       </div>
//   )
// }

// export default OrderDashboard
import React from 'react'

const OrderDashboard = () => {
  return (
    <div className="bg-white mt-8 p-6 rounded-2xl shadow-sm border border-gray-100">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Orders</h3>
      {/* Example orders data */}
      {false ? (
        <div className="text-center text-gray-500 py-8">
          <span className="text-md font-medium">No orders today</span>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 text-left">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-xs font-bold text-gray-600">Order Number</th>
                <th className="px-4 py-2 text-xs font-bold text-gray-600">Customer</th>
                <th className="px-4 py-2 text-xs font-bold text-gray-600">Time</th>
                <th className="px-4 py-2 text-xs font-bold text-gray-600">Total</th>
                <th className="px-4 py-2 text-xs font-bold text-gray-600">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              <tr>
                <td className="px-4 py-2">#1023</td>
                <td className="px-4 py-2">Ahmed Ali</td>
                <td className="px-4 py-2">10:30 AM</td>
                <td className="px-4 py-2">L.E 250</td>
                <td className="px-4 py-2"><span className="inline-block px-2 py-1 text-xs rounded bg-green-100 text-green-700">Completed</span></td>
              </tr>
              <tr>
                <td className="px-4 py-2">#1024</td>
                <td className="px-4 py-2">Mona Hassan</td>
                <td className="px-4 py-2">11:15 AM</td>
                <td className="px-4 py-2">L.E 180</td>
                <td className="px-4 py-2"><span className="inline-block px-2 py-1 text-xs rounded bg-yellow-100 text-yellow-700">In Progress</span></td>
              </tr>
              <tr>
                <td className="px-4 py-2">#1025</td>
                <td className="px-4 py-2">Sarah Mohammed</td>
                <td className="px-4 py-2">12:05 PM</td>
                <td className="px-4 py-2">L.E 320</td>
                <td className="px-4 py-2"><span className="inline-block px-2 py-1 text-xs rounded bg-red-100 text-red-700">Cancelled</span></td>
              </tr>
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}

export default OrderDashboard