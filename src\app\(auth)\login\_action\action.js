// // // "use server"

// // // import axios from "axios"
// // // import { cookies } from "next/headers"

// // // export async function addLoginForm(prevState, formData) {
// // //     try {
// // //         const payload = {
// // //             username: formData.get("username"),
// // //             password: formData.get("password"),
// // //         }

// // //         const res = await axios.post("https://fakestoreapi.com/auth/login", payload)
// // //         console.log("Login payload:", payload)
// // //         console.log("API response:", res.data)


// // //         if (res.data?.token) {
// // //             const CookieStore = cookies()
// // //             await  CookieStore.set("token",res.data.token)
// // //             return {
// // //                 success: true,
// // //                 message: "Login successful! Redirecting...",
// // //             }

// // //         } else {
// // //             return {
// // //                 success: false,
// // //                 message: "Invalid credentials",
// // //             }
// // //         }

// // //     } catch (error) {
// // //         console.error("Server error:", error)
// // //        if (error.response?.status === 401) {
// // //         return {
// // //             success: false,
// // //             message:  "user not found",
// // //         }
// // //     }
// // //     }
// // // }
// // "use server"
// // import axios from "axios"
// // import { cookies } from "next/headers"

// // const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'

// // export async function addLoginForm(prevState, formData) {
// //     try {
// //         // Extract form data
// //         const username = formData.get('username')
// //         const password = formData.get('password')

// //         // Validate input
// //         if (!username || !password) {
// //             return {
// //                 success: false,
// //                 message: "اسم المستخدم وكلمة المرور مطلوبان"
// //             }
// //         }

// //         // Prepare payload - API expects email field
// //         const payload = {
// //             email: username, // API expects email, but we're sending username
// //             password: password
// //         }

// //         console.log("Login payload:", payload)

// //         // Call your actual login API
// //         const res = await axios.post(`${baseURL}/auth/login`, payload, {
// //             headers: {
// //                 'Content-Type': 'application/json',
// //                 'Accept': 'application/json'
// //             }
// //         })

// //         console.log("API response:", res.data)

// //         // Check if login was successful
// //         if (res.data?.success && res.data?.data?.token) {
// //             const cookieStore = await cookies()

// //             // Store the token from API response
// //             await cookieStore.set('auth_token', res.data.data.token, {
// //                 httpOnly: true,
// //                 secure: process.env.NODE_ENV === 'production',
// //                 sameSite: 'strict',
// //                 maxAge: 60 * 60 * 24 * 7 // 7 days
// //             })

// //             // Store user data
// //             await cookieStore.set('user_data', JSON.stringify(res.data.data.user), {
// //                 httpOnly: true,
// //                 secure: process.env.NODE_ENV === 'production',
// //                 sameSite: 'strict',
// //                 maxAge: 60 * 60 * 24 * 7 // 7 days
// //             })

// //             return {
// //                 success: true,
// //                 message: res.data.message || "تم تسجيل الدخول بنجاح",
// //                 user: res.data.data.user,
// //                 token: res.data.data.token
// //             }
// //         } else {
// //             return {
// //                 success: false,
// //                 message: res.data?.message || "فشل تسجيل الدخول"
// //             }
// //         }

// //     } catch (error) {
// //         console.error("Server error:", error)

// //         // Handle different error responses
// //         if (error.response?.status === 401) {
// //             return {
// //                 success: false,
// //                 message: "بيانات الدخول غير صحيحة"
// //             }
// //         } else if (error.response?.data?.message) {
// //             return {
// //                 success: false,
// //                 message: error.response.data.message
// //             }
// //         } else if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
// //             return {
// //                 success: false,
// //                 message: "خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى."
// //             }
// //         } else {
// //             return {
// //                 success: false,
// //                 message: "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى."
// //             }
// //         }
// //     }
// // }

// // // Helper function to get stored token
// // export async function getAuthToken() {
// //     try {
// //         const cookieStore = await cookies()
// //         return cookieStore.get('auth_token')?.value || null
// //     } catch (error) {
// //         console.error('Error getting auth token:', error)
// //         return null
// //     }
// // }

// // // Helper function to get stored user data
// // export async function getUserData() {
// //     try {
// //         const cookieStore = await cookies()
// //         const userData = cookieStore.get('user_data')?.value
// //         return userData ? JSON.parse(userData) : null
// //     } catch (error) {
// //         console.error('Error getting user data:', error)
// //         return null
// //     }
// // }

// // // Logout action
// // export async function logoutAction() {
// //     try {
// //         const cookieStore = await cookies()

// //         // Clear cookies
// //         cookieStore.delete('auth_token')
// //         cookieStore.delete('user_data')

// //         return {
// //             success: true,
// //             message: "تم تسجيل الخروج بنجاح"
// //         }
// //     } catch (error) {
// //         console.error('Logout action error:', error)
// //         return {
// //             success: false,
// //             message: "حدث خطأ أثناء تسجيل الخروج"
// //         }
// //     }
// // }
// "use server"
// import axios from "axios"
// import { cookies } from "next/headers"

// const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'

// export async function addLoginForm(prevState, formData) {
//     try {
//         // Extract form data
//         const email = formData.get('email')
//         const password = formData.get('password')

//         // Validate input
//         if (!email || !password) {
//             return {
//                 success: false,
//                 message: "البريد الإلكتروني وكلمة المرور مطلوبان"
//             }
//         }

//         // Validate email format
//         const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
//         if (!emailRegex.test(email)) {
//             return {
//                 success: false,
//                 message: "يرجى إدخال بريد إلكتروني صحيح"
//             }
//         }

//         // Prepare payload
//         const payload = {
//             email: email,
//             password: password
//         }

//         console.log("Login payload:", payload)

//         // Call your actual login API
//         const res = await axios.post(`${baseURL}/auth/login`, payload, {
//             headers: {
//                 'Content-Type': 'application/json',
//                 'Accept': 'application/json'
//             }
//         })

//         console.log("API response:", res.data)

//         // Check if login was successful
//         if (res.data?.success && res.data?.data?.token) {
//             const cookieStore = await cookies()

//             // Store the token from API response
//             await cookieStore.set('auth_token', res.data.data.token, {
//                 httpOnly: true,
//                 secure: process.env.NODE_ENV === 'production',
//                 sameSite: 'strict',
//                 maxAge: 60 * 60 * 24 * 7 // 7 days
//             })

//             // Store user data
//             await cookieStore.set('user_data', JSON.stringify(res.data.data.user), {
//                 httpOnly: true,
//                 secure: process.env.NODE_ENV === 'production',
//                 sameSite: 'strict',
//                 maxAge: 60 * 60 * 24 * 7 // 7 days
//             })

//             return {
//                 success: true,
//                 message: res.data.message || "تم تسجيل الدخول بنجاح",
//                 user: res.data.data.user,
//                 token: res.data.data.token
//             }
//         } else {
//             return {
//                 success: false,
//                 message: res.data?.message || "فشل تسجيل الدخول"
//             }
//         }

//     } catch (error) {
//         console.error("Server error:", error)

//         // Handle different error responses
//         if (error.response?.status === 401) {
//             return {
//                 success: false,
//                 message: "بيانات الدخول غير صحيحة"
//             }
//         } else if (error.response?.status === 422) {
//             return {
//                 success: false,
//                 message: "البيانات المدخلة غير صحيحة"
//             }
//         } else if (error.response?.data?.message) {
//             return {
//                 success: false,
//                 message: error.response.data.message
//             }
//         } else if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
//             return {
//                 success: false,
//                 message: "خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى."
//             }
//         } else {
//             return {
//                 success: false,
//                 message: "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى."
//             }
//         }
//     }
// }

// // Helper function to get stored token
// export async function getAuthToken() {
//     try {
//         const cookieStore = await cookies()
//         return cookieStore.get('auth_token')?.value || null
//     } catch (error) {
//         console.error('Error getting auth token:', error)
//         return null
//     }
// }

// // Helper function to get stored user data
// export async function getUserData() {
//     try {
//         const cookieStore = await cookies()
//         const userData = cookieStore.get('user_data')?.value
//         return userData ? JSON.parse(userData) : null
//     } catch (error) {
//         console.error('Error getting user data:', error)
//         return null
//     }
// }

// // Logout action
// export async function logoutAction() {
//     try {
//         const cookieStore = await cookies()

//         // Clear cookies
//         cookieStore.delete('auth_token')
//         cookieStore.delete('user_data')

//         return {
//             success: true,
//             message: "تم تسجيل الخروج بنجاح"
//         }
//     } catch (error) {
//         console.error('Logout action error:', error)
//         return {
//             success: false,
//             message: "حدث خطأ أثناء تسجيل الخروج"
//         }
//     }
// }
"use server"
import axios from "axios"

const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'

export async function addLoginForm(prevState, formData) {
    try {
        // Extract form data
        const email = formData.get('email')
        const password = formData.get('password')

        // Validate input
        if (!email || !password) {
            return {
                success: false,
                message: "البريد الإلكتروني وكلمة المرور مطلوبان"
            }
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(email)) {
            return {
                success: false,
                message: "يرجى إدخال بريد إلكتروني صحيح"
            }
        }

        // Prepare payload
        const payload = {
            email: email,
            password: password
        }

        console.log("Login payload:", payload)

        // Call your actual login API
        const res = await axios.post(`${baseURL}/api/auth/login`, payload, {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })

        console.log("API response:", res.data)

        // Check if login was successful
        if (res.data?.success && res.data?.data?.token) {
            // Return the data to be stored in localStorage on client side
            return {
                success: true,
                message: res.data.message || "تم تسجيل الدخول بنجاح",
                user: res.data.data.user,
                token: res.data.data.token
            }
        } else {
            return {
                success: false,
                message: res.data?.message || "فشل تسجيل الدخول"
            }
        }

    } catch (error) {
        console.error("Server error:", error)

        // Handle different error responses
        if (error.response?.status === 401) {
            return {
                success: false,
                message: "بيانات الدخول غير صحيحة"
            }
        } else if (error.response?.status === 422) {
            return {
                success: false,
                message: "البيانات المدخلة غير صحيحة"
            }
        } else if (error.response?.data?.message) {
            return {
                success: false,
                message: error.response.data.message
            }
        } else if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
            return {
                success: false,
                message: "خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى."
            }
        } else {
            return {
                success: false,
                message: "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى."
            }
        }
    }
}

// Logout action - server side API call
export async function logoutFromServer(token) {
    try {
        if (token) {
            await axios.post(`${baseURL}/api/auth/logout`, {}, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
        }

        return {
            success: true,
            message: "تم تسجيل الخروج بنجاح"
        }
    } catch (error) {
        console.error('Server logout error:', error)
        return {
            success: true, // Still consider it successful to clear local data
            message: "تم تسجيل الخروج محلياً"
        }
    }
}