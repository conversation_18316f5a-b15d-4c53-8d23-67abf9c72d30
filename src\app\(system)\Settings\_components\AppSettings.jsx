import React, { useState } from 'react';
import { Globe, Clock, CreditCard, Settings, Eye, EyeOff, MapPin } from 'lucide-react';

const AppSettings = () => {
  const [settingsData, setSettingsData] = useState({
    country: 'Egypt',
    timezone: 'Africa/Cairo',
    currency: 'Egyptian pound (Egp)',
    hideTodayOrders: false,
    hideNewReservation: false,
    hideNewWaiterRequest: false
  });

  const handleSelectChange = (field, value) => {
    setSettingsData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleToggleChange = (field, value) => {
    setSettingsData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const countries = [
    'Egypt', 'United States', 'United Kingdom', 'Germany', 'France', 
    'Italy', 'Spain', 'Canada', 'Australia', 'Japan', 'China', 'India'
  ];

  const timezones = [
    'Africa/Cairo', 'America/New_York', 'Europe/London', 'Europe/Berlin',
    'Europe/Paris', 'Europe/Rome', 'Europe/Madrid', 'America/Toronto',
    'Australia/Sydney', 'Asia/Tokyo', 'Asia/Shanghai', 'Asia/Kolkata'
  ];

  const currencies = [
    'Egyptian pound (Egp)', 'US Dollar (USD)', 'British Pound (GBP)', 
    'Euro (EUR)', 'Canadian Dollar (CAD)', 'Australian Dollar (AUD)',
    'Japanese Yen (JPY)', 'Chinese Yuan (CNY)', 'Indian Rupee (INR)'
  ];

  const navigationOptions = [
    {
      id: 'hideTodayOrders',
      label: 'Hide Today Orders',
      description: 'Enable this to hide today orders widget from top navigation.',
      icon: Eye
    },
    {
      id: 'hideNewReservation',
      label: 'Hide New Reservation',
      description: 'Enable this to hide new reservation widget from top navigation.',
      icon: Eye
    },
    {
      id: 'hideNewWaiterRequest',
      label: 'Hide New Waiter Request',
      description: 'Enable this to hide new waiter request widget from top navigation.',
      icon: Eye
    }
  ];

  const handleSave = () => {
    console.log('Settings saved:', settingsData);
    // Add your save logic here
  };

  return (
    <div className="space-y-8">
      <div className=" rounded-2xl p-8 border border-blue-200">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-[#FF6500] to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
            <Settings className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">App Settings</h2>
            <p className="text-gray-600 mt-1">Configure your application preferences and regional settings</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Regional Settings */}
          <div className="bg-white rounded-2xl p-6 border-2 border-gray-100 shadow-sm">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-[#FF6500] to-orange-600 rounded-lg flex items-center justify-center">
                <Globe className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">Restaurant's Country, Timezone & Currency</h3>
                <p className="text-sm text-gray-600">Set your restaurant's regional preferences</p>
              </div>
            </div>

            <div className="space-y-6">
              {/* Country */}
              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-[#FF6500]" />
                  Country
                </label>
                <div className="relative">
                  <select
                    value={settingsData.country}
                    onChange={(e) => handleSelectChange('country', e.target.value)}
                    className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm appearance-none cursor-pointer"
                  >
                    {countries.map((country) => (
                      <option key={country} value={country}>
                        {country}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Timezone */}
              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <Clock className="w-4 h-4 text-[#FF6500]" />
                  Time Zone
                </label>
                <div className="relative">
                  <select
                    value={settingsData.timezone}
                    onChange={(e) => handleSelectChange('timezone', e.target.value)}
                    className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm appearance-none cursor-pointer"
                  >
                    {timezones.map((timezone) => (
                      <option key={timezone} value={timezone}>
                        {timezone}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Currency */}
              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <CreditCard className="w-4 h-4 text-[#FF6500]" />
                  Currency
                </label>
                <div className="relative">
                  <select
                    value={settingsData.currency}
                    onChange={(e) => handleSelectChange('currency', e.target.value)}
                    className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm appearance-none cursor-pointer"
                  >
                    {currencies.map((currency) => (
                      <option key={currency} value={currency}>
                        {currency}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Settings */}
          <div className="bg-white rounded-2xl p-6 border-2 border-gray-100 shadow-sm">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                <EyeOff className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">Hide Top Navigation</h3>
                <p className="text-sm text-gray-600">Control visibility of top navigation widgets</p>
              </div>
            </div>

            <div className="space-y-6">
              {navigationOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <div key={option.id} className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settingsData[option.id]}
                        onChange={(e) => handleToggleChange(option.id, e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-100 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#FF6500]"></div>
                    </label>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <Icon className="w-5 h-5 text-[#FF6500]" />
                        <h4 className="text-sm font-semibold text-gray-900">{option.label}</h4>
                      </div>
                      <p className="text-sm text-gray-600 leading-relaxed">{option.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="mt-8 flex justify-end">
          <button
            onClick={handleSave}
            className="px-8 py-3 bg-gradient-to-r from-[#FF6500] to-orange-600 text-white rounded-xl font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-2"
          >
            <Settings className="w-5 h-5" />
            Save Settings
          </button>
        </div>
      </div>
    </div>
  );
};

export default AppSettings;