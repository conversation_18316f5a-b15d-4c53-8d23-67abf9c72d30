import React, { useState } from 'react';
import { ExternalLink, ChevronDown, Clock } from 'lucide-react';

const DeliverySettings = () => {
  const [feeCalculationMethod, setFeeCalculationMethod] = useState('Fixed Rate');
  const [distanceUnit, setDistanceUnit] = useState('Kilometers (km)');
  const [maxDeliveryRadius, setMaxDeliveryRadius] = useState('0');
  const [fixedFee, setFixedFee] = useState('0.00');
  const [freeDeliveryAmount, setFreeDeliveryAmount] = useState('0.00');
  const [freeDeliveryRadius, setFreeDeliveryRadius] = useState('0.00');
  const [deliveryStartTime, setDeliveryStartTime] = useState('');
  const [deliveryEndTime, setDeliveryEndTime] = useState('');
  const [averageSpeed, setAverageSpeed] = useState('30');
  const [timeBuffer, setTimeBuffer] = useState('');

  const TimeInput = ({ value, onChange, placeholder }) => {
    return (
      <div className="relative">
        <input
          type="time"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] text-gray-700"
          placeholder={placeholder}
        />
        <Clock className="absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
      </div>
    );
  };

  const CustomSelect = ({ value, onChange, options, placeholder }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <div className="relative">
        <button
          type="button"
          className="w-full px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] flex items-center justify-between"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="text-gray-700">{value || placeholder}</span>
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </button>
        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
            {options.map((option) => (
              <button
                key={option}
                type="button"
                className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                onClick={() => {
                  onChange(option);
                  setIsOpen(false);
                }}
              >
                {option}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-semibold text-gray-900 mb-6">Delivery Settings</h1>
      
      {/* Warning Banner */}
      <div className="mb-8 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <div className="flex items-center justify-between">
          <p className="text-yellow-800 text-sm">
            Delivery settings require branch coordinates. Please update your branch location first.
          </p>
          <button className="flex items-center text-[#FF6500] hover:text-[#e55a00] font-medium text-sm">
            Branch Settings
            <ExternalLink className="ml-1 h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Fee Details Section */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Fee Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fee Calculation Method
            </label>
            <CustomSelect
              value={feeCalculationMethod}
              onChange={setFeeCalculationMethod}
              options={['Fixed Rate', 'Distance Based', 'Weight Based']}
              placeholder="Select method"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Distance Unit
            </label>
            <CustomSelect
              value={distanceUnit}
              onChange={setDistanceUnit}
              options={['Kilometers (km)', 'Miles (mi)']}
              placeholder="Select unit"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Maximum Delivery Radius
            </label>
            <div className="relative">
              <input
                type="number"
                value={maxDeliveryRadius}
                onChange={(e) => setMaxDeliveryRadius(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] pr-10"
                placeholder="0"
              />
              <span className="absolute right-3 top-2 text-gray-500 text-sm">km</span>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fixed Fee
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500 text-sm">LE</span>
              <input
                type="number"
                step="0.01"
                value={fixedFee}
                onChange={(e) => setFixedFee(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500]"
                placeholder="0.00"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Free Delivery Options Section */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Free Delivery Options</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Free Delivery Over Amount
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500 text-sm">LE</span>
              <input
                type="number"
                step="0.01"
                value={freeDeliveryAmount}
                onChange={(e) => setFreeDeliveryAmount(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500]"
                placeholder="0.00"
              />
            </div>
            <p className="text-sm text-gray-500 mt-2">Leave empty to disable this option</p>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Free Delivery Within Radius
            </label>
            <div className="relative">
              <input
                type="number"
                step="0.01"
                value={freeDeliveryRadius}
                onChange={(e) => setFreeDeliveryRadius(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-[#FF6500] pr-10"
                placeholder="0.00"
              />
              <span className="absolute right-3 top-2 text-gray-500 text-sm">km</span>
            </div>
            <p className="text-sm text-gray-500 mt-2">Leave empty to disable this option</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeliverySettings;