"use client"
import React, { useState } from 'react';
import { Clock, ArrowUp, ArrowDown } from 'lucide-react';

export default function ReservationSettings() {
  const [activeDay, setActiveDay] = useState('Monday');
  const [timeSlots, setTimeSlots] = useState({
    Monday: {
      Breakfast: { startTime: '7:00', endTime: '10:00', duration: 30, available: true },
      Lunch: { startTime: '12:00', endTime: '15:00', duration: 45, available: true },
      Dinner: { startTime: '18:00', endTime: '22:00', duration: 60, available: true }
    },
    Tuesday: {
      Breakfast: { startTime: '7:00', endTime: '10:00', duration: 30, available: true },
      Lunch: { startTime: '12:00', endTime: '15:00', duration: 45, available: true },
      Dinner: { startTime: '18:00', endTime: '22:00', duration: 60, available: true }
    },
    Wednesday: {
      Breakfast: { startTime: '7:00', endTime: '10:00', duration: 30, available: true },
      Lunch: { startTime: '12:00', endTime: '15:00', duration: 45, available: true },
      Dinner: { startTime: '18:00', endTime: '22:00', duration: 60, available: true }
    },
    Thursday: {
      Breakfast: { startTime: '7:00', endTime: '10:00', duration: 30, available: true },
      Lunch: { startTime: '12:00', endTime: '15:00', duration: 45, available: true },
      Dinner: { startTime: '18:00', endTime: '22:00', duration: 60, available: true }
    },
    Friday: {
      Breakfast: { startTime: '7:00', endTime: '10:00', duration: 30, available: true },
      Lunch: { startTime: '12:00', endTime: '15:00', duration: 45, available: true },
      Dinner: { startTime: '18:00', endTime: '22:00', duration: 60, available: true }
    },
    Saturday: {
      Breakfast: { startTime: '7:00', endTime: '10:00', duration: 30, available: true },
      Lunch: { startTime: '12:00', endTime: '15:00', duration: 45, available: true },
      Dinner: { startTime: '18:00', endTime: '22:00', duration: 60, available: true }
    },
    Sunday: {
      Breakfast: { startTime: '7:00', endTime: '10:00', duration: 30, available: true },
      Lunch: { startTime: '12:00', endTime: '15:00', duration: 45, available: true },
      Dinner: { startTime: '18:00', endTime: '22:00', duration: 60, available: true }
    }
  });

  const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  const updateTimeSlot = (day, slotType, field, value) => {
    setTimeSlots(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        [slotType]: {
          ...prev[day][slotType],
          [field]: value
        }
      }
    }));
  };

  const adjustDuration = (day, slotType, increment) => {
    const currentDuration = timeSlots[day][slotType].duration;
    const newDuration = Math.max(15, currentDuration + increment);
    updateTimeSlot(day, slotType, 'duration', newDuration);
  };

  const TimeInput = ({ value, onChange, icon }) => (
    <div className="relative">
      <input
        type="time"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-gray-50"
      />
      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
        {icon}
      </div>
    </div>
  );

  const DurationControl = ({ duration, onAdjust }) => (
    <div className="flex items-center space-x-2">
      <input
        type="number"
        value={duration}
        onChange={(e) => onAdjust(0, parseInt(e.target.value) - duration)}
        className="w-16 px-2 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent bg-gray-50 text-center"
        min="15"
        step="15"
      />
      <span className="text-gray-500 text-sm">Minutes</span>
      <div className="flex flex-col">
        <button
          onClick={() => onAdjust(15)}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <ArrowUp size={14} className="text-gray-400" />
        </button>
        <button
          onClick={() => onAdjust(-15)}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <ArrowDown size={14} className="text-gray-400" />
        </button>
      </div>
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white">
      {/* Day Navigation */}
      <div className="flex space-x-2 mb-8">
        {days.map((day) => (
          <button
            key={day}
            onClick={() => setActiveDay(day)}
            className={`px-6 py-3 rounded-lg font-medium transition-all ${
              activeDay === day
                ? 'bg-blue-800 text-white shadow-lg'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {day}
          </button>
        ))}
      </div>

      {/* Active Day Content */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">{activeDay}</h2>
        
        {/* Table Header */}
        <div className="grid grid-cols-6 gap-4 mb-4">
          <div className="font-semibold text-gray-600 uppercase text-sm">Slot Type</div>
          <div className="font-semibold text-gray-600 uppercase text-sm">Start Time</div>
          <div className="font-semibold text-gray-600 uppercase text-sm">End Time</div>
          <div className="font-semibold text-gray-600 uppercase text-sm">Time Slot Difference</div>
          <div className="font-semibold text-gray-600 uppercase text-sm">Available</div>
          <div></div>
        </div>

        {/* Time Slots */}
        {Object.entries(timeSlots[activeDay]).map(([slotType, slot]) => (
          <div key={slotType} className="grid grid-cols-6 gap-4 items-center py-4 border-b border-gray-200 last:border-b-0">
            {/* Slot Type */}
            <div className="font-medium text-gray-800">{slotType}</div>
            
            {/* Start Time */}
            <div>
              <TimeInput
                value={slot.startTime}
                onChange={(value) => updateTimeSlot(activeDay, slotType, 'startTime', value)}
                icon={<Clock size={16} />}
              />
            </div>
            
            {/* End Time */}
            <div>
              <TimeInput
                value={slot.endTime}
                onChange={(value) => updateTimeSlot(activeDay, slotType, 'endTime', value)}
                icon={<Clock size={16} />}
              />
            </div>
            
            {/* Duration Control */}
            <div>
              <DurationControl
                duration={slot.duration}
                onAdjust={(increment) => adjustDuration(activeDay, slotType, increment)}
              />
            </div>
            
            {/* Available Toggle */}
            <div className="flex justify-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={slot.available}
                  onChange={(e) => updateTimeSlot(activeDay, slotType, 'available', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
              </label>
            </div>
            
            <div></div>
          </div>
        ))}
      </div>

      {/* Save Button */}
      <div className="mt-8">
        <button
          className="bg-blue-800 hover:bg-blue-900 text-white font-semibold px-8 py-3 rounded-lg transition-colors shadow-lg hover:shadow-xl"
          onClick={() => alert('Settings saved!')}
        >
          Save
        </button>
      </div>
    </div>
  );
}