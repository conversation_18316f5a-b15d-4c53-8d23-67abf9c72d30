"use client"
import React, { useState, useEffect } from 'react';
import { Search, Bell, ShoppingCart, Star, Heart, Plus, Menu, User, Filter, Grid, List } from 'lucide-react';

const GroceryApp = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [favorites, setFavorites] = useState(new Set());
  const [viewMode, setViewMode] = useState('grid');

  const deals = [
    {
      id: 1,
      title: "Fresh Vegetables & Fruits",
      discount: "25%",
      image: "🥕🥬🍅",
      color: "from-orange-500 to-red-500",
      customStyle: "linear-gradient(135deg, #FF6500, #FF8533)"
    },
    {
      id: 2,
      title: "Organic Products",
      discount: "30%",
      image: "🥑🌿🥒",
      color: "from-green-500 to-emerald-500",
      customStyle: "linear-gradient(135deg, #10B981, #059669)"
    },
    {
      id: 3,
      title: "Fresh Bakery Items",
      discount: "20%",
      image: "🥖🥐🍞",
      color: "from-amber-500 to-yellow-500",
      customStyle: "linear-gradient(135deg, #F59E0B, #EAB308)"
    },
    {
      id: 4,
      title: "Premium Dairy",
      discount: "15%",
      image: "🥛🧀🧈",
      color: "from-blue-500 to-cyan-500",
      customStyle: "linear-gradient(135deg, #3B82F6, #06B6D4)"
    }
  ];

  const categories = [
    { name: "Vegetables", icon: "🥬", color: "bg-gradient-to-br from-green-400 to-emerald-500", items: "120+ items" },
    { name: "Fruits", icon: "🍎", color: "bg-gradient-to-br from-red-400 to-pink-500", items: "80+ items" },
    { name: "Bakery", icon: "🥖", color: "bg-gradient-to-br from-orange-400 to-amber-500", items: "45+ items" },
    { name: "Dairy", icon: "🥛", color: "bg-gradient-to-br from-blue-400 to-cyan-500", items: "35+ items" },
    { name: "Meat & Fish", icon: "🥩", color: "bg-gradient-to-br from-red-500 to-rose-600", items: "25+ items" },
    { name: "Snacks", icon: "🍿", color: "bg-gradient-to-br from-purple-400 to-indigo-500", items: "60+ items" },
    { name: "Beverages", icon: "🥤", color: "bg-gradient-to-br from-teal-400 to-blue-500", items: "40+ items" },
    { name: "Frozen", icon: "🧊", color: "bg-gradient-to-br from-sky-400 to-blue-600", items: "30+ items" }
  ];

  const hotDeals = [
    {
      id: 1,
      name: "Fresh Red Apples",
      price: 4.99,
      originalPrice: 6.99,
      rating: 4.8,
      image: "🍎",
      discount: "29% OFF",
      description: "Crispy and sweet red apples"
    },
    {
      id: 2,
      name: "Organic Bananas",
      price: 2.99,
      originalPrice: 4.49,
      rating: 4.7,
      image: "🍌",
      discount: "33% OFF",
      description: "Fresh organic bananas"
    },
    {
      id: 3,
      name: "Fresh Avocados",
      price: 8.99,
      originalPrice: 12.99,
      rating: 4.9,
      image: "🥑",
      discount: "31% OFF",
      description: "Premium Hass avocados"
    },
    {
      id: 4,
      name: "Organic Strawberries",
      price: 6.99,
      originalPrice: 9.99,
      rating: 4.6,
      image: "🍓",
      discount: "30% OFF",
      description: "Sweet organic strawberries"
    },
    {
      id: 5,
      name: "Fresh Spinach",
      price: 3.49,
      originalPrice: 4.99,
      rating: 4.5,
      image: "🥬",
      discount: "30% OFF",
      description: "Fresh baby spinach leaves"
    },
    {
      id: 6,
      name: "Premium Salmon",
      price: 18.99,
      originalPrice: 24.99,
      rating: 4.8,
      image: "🐟",
      discount: "24% OFF",
      description: "Atlantic salmon fillet"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % deals.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const toggleFavorite = (id) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(id)) {
        newFavorites.delete(id);
      } else {
        newFavorites.add(id);
      }
      return newFavorites;
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 ">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-lg">
        <div className="max-w-7xl mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            {/* Logo & Brand */}
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="font-bold text-xl text-white">H</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                  HealthyStore
                </h1>
                <p className="text-sm text-gray-500 font-medium">Fresh • Organic • Delivered</p>
              </div>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-2xl mx-8">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search for fresh groceries, organic products..."
                  className="w-full pl-12 pr-16 py-3 bg-gray-100/80 rounded-2xl text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/30 focus:bg-white transition-all duration-300 border border-transparent"
                />
                <button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-orange-500 to-red-500 text-white p-2 rounded-xl hover:shadow-lg transition-all duration-300">
                  <Search className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-4">
              <button className="p-3 bg-gray-100 hover:bg-gray-200 rounded-2xl transition-all duration-300 relative">
                <Bell className="w-5 h-5 text-gray-600" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white"></div>
              </button>
              
              <button className="p-3 bg-gray-100 hover:bg-gray-200 rounded-2xl transition-all duration-300">
                <User className="w-5 h-5 text-gray-600" />
              </button>
              
              <div className="relative">
                <button className="p-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-2xl hover:shadow-lg transition-all duration-300 relative">
                  <ShoppingCart className="w-5 h-5" />
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
                    <span className="text-xs font-bold text-orange-500">3</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-8 py-8">
        {/* Hero Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Main Hero Slider */}
          <div className="lg:col-span-2">
            <div className="relative overflow-hidden rounded-3xl h-80 shadow-2xl">
              {deals.map((deal, index) => (
                <div
                  key={deal.id}
                  className={`absolute inset-0 transition-all duration-700 ease-in-out ${
                    index === currentSlide ? 'opacity-100 scale-100' : 'opacity-0 scale-105'
                  }`}
                >
                  <div className="h-full rounded-3xl p-8 flex items-center justify-between text-white relative overflow-hidden"
                       style={{background: deal.customStyle}}>
                    <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/10 rounded-3xl"></div>
                    <div className="relative z-10 max-w-sm">
                      <div className="bg-white/20 backdrop-blur-sm rounded-2xl px-4 py-2 mb-4 inline-block">
                        <span className="text-sm font-semibold">Super Deal</span>
                      </div>
                      <div className="text-5xl font-bold mb-3">{deal.discount}</div>
                      <div className="text-lg opacity-90 mb-6">{deal.title}</div>
                      <button className="bg-white text-gray-800 px-8 py-3 rounded-2xl font-semibold hover:shadow-lg hover:scale-105 transition-all duration-300">
                        Shop Now
                      </button>
                    </div>
                    <div className="text-8xl opacity-70 animate-pulse">{deal.image}</div>
                  </div>
                </div>
              ))}
              
              {/* Slider Navigation */}
              <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3">
                {deals.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`h-3 rounded-full transition-all duration-300 ${
                      index === currentSlide ? 'bg-white w-8' : 'bg-white/50 w-3'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Side Promotions */}
          <div className="space-y-6">
            <div className="bg-gradient-to-br from-green-500 to-emerald-600 rounded-3xl p-6 text-white shadow-xl">
              <div className="text-3xl mb-2">🌱</div>
              <h3 className="text-xl font-bold mb-2">Organic Week</h3>
              <p className="text-sm opacity-90 mb-4">Get 40% off on all organic products</p>
              <button className="bg-white text-green-600 px-4 py-2 rounded-xl text-sm font-semibold hover:shadow-md transition-all">
                Explore
              </button>
            </div>
            
            <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-3xl p-6 text-white shadow-xl">
              <div className="text-3xl mb-2">🚚</div>
              <h3 className="text-xl font-bold mb-2">Free Delivery</h3>
              <p className="text-sm opacity-90 mb-4">Orders above $50 get free delivery</p>
              <button className="bg-white text-purple-600 px-4 py-2 rounded-xl text-sm font-semibold hover:shadow-md transition-all">
                Order Now
              </button>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-2">Shop by Category</h2>
              <p className="text-gray-600">Browse through our fresh collections</p>
            </div>
            <button className="text-orange-500 font-semibold hover:text-orange-600 transition-colors flex items-center space-x-2">
              <span>View All</span>
              <Plus className="w-4 h-4" />
            </button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {categories.map((category, index) => (
              <div
                key={index}
                className="bg-white rounded-3xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 cursor-pointer group"
              >
                <div className={`w-16 h-16 ${category.color} rounded-3xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300`}>
                  <span className="text-2xl">{category.icon}</span>
                </div>
                <h3 className="font-semibold text-gray-800 text-center mb-2 text-sm">{category.name}</h3>
                <p className="text-xs text-gray-500 text-center">{category.items}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Hot Deals */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 mb-2">Hot Deals For You</h2>
              <p className="text-gray-600">Limited time offers on fresh products</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 bg-gray-100 rounded-2xl p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-xl transition-all ${viewMode === 'grid' ? 'bg-white shadow-md text-orange-500' : 'text-gray-500'}`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-xl transition-all ${viewMode === 'list' ? 'bg-white shadow-md text-orange-500' : 'text-gray-500'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
              <button className="text-orange-500 font-semibold hover:text-orange-600 transition-colors">
                View All
              </button>
            </div>
          </div>
          
          <div className={viewMode === 'grid' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
            {hotDeals.map((item) => (
              <div
                key={item.id}
                className={`bg-white rounded-3xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden group ${
                  viewMode === 'list' ? 'flex items-center p-4' : 'p-6'
                }`}
              >
                {viewMode === 'grid' ? (
                  <>
                    <div className="relative mb-6">
                      <div className="w-full h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center text-6xl mb-4">
                        {item.image}
                      </div>
                      <button
                        onClick={() => toggleFavorite(item.id)}
                        className="absolute top-2 right-2 p-2 bg-white/80 backdrop-blur-sm hover:bg-white rounded-xl transition-all duration-300 shadow-md"
                      >
                        <Heart
                          className={`w-4 h-4 ${
                            favorites.has(item.id)
                              ? 'text-red-500 fill-current'
                              : 'text-gray-400'
                          }`}
                        />
                      </button>
                      <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-3 py-1 rounded-full font-semibold">
                        {item.discount}
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <h3 className="font-bold text-gray-800 mb-2">{item.name}</h3>
                      <p className="text-sm text-gray-500 mb-3">{item.description}</p>
                      <div className="flex items-center justify-center space-x-1 mb-4">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm text-gray-600 font-medium">{item.rating}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="text-left">
                          <div className="flex items-center space-x-2">
                            <span className="text-2xl font-bold text-orange-500">${item.price}</span>
                            <span className="text-sm text-gray-400 line-through">${item.originalPrice}</span>
                          </div>
                        </div>
                        <button className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-3 rounded-2xl hover:shadow-lg hover:scale-105 transition-all duration-300">
                          <Plus className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center space-x-6 w-full">
                    <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center text-3xl flex-shrink-0">
                      {item.image}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="font-bold text-gray-800 mb-1">{item.name}</h3>
                          <p className="text-sm text-gray-500">{item.description}</p>
                        </div>
                        <button
                          onClick={() => toggleFavorite(item.id)}
                          className="p-2 hover:bg-gray-100 rounded-xl transition-colors"
                        >
                          <Heart
                            className={`w-4 h-4 ${
                              favorites.has(item.id)
                                ? 'text-red-500 fill-current'
                                : 'text-gray-400'
                            }`}
                          />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-gray-600">{item.rating}</span>
                          </div>
                          <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium">
                            {item.discount}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <div className="flex items-center space-x-2">
                              <span className="text-xl font-bold text-orange-500">${item.price}</span>
                              <span className="text-sm text-gray-400 line-through">${item.originalPrice}</span>
                            </div>
                          </div>
                          <button className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-2 rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-300">
                            <Plus className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GroceryApp;