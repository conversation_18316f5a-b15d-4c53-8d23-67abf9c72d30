"use client"
import React, { useState } from 'react';
import { Edit, Trash2 } from 'lucide-react';

const TaxSettings = () => {
  const [taxes, setTaxes] = useState([
    {
      id: 1,
      name: 'قيمة مضافة',
      percent: 14
    }
  ]);

  const handleDelete = (id) => {
    setTaxes(taxes.filter(tax => tax.id !== id));
  };

  const handleUpdate = (id) => {
    // Handle update logic here
    console.log('Update tax with id:', id);
  };

  const handleAddTax = () => {
    // Handle add tax logic here
    console.log('Add new tax');
  };

  return (
    <div className=" bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Info Alert */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p className="text-blue-800 text-sm">
            All taxes will be applicable on creating order.
          </p>
        </div>

        {/* Add Tax Button */}
        <div className="mb-6">
          <button 
            onClick={handleAddTax}
            className="bg-[#FF6500] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#e55a00] transition-colors"
          >
            Add Tax
          </button>
        </div>

        {/* Tax Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="grid grid-cols-12 gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div className="col-span-5">
              <h3 className="text-sm font-medium text-gray-600 uppercase tracking-wider">
                TAX NAME
              </h3>
            </div>
            <div className="col-span-3">
              <h3 className="text-sm font-medium text-gray-600 uppercase tracking-wider">
                TAX PERCENT
              </h3>
            </div>
            <div className="col-span-4">
              <h3 className="text-sm font-medium text-gray-600 uppercase tracking-wider text-right">
                ACTION
              </h3>
            </div>
          </div>

          {/* Tax Rows */}
          <div className="divide-y divide-gray-200">
            {taxes.map((tax) => (
              <div key={tax.id} className="grid grid-cols-12 gap-4 px-6 py-4 hover:bg-gray-50">
                <div className="col-span-5">
                  <span className="text-gray-900 font-medium">{tax.name}</span>
                </div>
                <div className="col-span-3">
                  <span className="text-gray-900">{tax.percent}</span>
                </div>
                <div className="col-span-4 flex items-center justify-end gap-2">
                  <button
                    onClick={() => handleUpdate(tax.id)}
                    className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <Edit className="w-4 h-4 mr-1" />
                    Update
                  </button>
                  <button
                    onClick={() => handleDelete(tax.id)}
                    className="inline-flex items-center px-3 py-1 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Empty State */}
          {taxes.length === 0 && (
            <div className="px-6 py-8 text-center">
              <p className="text-gray-500">No taxes configured yet.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaxSettings;