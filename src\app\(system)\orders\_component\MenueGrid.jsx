"use client"
import React, { useState } from 'react';
import { Delete, Edit, Trash2, Plus } from 'lucide-react';

const MenuGrid = ({ data = [],category }) => {
  const [selectedItems, setSelectedItems] = useState(new Set());
  console.log(category)
 

  return (
    <div className="w-full max-w-7xl mx-auto p-6 bg-white rounded-2xl shadow-lg">
      {/* Header */}
      <div className="mb-6 flex items-center  justify-between px-4">
        <div>
        <h2 className="text-2xl font-bold text-gray-800">قائمة العناصر</h2>
        <p className="text-gray-500">إدارة عناصر القائمة</p>
        </div>
        
        {/* <div className='  ' >
          <div className=' flex items-center gap-2'>
          
          <button className="p-2 rounded-lg bg-red-50 hover:bg-red-100 text-red-600 transition-all duration-200" data-tooltip-id="delete-all-tip">
            <Trash2 />
          </button>
         
         
          <button className="p-2 rounded-lg bg-blue-50 hover:bg-blue-100 text-blue-600 transition-all duration-200" data-tooltip-id="update-tip">
          <Edit  />         
           </button>
          <p className="mx-2 font-semibold text-[#FF6500] uppercase">{category}</p>
         
        </div>
        <button
          className="flex cursor-pointer mt-3 items-center gap-2 px-4 py-2 rounded-lg bg-primary text-white font-semibold hover:bg-[#ff7f32] focus:ring-2 focus:ring-[#FF6500] transition-all duration-200"
        >
          <Plus className="w-4 h-4" />
          إضافة عنصر جديد لهذه الفئة
        </button>

        </div>
         */}
       
        
      </div>

      {/* Table Container */}
      <div className="overflow-x-auto rounded-xl border border-gray-200">
        <table className="w-full min-w-full">
          {/* Table Header */}
          <thead className="bg-gradient-to-r from-[#FF6500] to-[#FCB190]">
            <tr>
              {/* <th className="px-4 py-4 ">
                <input
                  type="checkbox"
                  checked={selectedItems.size === data.length && data.length > 0}
                  onChange={handleSelectAll}
                  className="w-4 h-4 text-[#FF6500] bg-white border-white rounded focus:ring-[#FF6500] focus:ring-2"
                />
              </th> */}
              <th className="px-4 py-4  text-white font-semibold">الصورة</th>
              <th className="px-4 py-4  text-white font-semibold">اسم العنصر</th>
              <th className="px-4 py-4  text-white font-semibold">الفئة</th>
              <th className="px-4 py-4  text-white font-semibold">السعر</th>
              <th className="px-4 py-4 text-center text-white font-semibold">متاح</th>
              <th className="px-4 py-4 text-center text-white font-semibold whitespace-nowrap ">متاح للعميل ؟</th>
              <th className="px-4 py-4 text-center text-white font-semibold">إجراءات</th>
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-gray-100">
            {data.length === 0 ? (
              <tr>
                <td colSpan="7" className="px-4 py-12 text-center text-gray-500">
                  <p className="text-lg font-medium">لا توجد عناصر</p>
                </td>
              </tr>
            ) : (
              data.map((item) => (
                <tr 
                  key={item.id} 
                  className={`hover:bg-gray-50 transition-colors duration-200 ${
                    selectedItems.has(item.id) ? 'bg-[#FF6500]/5 border-r-4 border-[#FF6500]' : ''
                  }`}
                >
                

                  {/* Image */}
                  <td className="px-4 py-4">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-12 h-12 object-cover rounded-lg shadow-md"
                      onError={(e) => {
                        e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMS4zMzMzIDQyLjY2NjdMMzIgMzJMMjEuMzMzMyAyMS4zMzMzTDE4LjY2NjcgMjRMMjYuNjY2NyAzMkwxOC42NjY3IDQwTDIxLjMzMzMgNDIuNjY2N1oiIGZpbGw9IiM5Q0E0QUYiLz4KPC9zdmc+';
                      }}
                    />
                  </td>

                  {/* Title */}
                  <td className="px-4 py-4">
                    <div className="font-semibold text-gray-800 ">
                      {item.title.substring(0, 10)}
                    </div>
                  </td>

                  {/* Category */}
                  <td className="px-4 py-4 ">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-[#FF6500]/10 to-[#FCB190]/10 text-[#FF6500] border border-[#FF6500]/20">
                      {item.category}
                    </span>
                  </td>

                  {/* Price */}
                  <td className="px-4 py-4 ">
                    <div className="font-bold text-[#FF6500] text-lg">
                      ${item.price?.toFixed(2)}
                    </div>
                  </td>

                  {/* Available */}
                  <td className="px-4 py-4 text-center">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      item.rating?.count > 0 
                        ? 'bg-green-100 text-green-800 border border-green-200' 
                        : 'bg-red-100 text-red-800 border border-red-200'
                    }`}>
                      {item.rating?.count > 0 ? 'متاح' : 'غير متاح'}
                    </span>
                  </td>
                  <td className="px-4 py-4 text-center">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      item.rating?.count < 0 
                        ? 'bg-green-100 text-green-800 border border-green-200' 
                        : 'bg-red-100 text-red-800 border border-red-200'
                    }`}>
                      {item.rating?.count < 0 ? 'متاح' : 'غير متاح'}
                    </span>
                  </td>

                  {/* Actions */}
                  <td className="px-4 py-4">
                    <div className="flex items-center justify-center gap-2">
                      <button className="p-2 text-gray-400 hover:text-[#FF6500] hover:bg-[#FF6500]/10 rounded-lg transition-all duration-200">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-all duration-200">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

     
    </div>
  );
};

export default MenuGrid;