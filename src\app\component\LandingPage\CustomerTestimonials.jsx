import React from 'react';
import { Star, Quote, Users, Heart } from 'lucide-react';
import ReusabelSubbortBtn from '../ReusabelComponent/ReusabelSubbortBtn';

const CustomerTestimonials = () => {
  const testimonials = [
    {
      name: 'كريم عبد الله',
      position: 'مالك مطعم مشويات في المعادي',
      testimonial: 'السيستم أفادني في رمضان! كنت بتلخبط في الطلبات والمحاسبة، لكن مع Episys، بقيت أراجع كل حاجة على التابلت، وأطلع تقارير المبيعات في لحظة',
      rating: 4
    },
    {
      name: 'رانيا حلمي',
      position: 'كافيه في الشيخ زايد',
      testimonial: 'أسهل برنامج جربته! اشتغلت على أنظمة كتير قبل كده، بس أول مرة أحس إن كل حاجة بسيطة وواضحة كده. حتى الطاقم فهم القوائم في يوم واحد',
      rating: 5
    },
    {
      name: 'محمد فوزي',
      position: 'مطعم بيتزا في التجمع الخامس',
      testimonial: 'QR العملاء مبسوطين أكتر! بقيت أدي لكل ترابيزة، والناس تطلب لوحدها. قل الزحف والتوتر، وزاد معدل الطلبات',
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen my-8 bg-gradient-to-br from-white via-gray-50 to-orange-50 py-20 px-6" dir="rtl">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-4 mb-8">
            <Heart className="w-10 h-10 text-[#FF6500]" />
            <h1 className="text-5xl md:text-6xl font-black bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent">
              آراء عملائنا
            </h1>
          </div>
          
          <div className="max-w-3xl mx-auto">
            <p className="text-xl md:text-2xl text-gray-600 leading-relaxed font-light">
              اكتشف كيف غيّر{' '}
              <span className="text-[#FF6500] font-semibold">episys</span> حياة أصحاب المطاعم والكافيهات
            </p>
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#FFD5C7]/20 to-transparent blur-3xl"></div>
          
          <div className="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.name}
                className="group relative"
                style={{
                  animationDelay: `${index * 0.2}s`
                }}
              >
                <div className="relative h-full p-6 rounded-2xl bg-white/90 backdrop-blur-sm border border-gray-200/50 hover:border-[#FCB190]/50 shadow-lg hover:shadow-xl hover:shadow-[#FF6500]/10 transition-all duration-300 hover:scale-102">
                  
                  {/* Stars Rating */}
                  <div className="flex justify-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star 
                        key={i} 
                        className="w-4 h-4 fill-[#FF6500] text-[#FF6500]" 
                      />
                    ))}
                  </div>
                  
                  {/* Testimonial Text */}
                  <div className="relative z-10 mb-5">
                    <p className="text-gray-700 leading-relaxed text-sm font-medium">
                      "{testimonial.testimonial}"
                    </p>
                  </div>
                  
                  {/* Customer Info */}
                  <div className="relative z-10 text-center border-t border-gray-200/50 pt-4">
                    <h3 className="text-lg font-bold text-gray-800 mb-1 group-hover:text-[#FF6500] transition-colors duration-300">
                      {testimonial.name}
                    </h3>
                    <p className="text-gray-600 text-xs">
                      {testimonial.position}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Stats Section */}
      

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <div className="max-w-2xl mx-auto mb-8">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              انضم لعائلة العملاء السعداء
            </h2>
            <p className="text-gray-600 text-lg">
              ابدأ رحلتك مع episys واكتشف الفرق بنفسك
            </p>
          </div>
          
          <div className="inline-flex flex-col sm:flex-row gap-4">
            <button className="group relative px-10 py-4 bg-[#FF6500] rounded-full font-bold text-lg text-white hover:shadow-2xl hover:shadow-[#FF6500]/30 transition-all duration-300 hover:scale-105">
              <span className="relative z-10 flex items-center gap-2">
                <Heart className="w-5 h-5" />
                ابدأ تجربتك المجانية
              </span>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#FCB190] to-[#FFD5C7] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
            
            {/* <button className="group relative px-10 py-4 bg-white border-2 border-[#FF6500] rounded-full font-bold text-lg text-[#FF6500] hover:bg-[#FF6500] hover:text-white transition-all duration-300 hover:scale-105">
              <span className="relative z-10 flex items-center gap-2">
                <Users className="w-5 h-5" />
                تحدث مع فريق المبيعات
              </span>
            </button> */}
            <ReusabelSubbortBtn text='تحدث مع فريق المبيعات' phone={"01503135009"}/>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerTestimonials;