import { Check, Home, ArrowLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const BrandingRegister = () => {
  return (
    <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-[#FF6500] to-[#FF8A3D] p-12 flex-col justify-center items-center text-white relative overflow-hidden">
      <div className="absolute inset-0 bg-black/10"></div>
      
      {/* Beautiful Home Link */}
      <Link 
        href="/" 
        className="absolute top-8 left-8 group flex items-center space-x-2 bg-white/10 backdrop-blur-md hover:bg-white/20 transition-all duration-300 px-4 py-2 rounded-full border border-white/20 hover:border-white/40 hover:scale-105 transform z-20"
      >
        <Home size={18} className="group-hover:scale-110 transition-transform duration-300" />
        <span className="text-sm font-medium opacity-90 group-hover:opacity-100 transition-opacity duration-300">
          الرئيسية
        </span>
                <ArrowLeft size={18} className="group-hover:-translate-x-1 transition-transform duration-300" />

      </Link>

      <div className="relative z-10 text-center">
        <div className="flex items-center justify-center mb-8">
          <h1 className="text-4xl font-bold ml-4">EPISYS</h1>
          <div className="bg-white/20 backdrop-blur-sm p-4 rounded-2xl">
            <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center">
              <Image
                src="/images/EPISYS.png"
                alt="EPISYS"
                width={100}
                height={90}
                className="text-center"
              />
            </div>
          </div>
        </div>

        <h2 className="text-3xl text-right font-bold mb-6">سيطر على مطعمك</h2>

        <div className="space-y-4 text-right">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <Check size={16} />
            </div>
            <span className="text-lg">تنسيق إدارة الطلبات</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <Check size={16} />
            </div>
            <span className="text-lg">تحسين تحفظات الجدول</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <Check size={16} />
            </div>
            <span className="text-lg">إدارة القائمة دون عناء</span>
          </div>
        </div>

        <div className="mt-12 p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
          <p className="text-sm opacity-90">
            "لقد غير EPISYS طريقة إدارة مطعمي بالكامل. الآن يمكنني التركيز على
            تقديم أفضل الأطباق بدلاً من القلق بشأن العمليات اليومية."
          </p>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-10 right-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
    </div>
  );
};

export default BrandingRegister;