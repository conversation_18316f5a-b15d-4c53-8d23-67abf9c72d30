"use client";
import React, { useState, useEffect } from "react";
import { Edit, Plus, Loader2, <PERSON>ert<PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import TableFilter from "./TableFilter";
import CreateTableModal from "./CreateTableModal";
import UpdateTableModal from "./UpdateTableModal";
import DeleteTableModal from "./DeleteTableModal";
import { fetchTables } from "../../../../../lib/api";

// Filter Component
// const TableFilter = ({ selectedArea, onAreaChange }) => {
//   const areas = ["All Areas", "INDOOR", "TERRACE"];

//   return (
//     <div className="mb-6">
//       <div className="flex gap-2 justify-between">
//         <div className="flex gap-2">
//         {areas.map((area) => (
//           <button
//             key={area}
//             onClick={() => onAreaChange(area)}
//             className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
//               selectedArea === area
//                 ? "text-white shadow-sm"
//                 : "text-gray-600 bg-white hover:bg-gray-50 border border-gray-200"
//             }`}
//             style={selectedArea === area ? { backgroundColor: "#FF6500" } : {}}
//           >
//             {area}
//           </button>
//         ))}
//         </div>
//         <button
//           onClick={() => alert("Add new modifier")}
//           className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
//         >
//           <svg
//             className="w-5 h-5"
//             fill="none"
//             stroke="currentColor"
//             strokeWidth="2"
//             viewBox="0 0 24 24"
//           >
//             <path
//               strokeLinecap="round"
//               strokeLinejoin="round"
//               d="M12 4v16m8-8H4"
//             />
//           </svg>
//           إضافة طاوله جديده
//         </button>
//       </div>

//       {/* Area Info */}
//       <div className="mt-4 flex items-center gap-4 text-sm text-gray-600">
//         <span className="font-semibold text-gray-800">
//           {selectedArea === "All Areas" ? "ALL AREAS" : selectedArea}
//         </span>
//         <span>
//           {selectedArea === "All Areas"
//             ? `${tables.length} Table${tables.length !== 1 ? "s" : ""}`
//             : `${tables.filter((t) => t.area === selectedArea).length} Table${
//                 tables.filter((t) => t.area === selectedArea).length !== 1
//                   ? "s"
//                   : ""
//               }`}
//         </span>
//       </div>
//     </div>
//   );
// };

function TableGrid() {
  const [tables, setTables] = useState([]);
  const [areas, setAreas] = useState([]);
  const [selectedArea, setSelectedArea] = useState("All Areas");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTable, setSelectedTable] = useState(null);

  // Fetch tables data
  useEffect(() => {
    fetchTablesData();
  }, []);

  const fetchTablesData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetchTables(1);
      setTables(response.tables);

      // Extract unique areas from tables
      const uniqueAreas = [...new Set(response.tables.map(table => table.area?.name).filter(Boolean))];
      setAreas(uniqueAreas);
    } catch (err) {
      console.error('Error fetching tables:', err);
      setError('Failed to load tables. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredTables =
    selectedArea === "All Areas"
      ? tables
      : tables.filter((t) => t.area?.name === selectedArea);

  // Modal handlers
  const handleTableCreated = (newTable) => {
    setTables(prev => [...prev, newTable]);
    // Update areas if new area is added
    if (newTable.area?.name && !areas.includes(newTable.area.name)) {
      setAreas(prev => [...prev, newTable.area.name]);
    }
  };

  const handleTableUpdated = (updatedTable) => {
    setTables(prev => prev.map(table =>
      table.id === updatedTable.id ? updatedTable : table
    ));
    // Update areas if area changed
    if (updatedTable.area?.name && !areas.includes(updatedTable.area.name)) {
      setAreas(prev => [...prev, updatedTable.area.name]);
    }
  };

  const handleTableDeleted = (deletedTableId) => {
    setTables(prev => prev.filter(table => table.id !== deletedTableId));
  };

  const openUpdateModal = (table) => {
    setSelectedTable(table);
    setShowUpdateModal(true);
  };

  const openDeleteModal = (table) => {
    setSelectedTable(table);
    setShowDeleteModal(true);
  };

  const getTableBgColor = (status) => {
    switch (status) {
      case "occupied":
        return "bg-blue-100 border-blue-300";
      case "reserved":
        return "bg-green-100 border-green-300";
      case "cleaning":
        return "bg-yellow-100 border-yellow-300";
      case "out_of_order":
        return "bg-red-100 border-red-300";
      case "available":
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  const getTableCodeBg = (status) => {
    switch (status) {
      case "occupied":
        return "bg-blue-200 text-blue-800";
      case "reserved":
        return "bg-green-200 text-green-800";
      case "cleaning":
        return "bg-yellow-200 text-yellow-800";
      case "out_of_order":
        return "bg-red-200 text-red-800";
      case "available":
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  if (loading) {
    return (
      <div className="p-6 bg-gray-100 min-h-screen">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-[#FF6500]" />
              <p className="text-gray-600">جاري تحميل الطاولات...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-gray-100 min-h-screen">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <AlertCircle className="w-8 h-8 mx-auto mb-4 text-red-500" />
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={fetchTablesData}
                className="px-4 py-2 text-white rounded-md hover:opacity-90 transition-opacity"
                style={{ backgroundColor: "#FF6500" }}
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="max-w-6xl mx-auto">
        {/* Filter Component */}
        <TableFilter
          tables={tables}
          areas={areas}
          selectedArea={selectedArea}
          onAreaChange={setSelectedArea}
          onAddTable={() => setShowCreateModal(true)}
        />

        {/* Table Grid */}
        {filteredTables.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد طاولات</h3>
            <p className="text-gray-500 mb-4">
              {selectedArea === "All Areas" ? 'ابدأ بإضافة طاولة جديدة.' : `لا توجد طاولات في ${selectedArea}.`}
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="px-4 py-2 text-white rounded-md hover:opacity-90 transition-opacity"
              style={{ backgroundColor: "#FF6500" }}
            >
              إضافة طاولة جديدة
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-4 gap-4">
            {filteredTables.map((table) => (
              <div
                key={table.id}
                className={`${getTableBgColor(
                  table.status
                )} border-2 rounded-lg p-4 relative shadow-sm hover:shadow-md transition-shadow`}
              >
                {/* Table Code Header */}
                <div
                  className={`${getTableCodeBg(
                    table.status
                  )} rounded px-2 py-1 text-sm font-semibold mb-3 inline-block`}
                >
                  {table.table_number}
                </div>

                {/* Table Name */}
                {table.table_name && (
                  <div className="text-sm text-gray-700 mb-2 font-medium">
                    {table.table_name}
                  </div>
                )}

                {/* Area */}
                <div className="text-xs text-gray-500 mb-2">
                  {table.area?.name}
                </div>

                {/* Capacity */}
                <div className="text-right text-sm text-gray-600 mb-2">
                  {table.seating_capacity} مقعد
                </div>

                {/* Status Badge */}
                <div className="mb-3">
                  <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                    table.status === 'available' ? 'bg-green-100 text-green-800' :
                    table.status === 'occupied' ? 'bg-blue-100 text-blue-800' :
                    table.status === 'reserved' ? 'bg-yellow-100 text-yellow-800' :
                    table.status === 'cleaning' ? 'bg-orange-100 text-orange-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {table.status === 'available' ? 'متاحة' :
                     table.status === 'occupied' ? 'مشغولة' :
                     table.status === 'reserved' ? 'محجوزة' :
                     table.status === 'cleaning' ? 'تنظيف' :
                     'خارج الخدمة'}
                  </span>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between items-center mt-4">
                  {table.status === "occupied" ? (
                    <>
                      <button className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                        عرض الطلب
                      </button>
                      <button
                        className="px-3 py-1 text-xs text-white rounded hover:opacity-90 transition-opacity flex items-center gap-1"
                        style={{ backgroundColor: "#FF6500" }}
                      >
                        <Plus size={12} />
                        طلب جديد
                      </button>
                    </>
                  ) : (
                    <div className="flex gap-2 ml-auto">
                      <button
                        onClick={() => openUpdateModal(table)}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                        title="تعديل"
                      >
                        <Edit size={16} className="text-gray-600" />
                      </button>
                      <button
                        onClick={() => openDeleteModal(table)}
                        className="p-1 hover:bg-red-100 rounded transition-colors"
                        title="حذف"
                      >
                        <Trash2 size={16} className="text-red-600" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      <CreateTableModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onTableCreated={handleTableCreated}
      />

      <UpdateTableModal
        isOpen={showUpdateModal}
        onClose={() => setShowUpdateModal(false)}
        table={selectedTable}
        onTableUpdated={handleTableUpdated}
      />

      <DeleteTableModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        table={selectedTable}
        onTableDeleted={handleTableDeleted}
      />
    </div>
  );
}

export default TableGrid;
