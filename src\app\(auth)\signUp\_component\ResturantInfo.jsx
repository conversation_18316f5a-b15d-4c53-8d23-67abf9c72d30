"use client"
import React, { useState } from 'react'
import { User, AlertCircle } from 'lucide-react'
import BrandingRegister from './BrandingRegister'
import { useRouter } from 'next/navigation'

const PersonalRegister = ({ setStep }) => {
  const [formData, setFormData] = useState({
    branchName: '',
    country: '',
    branchAddress: ''
  })
   const router = useRouter()
  const [errors, setErrors] = useState({})
  const [showErrors, setShowErrors] = useState(false)
 

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.branchName.trim()) {
      newErrors.branchName = 'اسم الفرع مطلوب'
    }

    if (!formData.country.trim()) {
      newErrors.country = 'الدولة مطلوبة'
    }

    if (!formData.branchAddress.trim()) {
      newErrors.branchAddress = 'عنوان الفرع مطلوب'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    
    setFormData({
      ...formData,
      [name]: value
    })
    
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      })
    }
  }

  const handleStep = () => {
    setShowErrors(true)
    if (validateForm()) {
      console.log("Form Data:", formData)
      router.push("/")
    }
  }

  const getInputBorderClass = (fieldName) => {
    if (showErrors && errors[fieldName]) {
      return 'border-red-300 focus:ring-red-500 focus:border-red-500'
    }
    return 'border-gray-200 focus:ring-[#FF6500] focus:border-transparent'
  }

  return (
    <div className="min-h-screen  flex items-center justify-center p-4">
      <div className="w-full max-w-6xl flex bg-white rounded-3xl shadow-2xl overflow-hidden">
        
        {/* Right Side - Form */}
        <div className="w-full lg:w-1/2 p-8 lg:p-12">
          <div className="max-w-md mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-800 mb-2">إنشاء حساب جديد</h2>
              <p className="text-gray-600">الخاص بك EPISYS انضم إلى عائلة</p>
            </div>

            <div className="space-y-6">
              {/* Branch Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center text-gray-700 gap-1">
                  اسم الفرع
                  <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    name="branchName"
                    value={formData.branchName}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 pr-12 bg-gray-50 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 text-right ${getInputBorderClass('branchName')}`}
                    placeholder="الفرع الرئيسي"
                  />
                  <User className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>
                {showErrors && errors.branchName && (
                  <div className="flex items-center gap-1 text-red-500 text-sm">
                    <AlertCircle size={16} />
                    <span>{errors.branchName}</span>
                  </div>
                )}
              </div>

              {/* Country */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center text-gray-700 gap-1">
                  الدولة
                  <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 pr-12 bg-gray-50 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 text-right ${getInputBorderClass('country')}`}
                    placeholder="مصر"
                  />
                  <User className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>
                {showErrors && errors.country && (
                  <div className="flex items-center gap-1 text-red-500 text-sm">
                    <AlertCircle size={16} />
                    <span>{errors.country}</span>
                  </div>
                )}
              </div>

              {/* Branch Address */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center text-gray-700 gap-1">
                  عنوان الفرع
                  <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    name="branchAddress"
                    value={formData.branchAddress}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 pr-12 bg-gray-50 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 text-right ${getInputBorderClass('branchAddress')}`}
                    placeholder="شارع النيل، القاهرة"
                  />
                  <User className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>
                {showErrors && errors.branchAddress && (
                  <div className="flex items-center gap-1 text-red-500 text-sm">
                    <AlertCircle size={16} />
                    <span>{errors.branchAddress}</span>
                  </div>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="button"
                onClick={handleStep}
                className="w-full bg-gradient-to-r from-[#FF6500] to-[#FF8A3D] text-white py-4 px-6 rounded-xl font-semibold text-lg hover:from-[#e55a00] hover:to-[#e67a2d] focus:outline-none focus:ring-4 focus:ring-[#FF6500]/20 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg"
              >
                اشتراك
              </button>
            </div>
          </div>
        </div>
        <BrandingRegister />
      </div>
    </div>
  )
}

export default PersonalRegister