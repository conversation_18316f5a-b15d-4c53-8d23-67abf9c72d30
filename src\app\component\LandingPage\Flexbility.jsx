import React from "react";
import FlexbilityImage from "../../../../public/images/store.png";
import ReusabelLink from "../ReusabelComponent/ReusabelLink";

const Flexbility = () => {
  return (
    <div className="relative rounded-4xl  max-w-7xl mx-auto  overflow-hidden">
      <div className="relative   p-8 ">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6 order-2 lg:order-1">
            <h1 className="font-bold text-2xl  leading-tight text-gray-900">
              تحكم بعمليات مطعمك على الدوام ومن أي مكان
            </h1>
            <p className="text-lg md:text-xl text-gray-700 leading-relaxed text-justify">
              امفيش حاجة هتضيع منك تانى تابع مخزونك لحظة بلحظة، وكل صنف في مكانه
              والكمية محسوبة بدقة. الطلبات بتتخصم أوتوماتيك من المخزون، والسيستم
              بيبلغك أول ما صنف يقرّب يخلص. واجهة سهلة، جرد منظم، وصلاحيات واضحة
              لكل موظف. ودّع نقص الخامات المفاجئ… وخلي كل حاجة تحت سيطرتك
            </p>
            <div className="pt-4">
              <ReusabelLink text={"اعرف المزيد"} href={"/"} background={true} />
            </div>
          </div>
          <div>
          <div className="flex justify-center items-center order-1 lg:order-2">
            <div className="relative w-full md:w-[450px] h-[250px] md:h-[350px]">
              <img
                src={FlexbilityImage.src}
                alt="Pricing"
                className="relative rounded-2xl w-full h-full"
              />
            </div>
          </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Flexbility;
