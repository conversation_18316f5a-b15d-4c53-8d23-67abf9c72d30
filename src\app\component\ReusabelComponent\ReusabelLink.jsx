import Link from 'next/link'
import React from 'react'

const ReusabelLink = ({href,text,background}) => {
  return (
    
              <Link
                href={href}
                className={`inline-block ${background ?" bg-[#FF6500]  text-white px-8 py-4 rounded-lg font-semibold text-lg hover:shadow-lg hover:-translate-y-1 transition-all duration-300 shadow-md":'inline-block text-[#FF6500] px-8 py-4  font-semibold text-lg '} `}
              >
                {text}
              </Link>
           
  )
}

export default ReusabelLink
