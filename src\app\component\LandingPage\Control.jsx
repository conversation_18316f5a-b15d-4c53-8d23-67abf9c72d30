import React from "react";
import pricingImage from "../../../../public/images/order-management.png";
import ReusabelLink from "../ReusabelComponent/ReusabelLink";

const Control = () => {
  return (
    <div className="relative  mx-4 my-4 rounded-4xl max-w-7xl md:mx-auto overflow-hidden">
      <div className="relative p-4 md:p-8">
        <div className="grid lg:grid-cols-2 gap-6 md:gap-12 items-center min-h-[300px] md:min-h-[400px]">
          <div className="flex justify-center items-center order-1 lg:order-2">
            <div className="relative w-full md:w-[450px] h-[250px] md:h-[350px]">
              <img
                src={pricingImage.src}
                alt="Pricing"
                className="relative rounded-2xl w-full h-full"
              />
            </div>
          </div>
          <div className="space-y-4 md:space-y-6 order-2 lg:order-1 flex flex-col justify-center">
            <h1 className="font-bold text-xl md:text-2xl leading-tight text-gray-900">
              تحكم بعمليات مطعمك على الدوام ومن أي مكان
            </h1>
            <p className="text-base md:text-lg lg:text-xl text-gray-700 leading-relaxed text-justify">
              أضف أو عدّل أو احذف العناصر من قائمتك بسهولة أثناء التنقل. قم
              بإبراز العروض الخاصة، وتحديث الأسعار، والحفاظ على تزامن كل شيء عبر
              جميع المنصات، حتى يرى موظفوك وزبائنك أحدث العروض دائمًا.
            </p>
            <div className="pt-2 md:pt-4">
              <ReusabelLink text={"اعرف المزيد"} href={"/"} background={true} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Control;
