"use client";
import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { fetchAreas } from "../../../../../lib/api";

const CreateTableModal = ({ isOpen, onClose, onTableCreated }) => {
  const [formData, setFormData] = useState({
    branch_id: 1,
    area_id: "",
    table_number: "",
    table_name: "",
    seating_capacity: "",
    section: "",
    status: "available",
    notes: "",
    is_active: true
  });
  const [areas, setAreas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingAreas, setLoadingAreas] = useState(false);
  const [error, setError] = useState("");

  // Fetch areas when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchAreasData();
    }
  }, [isOpen]);

  const fetchAreasData = async () => {
    setLoadingAreas(true);
    try {
      const areasData = await fetchAreas(1);
      setAreas(areasData);
    } catch (err) {
      console.error('Error fetching areas:', err);
      setError("Failed to load areas");
    } finally {
      setLoadingAreas(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!formData.table_number.trim()) {
      setError("Table number is required");
      return;
    }
    if (!formData.area_id) {
      setError("Area is required");
      return;
    }
    if (!formData.seating_capacity || formData.seating_capacity < 1) {
      setError("Valid seating capacity is required");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com'}/reservation/tables`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          ...formData,
          area_id: parseInt(formData.area_id),
          seating_capacity: parseInt(formData.seating_capacity)
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        onTableCreated(data.data);
        setFormData({
          branch_id: 1,
          area_id: "",
          table_number: "",
          table_name: "",
          seating_capacity: "",
          section: "",
          status: "available",
          notes: "",
          is_active: true
        });
        onClose();
      } else {
        setError(data.message || "Failed to create table");
      }
    } catch (err) {
      console.error('Error creating table:', err);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      branch_id: 1,
      area_id: "",
      table_number: "",
      table_name: "",
      seating_capacity: "",
      section: "",
      status: "available",
      notes: "",
      is_active: true
    });
    setError("");
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">إضافة طاولة جديدة</h2>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Area Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              المنطقة *
            </label>
            <select
              name="area_id"
              value={formData.area_id}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              required
              disabled={loadingAreas}
            >
              <option value="">اختر المنطقة</option>
              {areas.map((area) => (
                <option key={area.id} value={area.id}>
                  {area.name}
                </option>
              ))}
            </select>
          </div>

          {/* Table Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              رقم الطاولة *
            </label>
            <input
              type="text"
              name="table_number"
              value={formData.table_number}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              placeholder="مثال: T001"
              required
            />
          </div>

          {/* Table Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              اسم الطاولة
            </label>
            <input
              type="text"
              name="table_name"
              value={formData.table_name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              placeholder="مثال: طاولة النافذة 1"
            />
          </div>

          {/* Seating Capacity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              عدد المقاعد *
            </label>
            <input
              type="number"
              name="seating_capacity"
              value={formData.seating_capacity}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              placeholder="4"
              min="1"
              required
            />
          </div>

          {/* Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              القسم
            </label>
            <input
              type="text"
              name="section"
              value={formData.section}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              placeholder="مثال: قسم النوافذ"
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الحالة
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
            >
              <option value="available">متاحة</option>
              <option value="occupied">مشغولة</option>
              <option value="reserved">محجوزة</option>
              <option value="cleaning">تنظيف</option>
              <option value="out_of_order">خارج الخدمة</option>
            </select>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ملاحظات
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows="3"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
              placeholder="أي ملاحظات إضافية..."
            />
          </div>

          {/* Is Active */}
          <div className="flex items-center">
            <input
              type="checkbox"
              name="is_active"
              checked={formData.is_active}
              onChange={handleInputChange}
              className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500]"
            />
            <label className="ml-2 text-sm text-gray-700">
              نشط
            </label>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading || loadingAreas}
              className="px-4 py-2 text-white rounded-md hover:opacity-90 transition-opacity disabled:opacity-50"
              style={{ backgroundColor: "#FF6500" }}
            >
              {loading ? "جاري الحفظ..." : "حفظ"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateTableModal;
