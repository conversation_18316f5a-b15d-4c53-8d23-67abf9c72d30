// import { CreditCard, ShoppingCart, Users } from 'lucide-react'
// import React from 'react'

// const TopValues = () => {
//   return (
//     <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//         {/* Card 1 */}
//         <div className="bg-gradient-to-br from-white to-blue-50 border border-blue-100 rounded-2xl shadow-md p-8">
//           <div className="flex items-center mb-4">
//             <CreditCard className="h-7 w-7 text-blue-500 ml-3" />
//             <h3 className="text-lg font-bold text-gray-900">طريقة الدفع (اليوم)</h3>
//           </div>
//           <div className="">
//             <span className="text-gray-500 text-md font-medium">لم يتم العثور على دفعة</span>
//           </div>
//         </div>
//         {/* Card 2 */}
//         <div className="bg-gradient-to-br from-white to-green-50 border border-green-100 rounded-2xl shadow-md p-8">
//           <div className="flex items-center mb-4">
//             <ShoppingCart className="h-7 w-7 text-green-500 ml-3" />
//             <h3 className="text-lg font-bold text-gray-900">أعلى طبق بيع (اليوم)</h3>
//           </div>
//           <div className="">
//             <span className="text-gray-500 text-md font-medium">لم يتم العثور على دفعة</span>
//           </div>
//         </div>
//         {/* Card 3 */}
//         <div className="bg-gradient-to-br from-white to-purple-50 border border-purple-100 rounded-2xl shadow-md p-8">
//           <div className="flex items-center mb-4">
//             <Users className="h-7 w-7 text-purple-500 ml-3" />
//             <h3 className="text-lg font-bold text-gray-900">أعلى طاولات للبيع (اليوم)</h3>
//           </div>
//           <div className="">
//             <span className="text-gray-500 text-md font-medium">لم يتم العثور على دفعة</span>
//           </div>
//         </div>
//       </div>
//   )
// }

// export default TopValues
import { CreditCard, ShoppingCart, Users } from 'lucide-react'
import React from 'react'

const TopValues = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Card 1 */}
        <div className="bg-gradient-to-br from-white to-blue-50 border border-blue-100 rounded-2xl shadow-md p-8">
          <div className="flex items-center mb-4">
            <CreditCard className="h-7 w-7 text-blue-500 ml-3" />
            <h3 className="text-lg font-bold text-gray-900">Payment Method (Today)</h3>
          </div>
          <div className="">
            <span className="text-gray-500 text-md font-medium">No payment found</span>
          </div>
        </div>
        {/* Card 2 */}
        <div className="bg-gradient-to-br from-white to-green-50 border border-green-100 rounded-2xl shadow-md p-8">
          <div className="flex items-center mb-4">
            <ShoppingCart className="h-7 w-7 text-green-500 ml-3" />
            <h3 className="text-lg font-bold text-gray-900">Top Selling Dish (Today)</h3>
          </div>
          <div className="">
            <span className="text-gray-500 text-md font-medium">No payment found</span>
          </div>
        </div>
        {/* Card 3 */}
        <div className="bg-gradient-to-br from-white to-purple-50 border border-purple-100 rounded-2xl shadow-md p-8">
          <div className="flex items-center mb-4">
            <Users className="h-7 w-7 text-purple-500 ml-3" />
            <h3 className="text-lg font-bold text-gray-900">Top Tables for Sales (Today)</h3>
          </div>
          <div className="">
            <span className="text-gray-500 text-md font-medium">No payment found</span>
          </div>
        </div>
      </div>
  )
}

export default TopValues