"use client"
import React, { useState } from 'react';
import { QrCode, Settings } from 'lucide-react';

const PaymentGateways = () => {
  const [activeTab, setActiveTab] = useState('razorpay');
  const [enableRazorpay, setEnableRazorpay] = useState(false);

  const paymentMethods = [
    { id: 'razorpay', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🔱', disabled: false },
    { id: 'stripe', name: '<PERSON><PERSON>', icon: 'S', disabled: true },
    { id: 'flutterwave', name: 'Flutterwave', icon: '🦋', disabled: true },
    { id: 'paypal', name: 'Paypal', icon: 'P', disabled: true },
    { id: 'payfast', name: 'PayFast', icon: '≡', disabled: true },
    { id: 'paystack', name: 'Paystack', icon: '≡', disabled: true },
    { id: 'offline', name: 'Offline Payment', icon: '💳', disabled: false }
  ];

  const additionalTabs = [
    { id: 'qr-code', name: 'QR Code Payment', icon: QrCode, disabled: true },
    { id: 'general', name: 'General Settings', icon: Settings, disabled: false }
  ];

  const StatusDot = ({ disabled }) => (
    <div className={`w-2 h-2 rounded-full ml-2 ${disabled ? 'bg-red-500' : 'bg-green-500'}`} />
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">Payment Gateways</h1>
          <p className="text-gray-600">
            Enter payment gateway credentials to receive order payments.
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 mb-4">
            {paymentMethods.map((method) => (
              <button
                key={method.id}
                onClick={() => setActiveTab(method.id)}
                className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === method.id
                    ? 'bg-[#FF6500] text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                <span className="mr-2">{method.icon}</span>
                {method.name}
                <StatusDot disabled={method.disabled} />
              </button>
            ))}
          </div>
          
          <div className="flex gap-2">
            {additionalTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-[#FF6500] text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                <tab.icon className="w-4 h-4 mr-2" />
                {tab.name}
                <StatusDot disabled={tab.disabled} />
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {activeTab === 'razorpay' && (
            <div className="space-y-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enable-razorpay"
                  checked={enableRazorpay}
                  onChange={(e) => setEnableRazorpay(e.target.checked)}
                  className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500] focus:ring-2"
                />
                <label htmlFor="enable-razorpay" className="ml-2 text-sm font-medium text-gray-700">
                  Enable Razorpay
                </label>
              </div>
              
              {enableRazorpay && (
                <div className="space-y-4 pt-4 border-t border-gray-200">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Razorpay Key ID
                    </label>
                    <input
                      type="text"
                      placeholder="Enter Razorpay Key ID"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Razorpay Key Secret
                    </label>
                    <input
                      type="password"
                      placeholder="Enter Razorpay Key Secret"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'stripe' && (
            <div className="text-center py-8">
              <p className="text-gray-500">Stripe configuration coming soon...</p>
            </div>
          )}

          {activeTab === 'flutterwave' && (
            <div className="text-center py-8">
              <p className="text-gray-500">Flutterwave configuration coming soon...</p>
            </div>
          )}

          {activeTab === 'paypal' && (
            <div className="text-center py-8">
              <p className="text-gray-500">PayPal configuration coming soon...</p>
            </div>
          )}

          {activeTab === 'payfast' && (
            <div className="text-center py-8">
              <p className="text-gray-500">PayFast configuration coming soon...</p>
            </div>
          )}

          {activeTab === 'paystack' && (
            <div className="text-center py-8">
              <p className="text-gray-500">Paystack configuration coming soon...</p>
            </div>
          )}

          {activeTab === 'offline' && (
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enable-offline"
                  defaultChecked={true}
                  className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500] focus:ring-2"
                />
                <label htmlFor="enable-offline" className="ml-2 text-sm font-medium text-gray-700">
                  Enable Offline Payment
                </label>
              </div>
              <p className="text-sm text-gray-600">
                Allow customers to place orders without online payment. You can collect payment upon delivery or pickup.
              </p>
            </div>
          )}

          {activeTab === 'qr-code' && (
            <div className="text-center py-8">
              <p className="text-gray-500">QR Code Payment configuration coming soon...</p>
            </div>
          )}

          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Default Currency
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
                      <option>USD - US Dollar</option>
                      <option>EUR - Euro</option>
                      <option>GBP - British Pound</option>
                      <option>EGP - Egyptian Pound</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currency Symbol Position
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
                      <option>Before Amount ($100)</option>
                      <option>After Amount (100$)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Decimal Places
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent">
                      <option>0</option>
                      <option>1</option>
                      <option>2</option>
                      <option>3</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Minimum Order Amount
                    </label>
                    <input
                      type="number"
                      placeholder="0.00"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Method Settings</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="auto-capture"
                      defaultChecked={true}
                      className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500] focus:ring-2"
                    />
                    <label htmlFor="auto-capture" className="ml-2 text-sm font-medium text-gray-700">
                      Auto-capture payments
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="save-cards"
                      defaultChecked={false}
                      className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500] focus:ring-2"
                    />
                    <label htmlFor="save-cards" className="ml-2 text-sm font-medium text-gray-700">
                      Allow customers to save payment methods
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="payment-retry"
                      defaultChecked={true}
                      className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500] focus:ring-2"
                    />
                    <label htmlFor="payment-retry" className="ml-2 text-sm font-medium text-gray-700">
                      Enable payment retry on failure
                    </label>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Security Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Session Timeout (minutes)
                    </label>
                    <input
                      type="number"
                      defaultValue="15"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="ssl-verify"
                      defaultChecked={true}
                      className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500] focus:ring-2"
                    />
                    <label htmlFor="ssl-verify" className="ml-2 text-sm font-medium text-gray-700">
                      Verify SSL certificates
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="fraud-detection"
                      defaultChecked={false}
                      className="w-4 h-4 text-[#FF6500] border-gray-300 rounded focus:ring-[#FF6500] focus:ring-2"
                    />
                    <label htmlFor="fraud-detection" className="ml-2 text-sm font-medium text-gray-700">
                      Enable fraud detection
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Save Button */}
        <div className="mt-6">
          <button className="bg-[#FF6500] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#e55a00] transition-colors">
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentGateways;