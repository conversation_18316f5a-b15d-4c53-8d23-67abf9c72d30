import React, { Suspense } from 'react'
import MenueFiltetr from './_component/MenueFiltetr'
import MenueGridServer from './_component/MenueGridServer'
import { fetchCategories } from '@/component/sevices/serviceFeatching'
import { Loader2, Eye } from 'lucide-react'
import { redirect } from 'next/navigation'
import Link from 'next/link'

const page = async ({ searchParams: rawSearchParams }) => {
  const searchParams = await rawSearchParams
  const categories = await fetchCategories()

  if (!searchParams.category && categories && categories.length > 0) {
    redirect(`/orders?category=${categories[0]}`)
  }

  return (
    <div>
      {/* Header with link to view existing orders */}
      <div className="mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create New Order</h1>
            <p className="text-gray-600">Select items to create a new order</p>
          </div>
          <Link
            href="/OrdersT"
            className="bg-[#FF6500] hover:bg-[#E55A00] text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
          >
            <Eye className="w-4 h-4" />
            View All Orders
          </Link>
        </div>
      </div>

      <MenueFiltetr categories={categories} applayfilter={searchParams} />

       <Suspense
          key={JSON.stringify(searchParams.category)}
          fallback={
            <div className="flex items-center justify-center h-full mt-12">
              <Loader2
                size={70}
                className="text-center animate-spin text-primary"
              />
            </div>
          }
        >
          <MenueGridServer category={searchParams.category} />
        </Suspense>
    </div>
  )
}

export default page
