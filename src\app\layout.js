import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, Cairo } from "next/font/google";
import "./globals.css";
import { cookies, headers } from "next/headers";
import { I18nProvider } from "@/context/translate-api";
import { SidebarProvider, useSidebar } from "@/context/SidebarContext";

// import ClientLocaleSync from "@/context/ClientLocaleSync";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const cairo = Cairo({
  weight: '500',
  subsets: ['latin'],
});

export const metadata = {
  title: "EPISYS",
  description: "Generated by create next app",
};
import { AuthProvider } from '@/app/context/AuthContext'
export default async function RootLayout({ children }) {
  const cookieStore = await cookies();
  const langCookie = cookieStore.get("lang")?.value;

  // Get Accept-Language header from the request
  const headersStore = await headers();
  const acceptLanguage = headersStore.get("accept-language");
  const supportedLocales = ["en", "ar"];
  const defaultLocale = "ar";

  // Parse the preferred locale from Accept-Language
  let initialLocale = defaultLocale;
  if (!langCookie && acceptLanguage) {
    const preferredLocales = acceptLanguage
      .split(",")
      .map((lang) => lang.split(";")[0].trim().toLowerCase());
    initialLocale =
      preferredLocales.find((loc) =>
        supportedLocales.includes(loc.split("-")[0])
      ) || defaultLocale;
  } else if (langCookie) {
    initialLocale = supportedLocales.includes(langCookie)
      ? langCookie
      : defaultLocale;
  }
  console.log(initialLocale)

  return (
    <html 
      lang={initialLocale}
      dir={initialLocale === "ar" ? "rtl" : "ltr"}
      className={`${cairo.className}`}
    >
      <body className={`${geistSans.variable} ${geistMono.variable} ${initialLocale === "ar" ? "rtl" : "ltr"} antialiased`}>
        <AuthProvider>
          <I18nProvider initialLocale={initialLocale}>
            {/* <ClientLocaleSync /> */}
            {children}
          </I18nProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

// import "./globals.css";
// import { SidebarProvider } from "@/context/SidebarContext";
// // import Sidebar from "./_CommonComponent/Sidebar";
// // import Header from "./_CommonComponent/Header";
// import { useSidebar } from "@/context/SidebarContext";
// import Sidebar from "./(system)/_CommonComponent/Sidebar";
// import Header from "./(system)/_CommonComponent/Header";

// const Layout = ({ children }) => {
//   return (
//     <SidebarProvider>
//       <MainLayout>{children}</MainLayout>
//     </SidebarProvider>
//   );
// };

// const MainLayout = ({ children }) => {
//   const { isOpen } = useSidebar();

//   return (
//     <div className="flex h-screen overflow-hidden bg-gray-50">
//       <Sidebar />

//       <div
//         className={`flex-1 flex flex-col transition-all duration-300 ${
//           isOpen ? "ml-64" : "ml-0"
//         }`}
//       >
//         <Header />
//         <main className="overflow-y-auto p-3 flex-1">
//           {children}
//         </main>
//       </div>
//     </div>
//   );
// };

// export default Layout;
