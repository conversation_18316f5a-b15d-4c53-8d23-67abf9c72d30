"use client";
import React, { useState } from "react";
import {
  Eye,
  EyeOff,
  Mail,
  Lock,
  Home,
  ArrowLeft,
} from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useI18n } from "@/context/translate-api";
import { useAuth } from "@/app/context/AuthContext";
import Image from "next/image";

const LoginForm = () => {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const router = useRouter();
  const { t } = useI18n();
  const { login } = useAuth();

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
    // Clear message when user starts typing
    if (message) setMessage("");
  };

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Email validation
    if (!formData.email) {
      newErrors.email = "البريد الإلكتروني مطلوب";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "يرجى إدخال بريد إلكتروني صحيح";
    }
    
    // Password validation
    if (!formData.password) {
      newErrors.password = "كلمة المرور مطلوبة";
    } else if (formData.password.length < 6) {
      newErrors.password = "كلمة المرور يجب أن تكون على الأقل 6 أحرف";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    setMessage("");

    try {
      const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.epi-sys.com';
      
      const payload = {
        email: formData.email,
        password: formData.password
      };

      const response = await fetch(`${baseURL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      if (response.ok && data.success && data.data?.token) {
        // Store auth data in cookies via server action
        await fetch('/api/auth/set-cookies', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token: data.data.token,
            user: data.data.user
          })
        });

        // Update auth context
        login(data.data.user, data.data.token);
        
        setMessage(data.message || "تم تسجيل الدخول بنجاح");
        
        // Reset form
        setFormData({ email: "", password: "", rememberMe: false });
        
        // Redirect to home
        setTimeout(() => {
          router.push("/");
        }, 1000);
        
      } else {
        setMessage(data.message || "فشل تسجيل الدخول");
      }
    } catch (error) {
      console.error("Login error:", error);
      
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        setMessage("خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.");
      } else {
        setMessage("حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FF6500] via-[#FCB190] to-[#FFE5D9] relative flex items-center justify-center p-4">
      {/* Beautiful animated background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-32 left-16 w-96 h-96 bg-[#FCB190]/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/3 left-1/3 w-64 h-64 bg-white/5 rounded-full blur-2xl animate-pulse delay-500"></div>

        {/* Floating geometric shapes */}
        <div className="absolute top-10 left-10 w-8 h-8 bg-white/20 rounded-lg rotate-45 animate-bounce delay-300"></div>
        <div className="absolute bottom-20 right-32 w-6 h-6 bg-[#FCB190]/30 rounded-full animate-bounce delay-700"></div>
        <div className="absolute top-1/2 right-10 w-4 h-12 bg-white/15 rounded-full animate-pulse delay-1200"></div>
      </div>

      <div className="w-full max-w-md relative z-10">
        <div className="bg-white backdrop-blur-sm rounded-2xl shadow-2xl p-8 border border-white/20">
          {/* Enhanced Header with Beautiful Home Link */}
          <div className="text-right mb-6">
            <Link
              href="/"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-[#FF6500] to-[#FCB190] text-white px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 hover:from-[#e55a00] hover:to-[#f0a679] group"
            >
              <Home className="w-5 h-5 group-hover:rotate-[-10deg] transition-transform duration-300" />
              <span className="font-arabic text-sm">الصفحة الرئيسية</span>
              <p>{t?.Ai}</p>
              <ArrowLeft className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>

          <div className="text-center mb-8">
            <Link
              href={"/"}
              className="mx-auto w-20 h-20 mb-6 flex items-center"
            >
              <p className="text-3xl font-bold text-[#FF6500]">
                EPISYS
              </p>
              <Image
                src="/images/EPISYS.png"
                alt="EPISYS"
                width={100}
                height={90}
                className="text-center"
              />
            </Link>
            <h1 className="text-3xl font-bold text-gray-800 mb-2 font-arabic">
              أهلاً بعودتك
            </h1>
            <p className="text-gray-600">تسجيل الدخول إلى حسابك</p>
          </div>

          {/* Messages */}
          {message && (
            <div className={`mb-6 p-4 rounded-xl backdrop-blur-sm border text-sm ${
              message.includes("بنجاح") 
                ? "bg-green-50/80 border-green-200 text-green-700"
                : "bg-red-50/80 border-red-200 text-red-700"
            }`}>
              {message}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email */}
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                البريد الإلكتروني
              </label>
              <div className="relative">
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    handleInputChange("email", e.target.value)
                  }
                  className={`w-full pr-10 pl-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 ${
                    errors.email
                      ? "border-red-300 focus:ring-red-500"
                      : "border-gray-300 focus:ring-[#FF6500] focus:border-[#FF6500]"
                  }`}
                  placeholder="أدخل البريد الإلكتروني"
                  disabled={loading}
                  dir="ltr"
                />
              </div>
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
              )}
            </div>

            {/* Password */}
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                كلمة المرور
              </label>
              <div className="relative">
                <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange("password", e.target.value)
                  }
                  className={`w-full pr-10 pl-12 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 ${
                    errors.password
                      ? "border-red-300 focus:ring-red-500"
                      : "border-gray-300 focus:ring-[#FF6500] focus:border-[#FF6500]"
                  }`}
                  placeholder="أدخل كلمة المرور"
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 hover:text-[#FF6500] transition-colors"
                  disabled={loading}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password}</p>
              )}
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={(e) =>
                    handleInputChange("rememberMe", e.target.checked)
                  }
                  className="h-4 w-4 text-[#FF6500] focus:ring-[#FF6500] border-gray-300 rounded"
                />
                <label
                  htmlFor="remember-me"
                  className="mr-2 text-sm text-gray-700"
                >
                  تذكرني
                </label>
              </div>
              <a
                href="#"
                className="text-sm text-[#FF6500] hover:text-[#FCB190] transition-colors"
              >
                نسيت كلمة المرور؟
              </a>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-[#FF6500] to-[#FCB190] text-white py-3 px-4 rounded-xl font-medium hover:from-[#e55a00] hover:to-[#f0a679] focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] shadow-lg"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                    />
                  </svg>
                  جاري تسجيل الدخول...
                </div>
              ) : (
                "تسجيل الدخول"
              )}
            </button>

            {/* Sign Up Link */}
            <p className="text-center text-sm text-gray-600">
              ليس لديك حساب؟{" "}
              <Link
                href="/signUp"
                className="text-[#FF6500] hover:text-[#FCB190] font-medium transition-colors"
              >
                إنشاء حساب جديد
              </Link>
            </p>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;