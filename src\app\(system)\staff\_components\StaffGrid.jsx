"use client"
import React, { useState } from 'react';
import { Search, Download, Plus, Edit, Trash2 } from 'lucide-react';

const StaffGrid = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // Fake staff data
  const staffData = [
    {
      id: 1,
      memberName: 'MAHMOUD MOHAMED',
      emailAddress: '<EMAIL>',
      role: 'You can change new role'
    },
    {
      id: 2,
      memberName: 'Mahm<PERSON> farid',
      emailAddress: '<EMAIL>',
      role: 'Chef'
    },
    {
      id: 3,
      memberName: '<PERSON>',
      emailAddress: '<EMAIL>',
      role: 'Manager'
    },
    {
      id: 4,
      memberName: '<PERSON>',
      emailAddress: '<EMAIL>',
      role: 'Waiter'
    },
    {
      id: 5,
      memberName: '<PERSON>',
      emailAddress: '<EMAIL>',
      role: 'Kitchen Assistant'
    }
  ];

  const filteredStaff = staffData.filter(staff =>
    staff.memberName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    staff.emailAddress.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Staff</h1>
          <div className="flex items-center gap-3">
            <button className="flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Download size={16} />
              Export
            </button>
            <button 
              className="flex items-center gap-2 px-4 py-2 text-white rounded-lg transition-colors hover:opacity-90"
              style={{ backgroundColor: '#FF6500' }}
            >
              <Plus size={16} />
              Add Member
            </button>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="px-6 py-4">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Search by name or email"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent outline-none"
          />
        </div>
      </div>

      {/* Staff Table */}
      <div className="px-6">
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          {/* Table Header */}
          <div className="grid grid-cols-12 gap-4 px-6 py-3 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
            <div className="col-span-3">MEMBER NAME</div>
            <div className="col-span-4">EMAIL ADDRESS</div>
            <div className="col-span-3">ROLE</div>
            <div className="col-span-2"></div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-gray-200">
            {filteredStaff.map((staff) => (
              <div key={staff.id} className="grid grid-cols-12 gap-4 px-6 py-4 items-center hover:bg-gray-50 transition-colors">
                {/* Member Name */}
                <div className="col-span-3">
                  <span className="font-medium text-gray-900">{staff.memberName}</span>
                </div>

                {/* Email Address */}
                <div className="col-span-4">
                  <span className="text-gray-600">{staff.emailAddress}</span>
                </div>

                {/* Role */}
                <div className="col-span-3">
                  <span 
                    className={`text-sm ${
                      staff.role === 'You can change new role' 
                        ? 'text-orange-600' 
                        : 'text-gray-600'
                    }`}
                  >
                    {staff.role}
                  </span>
                </div>

                {/* Actions */}
                <div className="col-span-2 flex items-center justify-end gap-2">
                  <button 
                    className="p-2 text-white rounded hover:opacity-90 transition-colors"
                    style={{ backgroundColor: '#FF6500' }}
                    title="Update"
                  >
                    <Edit size={16} />
                  </button>
                  
                  <button 
                    className="p-2 text-red-600 hover:bg-red-50 rounded transition-colors"
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Empty State */}
        {filteredStaff.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg mb-2">No staff members found</div>
            <div className="text-gray-400 text-sm">Try adjusting your search terms</div>
          </div>
        )}
      </div>

      {/* Click outside to close dropdown */}
      {/* {showDropdown && (
        <div 
          className="fixed inset-0 z-5" 
          onClick={() => setShowDropdown(null)}
        ></div>
      )} */}
    </div>
  );
};

export default StaffGrid;