import React from "react";
import YourGistsImage from "../../../../public/images/manegeMeals.png";
import ReusabelLink from "../ReusabelComponent/ReusabelLink";

const YourGists = () => {
  return (
    <div className="relative rounded-4xl  max-w-7xl mx-auto  overflow-hidden">
      <div className="relative   p-8 ">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
        <div className="flex justify-center items-center order-1 lg:order-2">
            <div className="relative w-full md:w-[450px] h-[250px] md:h-[350px]">
              <img
                src={YourGistsImage.src}
                alt="Pricing"
                className="relative rounded-2xl w-full h-full "
              />
            </div>
          </div>
          <div className="space-y-6 order-2 lg:order-1">
            <h1 className="font-bold text-2xl  leading-tight text-gray-900">
              إدارة الوصفات والمكونات
            </h1>
            <p className="text-lg md:text-xl text-gray-700 leading-relaxed text-justify">
              كل حاجة محسوبة… من أول نقطة الزيت لحد آخر جرام قهوة! سجّل مكونات
              كل صنف أو مشروب بالتفصيل، وحدد الكميات بدقة. السيستم بيحسب
              الاستهلاك تلقائيًا مع كل طلب، ويخصم المكونات من المخزون أوتوماتيك.
              تابع تكاليفك، قلل الهدر، وخلي التشغيل ماشي بنظام ثابت. يناسب
              المطاعم، الكافيهات، والمطابخ المركزية.
            </p>
            <div className="pt-4">
              <ReusabelLink text={"اعرف المزيد"} href={"/"} background={true} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YourGists;
