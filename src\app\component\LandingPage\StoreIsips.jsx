import React from "react";
import StoreIsipsImage from "../../../../public/images/StoreIsips.jpeg";
import ReusabelLink from "../ReusabelComponent/ReusabelLink";

const StoreIsips = () => {
  return (
    <div className="relative rounded-4xl  max-w-7xl mx-auto  overflow-hidden">
      <div className="relative   p-8 ">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6 order-2 lg:order-1">
            <h1 className="font-bold text-2xl  leading-tight text-gray-900">
              تحكم بعمليات مطعمك على الدوام ومن أي مكان
            </h1>
            <p className="text-lg md:text-xl text-gray-700 leading-relaxed">
              عزّز قدرات مطعمك من خلال ربط نظام إيبيسيس مع مجموعة واسعة من
              التطبيقات العملية المتوفرة في متجر التطبيقات، مثل برامج المحاسبة
              والتسويق وخدمات التوصيل والطلب عبر الإنترنت{" "}
            </p>
            <div className="pt-4">
              <ReusabelLink
                text={"اعرف المزيد"}
                href={"/login"}
                background={true}
              />
            </div>
          </div>
          <div>
            <div className="flex justify-center order-1 lg:order-2">
              <div className="relative">
                <div className="relative  p-8 ">
                  <img
                    src={StoreIsipsImage.src}
                    alt="FlexbilityImage"
                    className="relative rounded-2xl w-full max-w-md h-auto"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreIsips;
