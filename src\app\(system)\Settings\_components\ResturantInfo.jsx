"use client"
import { Building, Mail, MapPin, Phone, Plus, Receipt } from 'lucide-react'
import React, { useState } from 'react'

const ResturantInfo = () => {

     const [formData, setFormData] = useState({
    name: 'DIP N ROLL',
    phone: '1272268023',
    countryCode: '+93',
    email: '<EMAIL>',
    address: 'DEGLA PALM COMPOUND MALL06',
    showTaxId: true,
    taxes: [
      { name: 'test tax', id: '234324' }
    ]
  });
     const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTaxChange = (index, field, value) => {
    const newTaxes = [...formData.taxes];
    newTaxes[index][field] = value;
    setFormData(prev => ({
      ...prev,
      taxes: newTaxes
    }));
  };

  const addTax = () => {
    setFormData(prev => ({
      ...prev,
      taxes: [...prev.taxes, { name: '', id: '' }]
    }));
  };
  return (
    <div className="space-y-8">
            <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-2xl p-8 border border-orange-200">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-[#FF6500] to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Building className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Restaurant Information</h2>
                  <p className="text-gray-600 mt-1">Configure your restaurant's basic details</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <Building className="w-4 h-4 text-[#FF6500]" />
                      Restaurant Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm"
                      placeholder="Enter restaurant name"
                    />
                  </div>

                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <Phone  className="w-4 h-4 text-[#FF6500]" />
                      Phone Number
                    </label>
                    <div className="flex gap-3">
                      <select
                        value={formData.countryCode}
                        onChange={(e) => handleInputChange('countryCode', e.target.value)}
                        className="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm"
                      >
                        <option value="+93">+93</option>
                        <option value="+1">+1</option>
                        <option value="+44">+44</option>
                        <option value="+20">+20</option>
                      </select>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="flex-1 px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm"
                        placeholder="Enter phone number"
                      />
                    </div>
                  </div>

                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <Mail className="w-4 h-4 text-[#FF6500]" />
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm"
                      placeholder="Enter email address"
                    />
                  </div>

                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-[#FF6500]" />
                      Restaurant Address
                    </label>
                    <textarea
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      rows="3"
                      className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:border-[#FF6500] focus:ring-4 focus:ring-orange-100 transition-all duration-200 text-gray-900 font-medium shadow-sm resize-none"
                      placeholder="Enter complete address"
                    />
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="bg-white rounded-2xl p-6 border-2 border-gray-100 shadow-sm">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-[#FF6500] to-orange-600 rounded-lg flex items-center justify-center">
                          <Receipt className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">Tax Configuration</h3>
                          <p className="text-sm text-gray-600">Manage tax settings for orders</p>
                        </div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.showTaxId}
                          onChange={(e) => handleInputChange('showTaxId', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-100 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#FF6500]"></div>
                        <span className="ml-3 text-sm font-medium text-gray-700">Show Tax ID on Orders</span>
                      </label>
                    </div>

                    <div className="space-y-4">
                      {formData.taxes.map((tax, index) => (
                        <div key={index} className="flex gap-4 p-4 bg-gray-50 rounded-xl">
                          <div className="flex-1">
                            <label className="block text-xs font-semibold text-gray-600 mb-2">Tax Name</label>
                            <input
                              type="text"
                              value={tax.name}
                              onChange={(e) => handleTaxChange(index, 'name', e.target.value)}
                              className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:border-[#FF6500] focus:ring-2 focus:ring-orange-100 transition-all duration-200 text-sm"
                              placeholder="Enter tax name"
                            />
                          </div>
                          <div className="flex-1">
                            <label className="block text-xs font-semibold text-gray-600 mb-2">Tax ID</label>
                            <input
                              type="text"
                              value={tax.id}
                              onChange={(e) => handleTaxChange(index, 'id', e.target.value)}
                              className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:border-[#FF6500] focus:ring-2 focus:ring-orange-100 transition-all duration-200 text-sm"
                              placeholder="Enter tax ID"
                            />
                          </div>
                        </div>
                      ))}
                      
                      <button
                        onClick={addTax}
                        className="w-full py-3 px-4 bg-gradient-to-r from-[#FF6500] to-orange-600 text-white rounded-xl font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105"
                      >
                        <Plus className="w-5 h-5" />
                        Add More Tax
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <button className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition-all duration-200">
                Cancel
              </button>
              <button className="px-8 py-3 bg-gradient-to-r from-[#FF6500] to-orange-600 text-white rounded-xl font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                Save Changes
              </button>
            </div>
          </div>
  )
}

export default ResturantInfo
