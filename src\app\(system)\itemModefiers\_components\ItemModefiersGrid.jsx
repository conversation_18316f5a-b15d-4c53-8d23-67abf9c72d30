// // "use client"
// // import React from 'react'
// // import { Edit, Plus, Trash2 } from 'lucide-react'

// // const ItemModifiersGrid = () => {
// //   const modifiers = [
// //     {
// //       id: 1,
// //       title: "Sauces",
// //       modifierName: "Ketchup",
// //       isRequired: false,
// //       allowMultipleSelection: false
// //     },
// //     {
// //       id: 2,
// //       title: "Sauces",
// //       modifierName: "Mayonnaise",
// //       isRequired: false,
// //       allowMultipleSelection: false
// //     },
// //     {
// //       id: 3,
// //       title: "Extras",
// //       modifierName: "Cheese",
// //       isRequired: true,
// //       allowMultipleSelection: true
// //     },
// //     {
// //       id: 4,
// //       title: "Extras",
// //       modifierName: "Bacon",
// //       isRequired: true,
// //       allowMultipleSelection: true
// //     }
// //   ]

// //   return (
// //     <div className="p-8 bg-gray-50 ">
// //       <div className="max-w-7xl mx-auto">
// //         <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
// //           Item Modifiers Management
// //         </h1>
// //          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
// //           <div className="flex justify-end p-4 pb-0">
// //             <button
// //               onClick={() => alert('Add new modifier')}
// //               className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
// //             >
// //               <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" /></svg>
// //               إضافة معدل جديد
// //             </button>
// //           </div>
// //           <div className="overflow-x-auto mt-2">
// //             <table className="w-full">
// //               <thead>
// //                 <tr className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
// //                   <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
// //                     ID
// //                   </th>
// //                   <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
// //                     Category
// //                   </th>
// //                   <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
// //                     Modifier Name
// //                   </th>
// //                   <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
// //                     Status
// //                   </th>
// //                   <th className="px-6 py-4  text-sm font-semibold uppercase tracking-wider">
// //                     Multiple Selection
// //                   </th>
// //                   <th className="px-6 py-4 text-center text-sm font-semibold uppercase tracking-wider">
// //                     Actions
// //                   </th>
// //                 </tr>
// //               </thead>
// //               <tbody className="divide-y divide-gray-200">
// //                 {modifiers.map((modifier, index) => (
// //                   <tr 
// //                     key={modifier.id}
// //                     className={`${
// //                       index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
// //                     } hover:bg-orange-50 transition-colors duration-200`}
// //                   >
// //                     <td className=" py-4 mx-auto whitespace-nowrap ">
// //                       <div className=" text-center w-6 h-6 flex items-center justify-center mx-auto px-2   bg-orange-100 rounded-full">
// //                         <span className="text-sm font-medium text-orange-600">
// //                           {modifier.id}
// //                         </span>
// //                       </div>
// //                     </td>
// //                     <td className=" py-4 whitespace-nowrap">
// //                       <div className="flex  text-center justify-center  items-center">
// //                         <div className={` rounded-full  mr-3 ${
// //                           modifier.title === 'Sauces' ? 'bg-blue-400' : 'bg-green-400'
// //                         }`}></div>
// //                         <span className="text-sm font-medium text-gray-900">
// //                           {modifier.title}
// //                         </span>
// //                       </div>
// //                     </td>
// //                     <td className="px-6 py-4 whitespace-nowrap text-center">
// //                       <span className="text-sm font-semibold text-gray-800">
// //                         {modifier.modifierName}
// //                       </span>
// //                     </td>
// //                     <td className="px-6 py-4 whitespace-nowrap  text-center">
// //                       <span className={`inline-flex items-center justify-center px-3 py-1 rounded-full text-xs font-medium ${
// //                         modifier.isRequired 
// //                           ? 'bg-red-100 text-red-800 border border-red-200' 
// //                           : 'bg-green-100 text-green-800 border border-green-200'
// //                       }`}>
// //                         {modifier.isRequired ? 'Required' : 'Optional'}
// //                       </span>
// //                     </td>
// //                     <td className="px-6 py-4 whitespace-nowrap  ">
// //                       <div className="flex items-center  justify-center text-center ">
                      
// //                         <span className={`text-sm font-medium ${
// //                           modifier.allowMultipleSelection ? 'text-orange-600' : 'text-gray-500'
// //                         }`}>
// //                           {modifier.allowMultipleSelection ? 'Yes' : 'No'}
// //                         </span>
// //                       </div>
// //                     </td>
// //                     <td className="px-6 py-4 whitespace-nowrap">
// //                       <div className="flex items-center justify-center space-x-2">
// //                         <button
// //                           onClick={() => console.log('Edit modifier', modifier.id)}
// //                           className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200"
// //                           title="Edit Modifier"
// //                         >
// //                           <Edit className="w-4 h-4" />
// //                         </button>
// //                         <button
// //                           onClick={() => console.log('Delete modifier', modifier.id)}
// //                           className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200"
// //                           title="Delete Modifier"
// //                         >
// //                           <Trash2 className="w-4 h-4" />
// //                         </button>
// //                       </div>
// //                     </td>
// //                   </tr>
// //                 ))}
// //               </tbody>
// //             </table>
// //           </div>
          
        
// //         </div>
// //       </div>
// //     </div>
// //   )
// // }

// // export default ItemModifiersGrid
// "use client"
// import React, { useState, useEffect } from 'react'
// import { Edit, Plus, Trash2, Loader2 } from 'lucide-react'

// const ItemModifiersGrid = ({ menuItemId }) => {
//   const [addons, setAddons] = useState([])
//   const [loading, setLoading] = useState(true)
//   const [error, setError] = useState(null)

//   // Mock function - replace with your actual API function
//   const fetchMenuItemAddons = async (menuItemId) => {
//     // This would be your actual API call
//     // return await apiCall(`/menu/items/${menuItemId}/addons`)
    
//     // Mock data for demonstration - remove this when integrating with real API
//     return new Promise((resolve) => {
//       setTimeout(() => {
//         resolve([
//           {
//             "id": 1,
//             "menu_item_id": menuItemId,
//             "addon_group_name": "Sauces",
//             "name": "Extra Cheese",
//             "code": "EXTRA_CHEESE",
//             "price": "1.50",
//             "cost": "0.50",
//             "is_required": false,
//             "max_quantity": 1,
//             "sort_order": 0,
//             "created_at": "2025-07-20T16:56:23.000000Z",
//             "updated_at": "2025-07-20T16:56:23.000000Z"
//           },
//           {
//             "id": 2,
//             "menu_item_id": menuItemId,
//             "addon_group_name": "Sauces",
//             "name": "Mayonnaise",
//             "code": "MAYO",
//             "price": "0.75",
//             "cost": "0.25",
//             "is_required": false,
//             "max_quantity": 1,
//             "sort_order": 1,
//             "created_at": "2025-07-20T16:56:23.000000Z",
//             "updated_at": "2025-07-20T16:56:23.000000Z"
//           },
//           {
//             "id": 3,
//             "menu_item_id": menuItemId,
//             "addon_group_name": "Extras",
//             "name": "Bacon",
//             "code": "BACON",
//             "price": "3.00",
//             "cost": "1.00",
//             "is_required": true,
//             "max_quantity": 2,
//             "sort_order": 2,
//             "created_at": "2025-07-20T16:56:23.000000Z",
//             "updated_at": "2025-07-20T16:56:23.000000Z"
//           }
//         ])
//       }, 1000)
//     })
//   }

//   useEffect(() => {
//     const loadAddons = async () => {
//       if (!menuItemId) return
      
//       try {
//         setLoading(true)
//         setError(null)
//         const data = await fetchMenuItemAddons(menuItemId)
//         setAddons(data)
//       } catch (err) {
//         setError('Failed to load modifiers')
//         console.error('Error loading addons:', err)
//       } finally {
//         setLoading(false)
//       }
//     }

//     loadAddons()
//   }, [menuItemId])

//   const groupAddonsByCategory = (addons) => {
//     const grouped = {}
//     addons.forEach(addon => {
//       const category = addon.addon_group_name || 'Other'
//       if (!grouped[category]) {
//         grouped[category] = []
//       }
//       grouped[category].push(addon)
//     })
//     return grouped
//   }

//   const getCategoryColor = (category) => {
//     const colors = {
//       'Sauces': 'bg-blue-400',
//       'Extras': 'bg-green-400',
//       'Toppings': 'bg-purple-400',
//       'Sides': 'bg-yellow-400',
//       'Other': 'bg-gray-400'
//     }
//     return colors[category] || 'bg-gray-400'
//   }

//   if (!menuItemId) {
//     return (
//       <div className="p-8 bg-gray-50">
//         <div className="max-w-7xl mx-auto">
//           <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
//             <h2 className="text-2xl font-semibold text-gray-600 mb-4">
//               No Menu Item Selected
//             </h2>
//             <p className="text-gray-500">
//               Please select a menu item to view its modifiers
//             </p>
//           </div>
//         </div>
//       </div>
//     )
//   }

//   if (loading) {
//     return (
//       <div className="p-8 bg-gray-50">
//         <div className="max-w-7xl mx-auto">
//           <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
//             <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
//             <h2 className="text-2xl font-semibold text-gray-600 mb-4">
//               Loading Modifiers...
//             </h2>
//           </div>
//         </div>
//       </div>
//     )
//   }

//   if (error) {
//     return (
//       <div className="p-8 bg-gray-50">
//         <div className="max-w-7xl mx-auto">
//           <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
//             <div className="text-red-500 mb-4">
//               <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
//               </svg>
//             </div>
//             <h2 className="text-2xl font-semibold text-gray-600 mb-4">
//               {error}
//             </h2>
//             <button 
//               onClick={() => window.location.reload()}
//               className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
//             >
//               Try Again
//             </button>
//           </div>
//         </div>
//       </div>
//     )
//   }

//   return (
//     <div className="p-8 bg-gray-50">
//       <div className="max-w-7xl mx-auto">
//         <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
//           Item Modifiers Management
//         </h1>
//         <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
//           <div className="flex justify-between items-center p-4 pb-0">
//             <div className="text-sm text-gray-600">
//               Menu Item ID: <span className="font-semibold">{menuItemId}</span>
//             </div>
//             <button
//               onClick={() => alert('Add new modifier')}
//               className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
//             >
//               <Plus className="w-5 h-5" />
//               إضافة معدل جديد
//             </button>
//           </div>
          
//           {addons.length === 0 ? (
//             <div className="p-8 text-center">
//               <div className="text-gray-400 mb-4">
//                 <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8v2a1 1 0 01-1 1H7a1 1 0 01-1-1V5a1 1 0 011-1h9a1 1 0 011 1z" />
//                 </svg>
//               </div>
//               <h3 className="text-xl font-semibold text-gray-600 mb-2">No Modifiers Found</h3>
//               <p className="text-gray-500 mb-4">This menu item doesn't have any modifiers yet.</p>
//               <button
//                 onClick={() => alert('Add first modifier')}
//                 className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors"
//               >
//                 Add First Modifier
//               </button>
//             </div>
//           ) : (
//             <div className="overflow-x-auto mt-2">
//               <table className="w-full">
//                 <thead>
//                   <tr className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
//                     <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
//                       ID
//                     </th>
//                     <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
//                       Category
//                     </th>
//                     <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
//                       Name
//                     </th>
//                     <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
//                       Code
//                     </th>
//                     <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
//                       Price
//                     </th>
//                     <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
//                       Status
//                     </th>
//                     <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
//                       Max Quantity
//                     </th>
//                     <th className="px-6 py-4 text-center text-sm font-semibold uppercase tracking-wider">
//                       Actions
//                     </th>
//                   </tr>
//                 </thead>
//                 <tbody className="divide-y divide-gray-200">
//                   {addons.map((addon, index) => (
//                     <tr 
//                       key={addon.id}
//                       className={`${
//                         index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
//                       } hover:bg-orange-50 transition-colors duration-200`}
//                     >
//                       <td className="py-4 mx-auto whitespace-nowrap">
//                         <div className="text-center w-6 h-6 flex items-center justify-center mx-auto px-2 bg-orange-100 rounded-full">
//                           <span className="text-sm font-medium text-orange-600">
//                             {addon.id}
//                           </span>
//                         </div>
//                       </td>
//                       <td className="py-4 whitespace-nowrap">
//                         <div className="flex text-center justify-center items-center">
//                           <div className={`w-3 h-3 rounded-full mr-3 ${getCategoryColor(addon.addon_group_name || 'Other')}`}></div>
//                           <span className="text-sm font-medium text-gray-900">
//                             {addon.addon_group_name || 'Other'}
//                           </span>
//                         </div>
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap text-center">
//                         <span className="text-sm font-semibold text-gray-800">
//                           {addon.name}
//                         </span>
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap text-center">
//                         <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
//                           {addon.code}
//                         </span>
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap text-center">
//                         <span className="text-sm font-semibold text-green-600">
//                           ${parseFloat(addon.price).toFixed(2)}
//                         </span>
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap text-center">
//                         <span className={`inline-flex items-center justify-center px-3 py-1 rounded-full text-xs font-medium ${
//                           addon.is_required 
//                             ? 'bg-red-100 text-red-800 border border-red-200' 
//                             : 'bg-green-100 text-green-800 border border-green-200'
//                         }`}>
//                           {addon.is_required ? 'Required' : 'Optional'}
//                         </span>
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap text-center">
//                         <span className="text-sm font-medium text-gray-600 bg-blue-100 px-2 py-1 rounded">
//                           {addon.max_quantity}
//                         </span>
//                       </td>
//                       <td className="px-6 py-4 whitespace-nowrap">
//                         <div className="flex items-center justify-center space-x-2">
//                           <button
//                             onClick={() => console.log('Edit addon', addon.id)}
//                             className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200"
//                             title="Edit Modifier"
//                           >
//                             <Edit className="w-4 h-4" />
//                           </button>
//                           <button
//                             onClick={() => console.log('Delete addon', addon.id)}
//                             className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200"
//                             title="Delete Modifier"
//                           >
//                             <Trash2 className="w-4 h-4" />
//                           </button>
//                         </div>
//                       </td>
//                     </tr>
//                   ))}
//                 </tbody>
//               </table>
//             </div>
//           )}
//         </div>
//       </div>
//     </div>
//   )
// }

// export default ItemModifiersGrid
"use client"
import React, { useState, useEffect } from 'react'
import { Edit, Plus, Trash2, Loader2 } from 'lucide-react'
import { fetchMenuItems, fetchMenuItemAddons } from '../../../../../lib/api'

const ItemModifiersGrid = ({ menuItemId }) => {
  const [addons, setAddons] = useState([])
  const [menuItems, setMenuItems] = useState([])
  const [selectedMenuItemId, setSelectedMenuItemId] = useState(menuItemId || null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)



  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Load menu items first
        const menuItemsData = await fetchMenuItems()
        
        // Filter menu items that have addons
        const menuItemsWithAddons = menuItemsData.filter(item => 
          item.addons && item.addons.length > 0
        )
        
        setMenuItems(menuItemsWithAddons)
        
        // If no menuItemId is provided, use the first menu item that has addons
        const itemId = selectedMenuItemId || (menuItemsWithAddons.length > 0 ? menuItemsWithAddons[0].id : null)
        
        if (itemId) {
          setSelectedMenuItemId(itemId)
          
          // Get the selected menu item and use its addons
          const selectedMenuItem = menuItemsWithAddons.find(item => item.id === itemId)
          if (selectedMenuItem && selectedMenuItem.addons) {
            setAddons(selectedMenuItem.addons)
          }
        }
      } catch (err) {
        setError('Failed to load data')
        console.error('Error loading data:', err)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const groupAddonsByCategory = (addons) => {
    const grouped = {}
    addons.forEach(addon => {
      const category = addon.addon_group_name || 'Other'
      if (!grouped[category]) {
        grouped[category] = []
      }
      grouped[category].push(addon)
    })
    return grouped
  }

  const getCategoryColor = (category) => {
    const colors = {
      'Sauces': 'bg-blue-400',
      'Extras': 'bg-green-400',
      'Toppings': 'bg-purple-400',
      'Sides': 'bg-yellow-400',
      'Other': 'bg-gray-400'
    }
    return colors[category] || 'bg-gray-400'
  }

  if (!selectedMenuItemId && (!menuItems || menuItems.length === 0)) {
    return (
      <div className="p-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            <h2 className="text-2xl font-semibold text-gray-600 mb-4">
              No Menu Item Selected
            </h2>
            <p className="text-gray-500">
              Please select a menu item to view its modifiers
            </p>
          </div>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-orange-500" />
            <h2 className="text-2xl font-semibold text-gray-600 mb-4">
              Loading Modifiers...
            </h2>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            <div className="text-red-500 mb-4">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-2xl font-semibold text-gray-600 mb-4">
              {error}
            </h2>
            <button 
              onClick={() => window.location.reload()}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
          Item Modifiers Management
        </h1>
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="flex justify-between items-center p-4 pb-0">
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                Menu Item:
              </div>
              <select 
                value={selectedMenuItemId || ''} 
                onChange={(e) => handleMenuItemChange(Number(e.target.value))}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                {menuItems.map(item => (
                  <option key={item.id} value={item.id}>
                    {item.name} (ID: {item.id})
                  </option>
                ))}
              </select>
            </div>
            <button
              onClick={() => alert('Add new modifier')}
              className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-lg duration-200 flex items-center gap-2"
            >
              <Plus className="w-5 h-5" />
              إضافة معدل جديد
            </button>
          </div>
          
          {addons.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-400 mb-4">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8v2a1 1 0 01-1 1H7a1 1 0 01-1-1V5a1 1 0 011-1h9a1 1 0 011 1z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No Modifiers Found</h3>
              <p className="text-gray-500 mb-4">This menu item doesn't have any modifiers yet.</p>
              <button
                onClick={() => alert('Add first modifier')}
                className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Add First Modifier
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto mt-2">
              <table className="w-full">
                <thead>
                  <tr className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                    <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
                      Code
                    </th>
                    <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-4 text-sm font-semibold uppercase tracking-wider">
                      Max Quantity
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-semibold uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {addons.map((addon, index) => (
                    <tr 
                      key={addon.id}
                      className={`${
                        index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                      } hover:bg-orange-50 transition-colors duration-200`}
                    >
                      <td className="py-4 mx-auto whitespace-nowrap">
                        <div className="text-center w-6 h-6 flex items-center justify-center mx-auto px-2 bg-orange-100 rounded-full">
                          <span className="text-sm font-medium text-orange-600">
                            {addon.id}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 whitespace-nowrap">
                        <div className="flex text-center justify-center items-center">
                          <div className={`w-3 h-3 rounded-full mr-3 ${getCategoryColor(addon.addon_group_name)}`}></div>
                          <span className="text-sm font-medium text-gray-900">
                            {addon.addon_group_name || 'Other'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className="text-sm font-semibold text-gray-800">
                          {addon.name}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                          {addon.code}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className="text-sm font-semibold text-green-600">
                          ${parseFloat(addon.price).toFixed(2)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className={`inline-flex items-center justify-center px-3 py-1 rounded-full text-xs font-medium ${
                          addon.is_required 
                            ? 'bg-red-100 text-red-800 border border-red-200' 
                            : 'bg-green-100 text-green-800 border border-green-200'
                        }`}>
                          {addon.is_required ? 'Required' : 'Optional'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className="text-sm font-medium text-gray-600 bg-blue-100 px-2 py-1 rounded">
                          {addon.max_quantity}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center justify-center space-x-2">
                          <button
                            onClick={() => console.log('Edit addon', addon.id)}
                            className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                            title="Edit Modifier"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => console.log('Delete addon', addon.id)}
                            className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200"
                            title="Delete Modifier"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ItemModifiersGrid