"use client"
import React, { useState } from 'react';
import { Plus, Phone, Mail, MapPin, Building, CreditCard, Settings, Users, Receipt, Truck, Clock, Info } from 'lucide-react';
import AppSettings from './AppSettings';
import BranchSettings from './BranchSettings';
import EmailSettings from './EmailSettings';
import TaxSettings from './TaxSettings';
import PaymentGateways from './PaymentGateways';
import StaffRoleManager from './StaffRoleManager';
import KOTSettings from './KOTSettings';
import DeliverySettings from './DeliverySettings';
import ReceiptSettings from './ReceiptSettings';
import AboutUsEditor from './AboutUsEditor';
import CustomerSiteSettings from './CustomerSiteSettings';
import ReservationSettings from './ReservationSettings';
import ThemeSettings from './ThemeSettings';
import ResturantInfo from './ResturantInfo';
import BillingPlanDetails from './BillingPlanDetails';
import CurrencyManagement from './CurrencyManagement';

const RestaurantInformationForm = () => {
  const [activeTab, setActiveTab] = useState('restaurant');
 

  const tabs = [
    { id: 'restaurant', label: 'Restaurant Information', icon: Building },
    { id: 'app', label: 'App Settings', icon: Settings },
    { id: 'branch', label: 'Branch Settings', icon: MapPin },
    { id: 'currencies', label: 'Currencies', icon: CreditCard },
    { id: 'email', label: 'Email Settings', icon: Mail },
    { id: 'taxes', label: 'Taxes', icon: Receipt },
    { id: 'payment', label: 'Payment Gateways', icon: CreditCard },
    { id: 'theme', label: 'Theme Settings', icon: Settings },
    { id: 'staff', label: 'Staff Roles', icon: Users },
    { id: 'billing', label: 'Billing', icon: Receipt },
    { id: 'brint', label: 'Brint', icon: Receipt }
  ];

  const secondaryTabs = [
    { id: 'reservation', label: 'Reservation Settings', icon: Clock },
    { id: 'about', label: 'About Us', icon: Info },
    { id: 'customer', label: 'Customer Site', icon: Users },
    { id: 'receipt', label: 'Receipt Settings', icon: Receipt },
    { id: 'delivery', label: 'Delivery Settings', icon: Truck },
    { id: 'kot', label: 'KOT Settings', icon: Settings }
  ];

 

  const renderTabContent = () => {
    switch (activeTab) {
      case 'restaurant':
        return (
         <ResturantInfo />
        );
      
      case 'app':
        return (
          <div className="">
         
            <AppSettings />
          </div>
        );
      
      case 'branch':
        return (
          <BranchSettings />
        );
      case 'email':
        return (
          <EmailSettings />
        );
      case 'taxes':
        return (
          <TaxSettings />
        );
      case 'payment':
        return (
          <PaymentGateways />
        );
      case 'staff':
        return (
          <StaffRoleManager  />
        );
      case 'kot':
        return (
          <KOTSettings  />
        );
      case 'delivery':
        return (
          <DeliverySettings  />
        );
      case 'receipt':
        return (
          <ReceiptSettings  />
        );
      case 'about':
        return (
          <AboutUsEditor   />
        );
      case 'customer':
        return (
          <CustomerSiteSettings   />
        );
      case 'reservation':
        return (
          <ReservationSettings   />
        );
      case 'theme':
        return (
          <ThemeSettings    />
        );
      case 'billing':
        return (
          <BillingPlanDetails    />
        );
      case 'currencies':
        return (
          <CurrencyManagement     />
        );
      
      default:
        return (
          <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-2xl p-8 border border-purple-200">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
                <p className="text-gray-600 mt-1">Configure your {activeTab} settings</p>
              </div>
            </div>
            <p className="text-gray-600">This section is under development.</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 mb-8 overflow-hidden">
          <div className="bg-gradient-to-r from-[#FF6500] to-orange-600 px-8 py-6">
            <h1 className="text-3xl font-bold text-white flex items-center gap-3">
              <Building className="w-8 h-8" />
              Restaurant Management System
            </h1>
            <p className="text-orange-100 mt-2">Manage your restaurant settings and configuration</p>
          </div>
          
          {/* Primary Navigation */}
          <div className="px-8 py-4 bg-white border-b border-gray-200">
            <div className="flex flex-wrap gap-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`px-4 py-3 rounded-xl font-semibold transition-all duration-200 flex items-center gap-2 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-[#FF6500] to-orange-600 text-white shadow-lg transform scale-105'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </div>
          
          {/* Secondary Navigation */}
          <div className="px-8 py-4 bg-gray-50">
            <div className="flex flex-wrap gap-2">
              {secondaryTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 text-sm ${
                      activeTab === tab.id
                        ? 'bg-[#FF6500] text-white shadow-md'
                        : 'bg-white text-gray-600 hover:bg-gray-100 hover:text-gray-800 border border-gray-200'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
          <div className="p-8">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestaurantInformationForm;