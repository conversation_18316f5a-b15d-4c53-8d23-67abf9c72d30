"use client"
import React, { useState } from 'react';

// Placeholder SelectDropdown component
const SelectDropdown = ({ value, onChange, options }) => (
  <select
    value={value}
    onChange={(e) => onChange(e.target.value)}
    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg appearance-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-gray-700"
  >
    {options.map((option) => (
      <option key={option.value} value={option.value}>
        {option.label}
      </option>
    ))}
  </select>
);

// Placeholder TextInput component
const TextInput = ({ label, placeholder, value, onChange }) => (
  <div className="mb-4">
    <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
    <input
      type="text"
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
    />
  </div>
);

export default function CustomerSiteSettings() {
  const [settings, setSettings] = useState({
    allowCustomerToPlaceOrders: true,
    customerNeedToLogin: false,
    allowDineIn: true,
    allowDeliveryOrders: true,
    allowPickupOrders: true,
    enableTipCustomerSite: true,
    enableTipPOS: true,
    autoConfirmOrderStatus: false,
    enableWaiterRequest: true,
    onDesktop: true,
    onMobile: true,
    onlyWhenOpenViaQR: false,
    tableRequiredForDineIn: true
  });

  const [defaultReservationStatus, setDefaultReservationStatus] = useState('Pending');
  
  const [socialMediaLinks, setSocialMediaLinks] = useState({
    facebook: '',
    instagram: '',
    twitter: '',
    yelp: ''
  });

  const [seoData, setSeoData] = useState({
    metaKeyword: '',
    metaDescription: ''
  });

  const handleSeoChange = (field, value) => {
    setSeoData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSocialMediaChange = (platform, value) => {
    setSocialMediaLinks(prev => ({
      ...prev,
      [platform]: value
    }));
  };

  const handleSave = () => {
    // Handle save functionality here
    console.log('Settings saved:', { settings, socialMediaLinks, seoData, defaultReservationStatus });
    alert('Settings saved successfully!');
  };

  const handleToggle = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const CheckboxToggle = ({ id, checked, onChange }) => (
    <div className="relative">
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={onChange}
        className="sr-only"
      />
      <label
        htmlFor={id}
        className={`flex items-center justify-center w-6 h-6 rounded cursor-pointer transition-colors ${
          checked 
            ? 'bg-orange-500 border-2 border-orange-500' 
            : 'bg-white border-2 border-gray-300 hover:border-gray-400'
        }`}
      >
        {checked && (
          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        )}
      </label>
    </div>
  );

  const SettingRow = ({ title, description, settingKey, checked }) => (
    <div className="flex items-start justify-between py-6 border-b border-gray-100 last:border-b-0">
      <div className="flex-1 pr-8">
        <h3 className="text-base font-medium text-gray-900 mb-1">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      <div className="flex-shrink-0">
        <CheckboxToggle
          id={settingKey}
          checked={checked}
          onChange={() => handleToggle(settingKey)}
        />
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-8 bg-white">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Customer Site</h1>
        <p className="text-gray-600">Configure settings related to the customer site.</p>
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Order Settings</h2>
        
        <div className="space-y-0">
          <SettingRow
            title="Allow Customer to place Orders"
            description="Enable this to allow customers to place orders."
            settingKey="allowCustomerToPlaceOrders"
            checked={settings.allowCustomerToPlaceOrders}
          />
          
          <SettingRow
            title="Customer need to login to place order?"
            description="Enable this to require customers to login before placing orders."
            settingKey="customerNeedToLogin"
            checked={settings.customerNeedToLogin}
          />
          
          <SettingRow
            title="Allow Dine-In"
            description="Enable this to allow dine-in orders."
            settingKey="allowDineIn"
            checked={settings.allowDineIn}
          />
          
          <SettingRow
            title="Allow Customer to place Delivery Orders"
            description="Enable this to allow customers to place delivery orders."
            settingKey="allowDeliveryOrders"
            checked={settings.allowDeliveryOrders}
          />
          
          <SettingRow
            title="Allow Customer to place Pickup Orders"
            description="Enable this to allow customers to place pickup orders."
            settingKey="allowPickupOrders"
            checked={settings.allowPickupOrders}
          />
          
          <SettingRow
            title="Enable Tip Customer Site"
            description="Enable this to allow customers to add tips to their orders."
            settingKey="enableTipCustomerSite"
            checked={settings.enableTipCustomerSite}
          />
          
          <SettingRow
            title="Enable Tip POS"
            description="Enable this to allow adding tips to their orders in POS."
            settingKey="enableTipPOS"
            checked={settings.enableTipPOS}
          />
          
          <SettingRow
            title="Auto Confirm Order Status"
            description="Enable this to automatically confirm orders status and send to KOT."
            settingKey="autoConfirmOrderStatus"
            checked={settings.autoConfirmOrderStatus}
          />
        </div>
      </div>

      {/* Call Waiter Settings */}
      <div className="bg-gray-50 rounded-lg p-6 mt-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Call Waiter Settings</h2>
        
        <div className="space-y-0">
          <SettingRow
            title="Enable Waiter Request"
            description="Enable this to allow customers to call waiters for service."
            settingKey="enableWaiterRequest"
            checked={settings.enableWaiterRequest}
          />
          
          <SettingRow
            title="On Desktop"
            description="Enable this to allow customers to call waiters on desktop."
            settingKey="onDesktop"
            checked={settings.onDesktop}
          />
          
          <SettingRow
            title="On Mobile"
            description="Enable this to allow customers to call waiters on mobile."
            settingKey="onMobile"
            checked={settings.onMobile}
          />
          
          <SettingRow
            title="Only When Open via QR Code"
            description="Enable this to allow customers to call waiters only when they open the app via QR code."
            settingKey="onlyWhenOpenViaQR"
            checked={settings.onlyWhenOpenViaQR}
          />
        </div>
      </div>

      {/* Dine-in Settings */}
      <div className="bg-gray-50 rounded-lg p-6 mt-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Dine-in Settings</h2>
        
        <div className="space-y-0">
          <SettingRow
            title="Table Required for Dine-In"
            description="Enable this to require customers to select a table for dine-in orders."
            settingKey="tableRequiredForDineIn"
            checked={settings.tableRequiredForDineIn}
          />
          
          <div className="py-6">
            <div className="mb-4">
              <h3 className="text-base font-medium text-gray-900 mb-1">Default Table Reservation Status</h3>
              <p className="text-sm text-gray-600 mb-4">Select the default status for new reservations.</p>
            </div>
            <SelectDropdown
              value={defaultReservationStatus}
              onChange={setDefaultReservationStatus}
              options={[
                { value: 'Pending', label: 'Pending' },
                { value: 'Confirmed', label: 'Confirmed' },
                { value: 'Cancelled', label: 'Cancelled' },
                { value: 'Completed', label: 'Completed' }
              ]}
            />
          </div>
        </div>
      </div>

      {/* Social Media Links */}
      <div className="bg-gray-50 rounded-lg p-6 mt-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Social Media Links</h2>
        
        <div className="space-y-0">
          <TextInput
            label="Facebook Link"
            placeholder="Enter your Facebook URL"
            value={socialMediaLinks.facebook}
            onChange={(value) => handleSocialMediaChange('facebook', value)}
          />
          
          <TextInput
            label="Instagram Link"
            placeholder="Enter your Instagram URL"
            value={socialMediaLinks.instagram}
            onChange={(value) => handleSocialMediaChange('instagram', value)}
          />
          
          <TextInput
            label="Twitter Link"
            placeholder="Enter your Twitter handle"
            value={socialMediaLinks.twitter}
            onChange={(value) => handleSocialMediaChange('twitter', value)}
          />
          
          <TextInput
            label="Yelp Link"
            placeholder="Enter your Yelp URL"
            value={socialMediaLinks.yelp}
            onChange={(value) => handleSocialMediaChange('yelp', value)}
          />
        </div>
      </div>
    </div>
  );
}