"use client"
import React, { useState } from 'react';
import { User, MapPin, Users, Upload, Eye, Save, CreditCard, Building, Receipt, QrCode } from 'lucide-react';

const ReceiptSettings = () => {
  const [settings, setSettings] = useState({
    showCustomerName: true,
    showCustomerAddress: true,
    showRestaurantLogo: true,
    showRestaurantTax: true,
    showTableNo: true,
    showWaiterName: true,
    showTotalGuest: true,
    showPaymentQRCode: false,
    showPaymentDetails: false
  });

  const handleToggle = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const CheckboxItem = ({ icon: Icon, label, checked, onChange }) => (
    <div className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
      <div className="flex items-center space-x-3 flex-1">
        <Icon className="w-5 h-5 text-gray-500" />
        <label className="text-sm font-medium text-gray-700 cursor-pointer flex-1">
          {label}
        </label>
      </div>
      <div className="relative">
        <input
          type="checkbox"
          checked={checked}
          onChange={onChange}
          className="sr-only"
        />
        <div
          onClick={onChange}
          className={`w-6 h-6 rounded border-2 cursor-pointer transition-all duration-200 flex items-center justify-center ${
            checked
              ? 'bg-[#FF6500] border-[#FF6500]'
              : 'bg-white border-gray-300 hover:border-gray-400'
          }`}
        >
          {checked && (
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900">Receipt Settings</h1>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Information */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h2>
              <div className="space-y-2">
                <CheckboxItem
                  icon={User}
                  label="Show Customer Name"
                  checked={settings.showCustomerName}
                  onChange={() => handleToggle('showCustomerName')}
                />
                <CheckboxItem
                  icon={MapPin}
                  label="Show Customer Address"
                  checked={settings.showCustomerAddress}
                  onChange={() => handleToggle('showCustomerAddress')}
                />
              </div>
            </div>

            {/* Restaurant Information */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Restaurant Information</h2>
              <div className="space-y-2">
                <CheckboxItem
                  icon={Building}
                  label="Show Restaurant Logo"
                  checked={settings.showRestaurantLogo}
                  onChange={() => handleToggle('showRestaurantLogo')}
                />
                <CheckboxItem
                  icon={Receipt}
                  label="Show Restaurant Tax"
                  checked={settings.showRestaurantTax}
                  onChange={() => handleToggle('showRestaurantTax')}
                />
              </div>
            </div>

            {/* Order Details */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Details</h2>
              <div className="space-y-2">
                <CheckboxItem
                  icon={Receipt}
                  label="Table no."
                  checked={settings.showTableNo}
                  onChange={() => handleToggle('showTableNo')}
                />
                <CheckboxItem
                  icon={User}
                  label="Show Waiter Name"
                  checked={settings.showWaiterName}
                  onChange={() => handleToggle('showWaiterName')}
                />
                <CheckboxItem
                  icon={Users}
                  label="Show Total guest"
                  checked={settings.showTotalGuest}
                  onChange={() => handleToggle('showTotalGuest')}
                />
              </div>
            </div>

            {/* Payment Details */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Details</h2>
              
              {/* QR Code Section */}
              <div className="mb-6">
                <div className="flex items-center justify-center w-24 h-24 bg-white border-2 border-gray-200 rounded-lg mb-4">
                  <QrCode className="w-16 h-16 text-gray-400" />
                </div>
                <button className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-[#FF6500] transition-colors">
                  <Upload className="w-4 h-4" />
                  <span>Upload Payment QR Code</span>
                </button>
              </div>

              <div className="space-y-2">
                <CheckboxItem
                  icon={QrCode}
                  label="Show Payment QR Code"
                  checked={settings.showPaymentQRCode}
                  onChange={() => handleToggle('showPaymentQRCode')}
                />
                <CheckboxItem
                  icon={CreditCard}
                  label="Show Payment Details"
                  checked={settings.showPaymentDetails}
                  onChange={() => handleToggle('showPaymentDetails')}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
          <button className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors">
            <Eye className="w-4 h-4" />
            <span>Preview Receipt</span>
          </button>
          
          <button className="flex items-center space-x-2 px-6 py-2 bg-[#FF6500] text-white font-medium rounded-lg hover:bg-[#E55A00] transition-colors">
            <Save className="w-4 h-4" />
            <span>Save</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReceiptSettings;