import React from "react";
import paySystem from "../../../../public/images/montring.jpg";
import ReusabelLink from "../ReusabelComponent/ReusabelLink";

const PaySystem = () => {
  return (
    <div className="relative  my-4 rounded-4xl  max-w-7xl mx-auto bg-gradient-to-br from-[#FFD5C7] via-[#FCB190]/30 to-[#FFD5C7] overflow-hidden">
      <div className="relative   p-8 ">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6 order-2 lg:order-1">
            <h1 className="font-bold text-2xl  leading-tight text-gray-900">
              استقبل المدفوعات الرقمية من عملائك عبر كل نقطة اتصال!
            </h1>
            <p className="text-lg md:text-xl text-gray-700 leading-relaxed">
              نبسّط عملية استلام المدفوعات في مطعمك واحصل على تسويات يومية إلى
              حسابك البنكي مع فودكس Pay.
            </p>
            <div className="pt-4">
              <ReusabelLink text={"اكتشف إيبيسيس pay"} href={"/"} background={true}/>
            </div>
          </div>
          <div className="flex justify-center order-1 lg:order-2">
            <div className="relative">
              <div className="relative  p-8 ">
                <img
                  src={paySystem.src}
                  alt="Pricing"
                  className="relative rounded-2xl w-full max-w-md h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaySystem;
