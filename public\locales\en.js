export default {
  // common
  direction: "ltr",

  login: "Login",
  header: {
    home: "Home",
    login: "signIn",
    logOut: "Logout",
    clientDashboard: "Client Dashboard",
    jobOpportunities: "Request Demo ",
    allProperties: "All Properties",
    searchPlaceholder: "Search",
    userMenu: {
      settings: "Settings",
      logout: "Logout",
    },
    logoutConfirm: {
      title: "Confirm Logout",
      message: "Are you sure you want to logout?",
      cancel: "Cancel",
      confirm: "Logout",
    },
    logoutSuccess: "Logout Successful",
    sendMessage: "support",
  },
  welcome: "Welcome",
  dashboard: "Dashboard",
  language: "Language",
  english: "English",
  arabic: "Arabic",
  hero: {
    title1: "MAKE IT AI",
    subtitle1: "EFFICIENT & COST-EFFECTIVE",
    title2: "SMART REAL ESTATE CRM",
    subtitle2: "AUTOMATE & SCALE EFFORTLESSLY",
  },
  buttons: {
    tryChat: "Try our Chatbot",
    requestDemo: "Request Demo",
    addNew: "Add New",
      addPhase:" Add Phase",
    updatePhase:" update Phase"
  },
  heroSection: {
    title1: "Sell Smarter",
    title2: "You Market, We Sell.",
    subtitle: "AI Sales team that handle clients requesst 24/7.",
    description:
      "Lena AI qualify andfilter  leads instantly, engage prospects 24/7, and boost conversions so you focus on closing, not chasing cold leads.",
    ctaButton: "Get Started",
    backgroundAlt: "Background",
    aiImageAlt: "AI Assistant",
    message1: "⁠Filter Leads",
    message2: "Smart CRM for Units, Leads & Sales Teams",
    message3: "24/7 AI Sales Team – Never Miss a Lead",
    message4: "AI Chatbot on Your Website",
    message5: "Live Client Handling & Market Insights",
    message6: "Auto Follow-Ups & Effortless Scaling",
    message7: "follow up, and scale with effortless",
  },
  dashboard: {
    title: {
      part1: "Lena is with you",
      part2: "all the time",
    },
    subtitle:
      "Mobile app, and website, you can track your work anytime anywhere",
    featuresTitle: "Some solutions we offer:",
    features: {
      feature1:
        "Mobile Chat App – Engage leads in real-time through a dedicated chat application",
      feature2:
        "Broker Mobile App – Instantly notifies your sales team when a property viewing is scheduled.",
      feature3:
        " AI Follow-Up – Automatically follows up with leads, saving your team time and effort.",
      feature4:
        "Website Chatbot – Handles inquiries from your website visitors 24/7, especially those coming from Google.",
      feature5:
        "Smart CRM – Manage sales, units, and client data in one place with a seamless interface.",
      feature6:
        "Client Insights – Get AI-powered analytics about each client's needs, behaviors, and communication history to close deals faster.",
    },
    ctaButton: "Get Started",
    images: {
      desktopAlt: "Lena CRM Dashboard Desktop View",
      mobileAlt: "Lena CRM Mobile View",
    },
  },
  salesManager: {
    title: "Do you face the same challenges?",
    subtitle:
      "Meet Sara & Hani: Real Estate Professionals Facing Real Challenges",
    haniTitle: "Meet Hani – The Overwhelmed Sales Manager",
    challenges: {
      challenge1:
        "I waste hours talking to unqualified leads who aren't serious buyers.",
      challenge2:
        "I need a faster way to follow up—before my leads buy somewhere else.",
      challenge3:
        "Our CRM is full of junk leads, making it hard to track real opportunities.",
    },
    imageAlt: "Hani - The Overwhelmed Sales Manager",
  },
  dataInsights: {
    imageAlt: "Sara - The Marketing Manager",
    saraTitle: "Meet Sara – The Marketing Manager",
    challenges: {
      challenge1:
        "I spend thousands on ads, but most leads aren't even serious buyers!",
      challenge2:
        "Sales blames me for 'low-quality leads,' but I don't have the right tools to qualify them.",
      challenge3:
        "By the time our team reaches out, leads have already chosen a competitor.",
    },
    footer: {
      question: "Sounds familiar?",
      solution: "Lena is here to solve these challenges.",
    },
    ctaButton: "Get Started",
  },
  footer: {
    companyInfo: {
      title: "Company Information",
      address:
        "Company Address: 505 Siac Building, ARCHPLAN Square, New Capital, Cairo, Egypt",
    },
    contact: {
      title: "Contact Details",
      phone: "Contact info: ",
      sales: "Sales",
      support: "Support",
    },
    connect: {
      title: "Connect With Us",
      linkedin: "LinkedIn",
      chat: "chat with Us",
      privacyPolicy: "Read our Privacy Policy",
      dowenloadios: "Download our iOS App",
      dowenloadAndroid: "Download our Android App",
      Facebook: "Facebook",
    },
    copyright: "©",
    companyName: "Lena AI",
    rightsReserved: "All rights reserved",
    version: "Version",
  },
  calendarModal: {
    closeButton: "Close",
    confirmationTitle: "Booking Confirmed!",
    thankYou: "Thank you,",
    meetingScheduled: "Your meeting has been scheduled for",
    at: "at",
    confirmationSent: "We've sent a confirmation email to",
    withDetails: "with all the details.",
  },
  calendar: {
    backButton: "Go back",
    logoAlt: "Lena AI logo",
    companyName: "Lena AI",
    meetingDuration: "30 Minute Meeting",
    duration: "30 min",
    conferencingDetails: "Web conferencing details provided upon confirmation.",
    bookMeeting: "Book a meeting",
    meetingDescription: "Book 30-Minute meeting with Lena Team",
    selectDateTime: "Select a Date & Time",
    previousMonth: "Previous month",
    nextMonth: "Next month",
    dayHeaders: ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"],
    confirmButton: "Confirm",
    availableTimes: "Available Times",
    selectDayPrompt: "Select a day to see available times.",
    enterInfo: "Enter Your Information",
    bookingFor: "You're booking a meeting on",
    at: "at",
    nameLabel: "Name",
    emailLabel: "Email Address",
    phoneLabel: "Phone Number",
    phonePlaceholder: "0 ************",
    companyLabel: "Company",
    notesLabel: "Please share anything that will help prepare for our meeting.",
    notesPlaceholder: "Let us know how we can help you...",
    completeBooking: "Complete Booking",
    bookingInProgress: "Booking in progress...",
  },
  login: {
    title: "Lena AI",
    subtitle: "Sign in to your account",
    backToHome: "Back to Home",
    usernameLabel: "Username",
    usernamePlaceholder: "Enter your username",
    passwordLabel: "Password",
    passwordPlaceholder: "Enter your password",
    signInButton: "Sign In",
    successMessage: "Login successful",
    errorMessage: "Login failed. Please check your credentials",
    footer: {
      copyright: "Lena AI. All rights reserved",
      version: "Version",
    },
  },
  sidebar: {
    "schedule":"Calendar",
    myProjects: "My Projects",
    dashboard: "Conversations",
    analytics: "Analytics",
    units: "Units",
    team: "Team",
    darkMode: "Dark Mode",
    logout: "Log Out",
    logoutConfirm: {
      title: "Are you sure?",
      message: "You will be logged out of your account",
      cancel: "Cancel",
      confirm: "Log Out",
    },
    logoutSuccess: "Logout Successful",
  },
  schaduall: {
    "assignError":"Failed to assign salesperson",
    "salesAssigned":"Salesperson assigned successfully",
    "ChooseSalesperson":"    select your sales for this task",
    "Available":"Available",
"NoSalesAvailable":"no Sales Avaliabel",
    "noappointments": "لا توجد مواعيد هذا الأسبوع",
    "noSale": "لا توجد مبيعات متاحة",
    "scheduleweek": "جدولك خالٍ هذا الأسبوع.",
    "Schedule":"مهام الاسبوع",
   },
  "Noappointments":"No appointments this week",
  "scheduleweek":"Your schedule is clear for this week.",
  "Schedule":" Week Schedule",
  "next":"next",
  "previous":"previous",
  "AllSales": "All Sales",
  "Unassigned": "Unassigned",
  "Noappointments": "No Appointments",
  "scheduleweek": "No appointments scheduled for this week",
  dashboardFilter: {
    actions: {
      all: "All actions",
      Monitorlead: "Monitor lead",
      makeCall: "Make a call",
      officeVisit: "Office visit",
      propertyView: "Property view",
      notInterested: "Not interested",
      notQualified: "Not qualified",
      followUpLater: "Follow up later",
      missingRequirement: "Missing Requirement",
      blocked: "blocked",
      print: "print",
      onGoingConversion: "On Going Conversion",
      qualifiedLead: "Qualified Lead",
      noAction: "No Action",
      allActions: "All Actions",
    },

    datePicker: {
      startDate: "Start Date",
      endDate: "End Date",
      cancel: "Cancel",
      apply: "Apply",
    },
    whatsappButton: "WhatsApp Leads",
    premuim: " Premium Feature",
    ADD: "Add New Lead",
  },
  search: {
    placeholder: "Search client by name or number...",
  },
  clientsTable: {
    noClients: "No clients found",
    headers: {
      name: "Name",
      userNumber: "Number",
      date: "Date",
      requirements: "Requirements",
      messageCount: "Messages Count",
      action: "Action",
    },
    newLead: "New Lead",
    notDefined: "Not defined",
    actions: {
      addAction: "Add Action to {count} client(s)",
    },
    lastActivity: {
      na: "N/A",
    },
    unreadMessages: {
      na: "N/A",
      count: "{count} unread messages",
    }
  },
  actionForm: {
    actions: {
      officeVisit: "Office visit",
      makeCall: "Make a call",
      propertyView: "Property view",
      notInterested: "Not interested",
      notQualified: "Not qualified",
      followUpLater: "Follow up later",
      missingRequirement: "Missing Requirement",
    },
    commentPlaceholder: "Enter your comment here...",
    submitButton: "Send",
    successMessage: "Action added successfully",
    errorMessage: "Failed to add action",
    aiAction: "AI Actions",
  },

  propertyDetails: {
    title: " Requirements",
    title2: "/Requirements",
    fields: {
      buildingType: "Building Type",
      landArea: "Land Area",
      floor: "Floor",
      roomsCount: "Rooms Count",
      bathroomCount: "Bathroom Count",
      viewType: "View",
      garageSize: "Garden Size",
      finishingType: "Finishing",
      developer: "Developer",
      downPayment: "Down Payment",
      deliveryDate: "Delivery Date",
      totalPrice: "Total Price",
      propertyPurpose: "For {purpose}",
    },
    purchaseProbability: "Purchase Probability",
    buttons: {
      close: "Close",
    },
    notAvailable: "-----",
  },
  identifierUnit: {
    title: "Real Estate Properties",
    subtitle: "Explore our exclusive listings",
  },
  unitsFilter: {
    allDevelopers: "All Developers",
    allCompounds: "All Projects",
    allPurposes: "All Purposes",
    allPropertyTypes: "All Property Types",
    price: "price",
    applay: "Apply",
    min: " Min Price",
    max: "Max Price",
    allCities: "All Cities",
    activeFilter: " Active Filters:",
    clearall: " Clear All",
    purposes: {
      buy: "Buy",
      rent: "Rent",
      sell: "Sell",
      lease: "Lease",
    },
    cities: {
      "Beni Suef": "Beni Suef",
      Qena: "Qena",
      Ismailia: "Ismailia",
      Alexandria: "Alexandria",
      Aswan: "Aswan",
      "Kafr El Sheikh": "Kafr El Sheikh",
      Luxor: "Luxor",
      Faiyum: "Faiyum",
      Monufia: "Monufia",
      "Red Sea": "Red Sea",
      Cairo: "Cairo",
      Giza: "Giza",
      "North Sinai": "North Sinai",
      Dakahlia: "Dakahlia",
      Beheira: "Beheira",
      "Port Said": "Port Said",
      Qalyubia: "Qalyubia",
      Suez: "Suez",
      "South Sinai": "South Sinai",
      Gharbia: "Gharbia",
      Sohag: "Sohag",
      Minya: "Minya",
      Matrouh: "Matrouh",
      Damietta: "Damietta",
      "New Administrative Capital": "New Administrative Capital",
      Sharqia: "Sharqia",
      Asyut: "Asyut",
      "New Valley": "New Valley",
    },
  },
  shareModel: {
    Content: "Share Unit Content",
    post: " Post Content",
    images: "Images",
    loading: "Loading share data...",
    copy: " Copied!",
    copyLink: "Copy with Link",
    noData: "No data available to share.",
    noImage: "No images available to share.",
  },
  unitsSearch: {
    placeholder: "Search by name or location...",
    clearAriaLabel: "Clear search",
  },
  units: {
    addButton: {
      addNew: "New Unit",
      edit: "Edit Unit",
      back: "back",
    },
  },
  modal: {
    addNewUnit: "Add New Unit",
    editUnit: "Edit Unit",
  },
  steps: {
    basicDetails: "Basic Details",
    financialDetails: "Financial Details",
    rentalDetails: "Rental Details",
    imagesInfo: "Images & Additional Info",
  },
  buttons: {
    back: "Back",
    next: "Next",
    saveUnit: "Save Unit",
  },
  toasts: {
    enterValidPrice:
      "Please enter a price greater than 0 for at least one duration type",
    uploadImage: "Please upload at least one image.",
    selectPurpose: "Please select a purpose for the unit (sell/rent)",
    errorProcessing:
      "An error occurred while processing your request. Please try again",
  },
  updatePhase:"updatePhase",
  viewInCRM: "View in CRM",
  phases: "Phases",
  description: "Description",
  addNewProject: "Add New Project",
  noPhsesProject: "No phases for this project",
  updateProject: " update project",
  updating: "updating...",
  compoundUpdated: "project updated successfully!",
  compoundAdded: "project added successfully!",

  projectUndfined: " No projects found.",
  cancelButton: "cancel",
  deleteButton: "delete",
  sureDelet: " Are you sure you want to delete",
  actionDelet: "This action cannot be undone.",
  deleteTitel: "delete project",
  failedProject: "Failed to delete project",
  projectDelete: "project deleted successfuly",
  associateProject:
    "This project is associated with multiple units. Kindly contact support to delete it",
  phase: "Phase",
  viewOnGoogleMaps: "View on Google Maps",
  clickToViewFullscreen: "Click to view fullscreen",
  watchVideo: "Watch Video",
  addPhase: "Add Phase",
  images: "Images",
  loadingShareData: "Loading share data...",
  projectFirst: "Select project first",
  shareUnitContent: "Share Unit Content",
  postContent: "Post Content",
  clickCopy:
    "Click the 'Copy with Link' button to copy the text, images, and share link.",
  noImagesAvailable: "No images available to share.",
  copyWithLink: "Copy with Link",
  copied: "Copied!",
  addPhaseSuccess: "Phase added successfully!",
  addPhaseFailed: "Failed to add phase. Please try again.",
  formLabels: {
    unitTitle: "Unit Title",
    buildingType: "Building Type",
    purpose: "Purpose",
    compound: "Compound",
    view: "View",
    isGated: "Gated Community",
    city: "City",
    district: "District",
    developer: "Developer",
    deliveryDate: "Delivery Date",
    deliveryStatus: "Delivery Status",
    bathroomCount: "Bathrooms",
    floor: "Floor",
    roomsCount: "Rooms",
    landArea: "Land Area (m²)",
    gardenSize: "Garden Size (m²)",
    finishing: "Finishing",
    garageArea: "Garage Area (m²)",
    images: "Images",
    districtFirst: "select district first",
    cityFirst: "select city first",
    selectDistrict: "select district",
  },
  buildingTypes: {
    apartment: "Apartment",
    villa: "Villa",
    townhouse: "Townhouse",
    duplex: "Duplex",
    penthouse: "Penthouse",
    studio: "Studio",
    chalet: "Chalet",
    office: "Office",
    shop: "Shop",
    land: "Land",
  },
 
  purpose: {
    sell: "Sell",
    rent: "Rent",
    buy: "Buy",
    lease: "Lease",
  },
  view: {
    garden: "Garden",
    pool: "Pool",
    sea: "Sea",
    landmark: "Landmark",
    street: "Street",
    other: "Other",
  },
  deliveryStatus: {
    ready: "Ready",
    underConstruction: "Under Construction",
    offPlan: "Off Plan",
  },
  finishing: {
    finished: "Finished",
    semiFinished: "Semi-Finished",
    core: "Core & Shell",
    unfinished: "Unfinished",
  },
  sale: {
    downPayment: "Down Payment",
    totalPrice: "Total Price",
    deliveryDate: "Delivery Date",
    paymentPlans: "Payment Plans",
    addPaymentPlan: "Add Payment Plan",
    yearsLabel: "Years",
    priceLabel: "Price",
    maintenanceLabel: "Maintenance",
  },
  rental: {
    isAvailable: "Available for Rent",
    availabilityDate: "Availability Date",
    rentDuration: "Rent Duration",
    daily: "Daily",
    weekly: "Weekly",
    monthly: "Monthly",
    price: "Price",
    securityDeposit: "Security Deposit",
    cleaningFee: "Cleaning Fee",
    serviceFee: "Service Fee",
    amenities: "Amenities",
    currency: "Currency",
  },
  basicDetails: {
    selectPhase: "Select Phase",
    noPhases: "No phases in this project",
    noPhasespropertyDetails: "Property Details",
    propertySpecs: "Property Specifications",
    unitTitle: "Unit Title",
    compound: "project",
    buildingType: "Building Type",
    purpose: "Purpose",
    city: "City",
    view: "View",
    district: "District",
    rooms: "Rooms",
    bathrooms: "Bathrooms",
    floor: "Floor",
    landArea: "Land Area",
    gardenSize: "Garden Size",
    garageArea: "Garage Area",
    selectCompound: "Select project",
    selectPurpose: "Select purpose",
    selectCity: "Select city",
    selectView: "Select view",
    placeholders: {
      unitTitle: "Enter unit title",
      district: "district name",
      code: "unit code (optional)",
    },
    code: "unit code",
    model: "Unit Model",
    buildingTypes: {
      apartment: "Apartment",
      villa: "Villa",
      townhouse: "Townhouse",
      duplex: "Duplex",
      penthouse: "Penthouse",
      studio: "Studio",
      chalet: "Chalet",
      office: "Office",
      shop: "Shop",
      twinhouse: "Twinhouse",
      house: "House",
    },
    purposes: {
      sell: "Sell",
      rent: "Rent",
    },
    cities: {
      cairo: "Cairo",
      alexandria: "Alexandria",
      giza: "Giza",
      newCairo: "New Cairo",
      october6: "6th of October",
      elShorouk: "El Shorouk",
      sheikhZayed: "Sheikh Zayed",
    },
    views: {
      park: "Park",
      street: "Street",
      lagoon: "Lagoon",
      sea: "Sea",
      city: "City",
      river: "River",
      pool: "Pool",
      golf: "Golf",
      garden: "Garden",
      openArea: "Open Area",
      mountain: "Mountain",
    },
  },
  saleDetails: {
    financialDetails: "Financial Details",
    totalPrice: "Total Price",
    deliveryDate: "Delivery Date",
    downPayment: "Down Payment",
    paymentPlans: "Payment Plans",
    addPlan: "Add Plan",
    noPlans: "No payment plans added yet.",
    years: "Years",
    price: "Price",
    maintenance: "Maintenance",
  },
  rentalDetails: {
    availability: "Availability",
    chooseAvailabilityDate: "choose availability date",
    availableForRent: "Available for rent",
    rentDurationOptions: "Rent Duration Options",
    daily: "Daily",
    weekly: "Weekly",
    monthly: "Monthly",
    price: "Price",
    securityDeposit: "Security Deposit",
    cleaningFee: "Cleaning Fee",
    serviceFee: "Service Fee",
    amenities: "Amenities",
    amenities: {
      wifi: "Wifi",
      dryer: "Dryer",
      air_conditioning: "Air Conditioning",
      heating: "Heating",
      smart_tv: "Smart TV",
      hair_dryer: "Hair Dryer",
      pool: "Pool",
      free_parking: "Free Parking",
      ev_charger: "EV Charger",
      bbq_grill: "BBQ Grill",
      indoor_fireplace: "Indoor Fireplace",
      smoking_allowed: "Smoking Allowed",
      beachfront: "Beachfront",
      smoke_alarm: "Smoke Alarm",
      co_alarm: "CO Alarm",
    },
  },
  ourResultsInNumbers: {
    title: "Our Results in Numbers",
    fasterLeadResponse: "Faster Lead Response",
    fasterLeadResponseValue: "3X",
    moreConversion: "More Conversion",
    moreConversionValue: "40%",
    engagement: "Engagement",
    engagementValue: "24/7",
    lessManualWork: "Less Manual Work",
    lessManualWorkValue: "80%",
    increaseInSales: "Increase In Sales",
    increaseInSalesValue: "26%",
    increaseInCSAT: "Increase In CSAT",
    increaseInCSATValue: "60%",
  },
  typeYourMessage: "Type your message...",
  Ai: "AI Reply",
  send: "Send",
  manual: "Manual Reply",
  january: "January",
  february: "February",
  march: "March",
  april: "April",
  may: "May",
  june: "June",
  july: "July",
  august: "August",
  september: "September",
  october: "October",
  november: "November",
  december: "December",
  numberConversation: "Number of Conversations",
  selectNumberOfMonths: "Select Number of Months",
  lastMonth: "Last Month",
  lastMonths: "Last {count} Months",
  conversation: "Conversation",
  dailyConversationAnalysis: "Daily Conversation Analysis",
  averageMessagesPerUser: "Average Messages per User",
  count: "Count of Messages & Conversations",
  dashboardTitle: "Analytics Dashboard",
  dashboardDescription: "Showing user activities and daily action breakdowns",
  conversationAnalysis: "Conversation Analysis",
  dailyConversationAnalysis: "Daily Conversation Analysis",
  countMessagesConversations: "Count of Messages & Conversations",
  numberOfConversations: "Number of Conversations",
  averageMessagesPerUser: "Average Messages per User",
  conversations: "Conversations",
  avgMessages: "Avg Messages",
  actionFrequency: "Action Frequency",
  monthlyActionFrequency: "Monthly Action Frequency",
  breakdownDescription: "Breakdown of actions taken each month",
  bubbleVisualization: "Bubble visualization of daily action totals",
  totalActions: "Total Actions",
  clickBarInfo: "Click on any bar to see detailed breakdown of actions",
  saveDeveloper: "Save Developer",
  saving: "Saving...",
  DeveloperName: "Developer Name",
  cancel: "Cancel",
  bathrooms: "Bathrooms",
  for: "For  ",
  sell: "Sell",
  rent: "Rent",
  city: "City",
  project: "Project",
  rentPrice: "Rent Price",
  totalPrice: "Total Price",
  addNew: "Add New",
  additionalDetails: "Additional Details",
  finishingType: "Finishing Type",
  selectDeveloper: "Select developer",
  furnishingType: "Furnishing Type",
  developer: "Developer",
  selectFinishingType: "Select finishing type",
  furnished: "Furnished",
  unfurnished: "Unfurnished",
  fullyFinished: "Fully Finished",
  semiFinished: "Semi Finished",
  coreAndShell: "Core & Shell",
  selectDeveloper: "Select developer",
  propertyImages: "Property Images",
  maximum: "Maximum",
  clickOrDragAndDrop: "Click or drag and drop images here",
  supportedFormats: "Supported formats: JPG, PNG, WEBP (Max 5MB each)",
  uploading: "Uploading...",
  upload: "Upload",
  selectedImage: "Selected Image",
  selectedImages: "Selected Images",
  images: "images",
  selectedImagesTitle: "Selected Images:",
  uploadedImagesTitle: "Uploaded Images:",
  retry: "Retry",
  imageDeletedSuccess: "Image deleted successfully",
  failedToDeleteImage: "Failed to delete image. Please try again.",
  maxImagesError:
    "You can only upload a maximum of 8 images. Please remove some images before adding new ones.",
  invalidFileType: "Invalid file type. Please upload JPG, PNG, or WEBP images.",
  fileSizeExceeds: "exceeds 3MB. Please upload smaller images.",
  failedToUploadImage: "Failed to upload image",

  unitPage: {
    backToUnits: "Back to Units",
    in: "in",
    deleteUnit: "Delete Unit",
    confirmDeleteMsg: "Are you sure you want to delete this unit?",
    cancel: "Cancel",
    delete: "Delete",
    deleteFail: "Failed to delete unit. Please try again.",
    deleteError: "An error occurred while deleting the unit.",
  },
  unitDetails: {
    title: "Unit Details",
    noImages: "No images available",
    noThumbnails: "No thumbnail images available",
    viewFullscreen: "Click to view in fullscreen",
    deleteConfirmation: "Are you sure you want to delete this unit?",
    delete: "Delete",
    cancel: "Cancel",
    updateSuccess: "Unit updated successfully",
    galleryTitle: "Image Gallery",
    galleryNavigation: "Use arrow keys or swipe to navigate",
    imageCounter: "Image {current} of {total}",
    amenities: "Amenities:",
    noAmenities: "No amenities specified",
    location: "Location",
    developer: "Developer",
    dailyRate: "Daily Rate",
    weeklyRate: "Weekly Rate",
    monthlyRate: "Monthly Rate",
    totalPrice: "Total Price",
    securityDeposit: "Security Deposit",
    serviceFee: "Service Fee",
    cleaningFee: "Cleaning Fee",
    availableFrom: "Available from:",
    rooms: "Rooms",
    bathrooms: "Bathrooms",
    floor: "Floor",
    view: "View",
    city: "City",
    country: "Country",
    purpose: "Purpose",
    buildingType: "Building Type",
    finishing: "Finishing",
    landArea: "Land Area",
    gardenSize: "Garden Size",
    deliveryDate: "Delivery Date:",
    deliveryStatus: "Delivery Status:",
    developer: "Developer",
    maintenanceLabel: "furniched",
    deliveryDate: "Delivery Date",
    deliveryDatee: "Delivery Date",
    floor: "Floor",
    finishing: "Finishing",
    furnishing: "Furnishing",
    area: "Area",
    view: "View",
    rooms: "Rooms",
    bathrooms: "Bathrooms",
    notAvailable: "N/A",
    ground: "Ground",
    first: "1st",
    second: "2nd",
    third: "3rd",
    th: "th",
    viewTypes: {
      garden: "Garden",
      sea: "Sea",
      pool: "Pool",
      mountain: "Mountain",
      street: "Street",
      city: "City",
      park: "Park",
      lake: "Lake",
      golf: "Golf Course",
      landmark: "Landmark",
      partial: "Partial",
      open: "Open",
      desert: "Desert",
      river: "River",
      courtyard: "Courtyard",
      forest: "Forest",
      beach: "Beach",
      lagoon: "Lagoon",
      openArea: "Open Area",
    },
    finishingTypes: {
      "fully finished": "Fully Finished",
      "semi finished": "Semi Finished",
      "core & shell": "Core & Shell",
      unfinished: "Unfinished",
      finished: "Finished",
    },
    furnishingTypes: {
      furnished: "Furnished",
      unfurnished: "Unfurnished",
      "semi furnished": "Semi Furnished",
    },
    amenitiesTypes: {
      wifi: "WiFi",
      dryer: "Dryer",
      air_conditioning: "Air Conditioning",
      heating: "Heating",
      smart_tv: "Smart TV",
      hair_dryer: "Hair Dryer",
      pool: "Pool",
      free_parking: "Free Parking",
      ev_charger: "EV Charger",
      bbq_grill: "BBQ Grill",
      indoor_fireplace: "Indoor Fireplace",
      smoking_allowed: "Smoking Allowed",
      beachfront: "Beachfront",
      smoke_alarm: "Smoke Alarm",
      co_alarm: "CO Alarm",
    },
    currency: {
      egp: "EGP",
    },
    unit_pricing: {
      down_payment: "Down Payment",
      years: "Years",
      maintenance: "Maintenance",
      duration: {
        daily: "Daily",
        weekly: "Weekly",
        monthly: "Monthly",
      },
      per_duration: {
        daily: "",
        weekly: "",
        monthly: "",
      },
      security_deposit: "Security Deposit",
      cleaning_fee: "Cleaning Fee",
      service_fee: "Service Fee",
      available_now: "Available Now",
      from: "From",
      currently_unavailable: "Currently Unavailable",
    },
    common: {
      na: "N/A",
    },
    buildingTypesMap: {
      Notdefined: "Not defined",
      apartment: "Apartment",
      villa: "Villa",
      townhouse: "Townhouse",
      duplex: "Duplex",
      penthouse: "Penthouse",
      studio: "Studio",
      chalet: "Chalet",
      office: "Office",
      shop: "Shop",
      twinhouse: "Twinhouse",
      house: "House",
    },
  },
  common: {
    ProcessingImages: "Processing images...",
    addImage: "Add Image",
  },
  formValidation: {
    compoundNameRequired: "Compound name is required",
    cityRequired: "City is required",
    countryRequired: "Country is required",
    districtRequired: "District is required",
    areaPositive: "Area must be a positive number",
  },
  toasts: {
    imageRemoved: "Image removed successfully from the server!",
    imageRemoveFailed:
      "Failed to remove image from the server. Please try again.",
    selectImage: "Please select an image to upload.",
    imageUploaded: "Image uploaded successfully!",
    imageUploadFailed: "Failed to upload image. Please try again.",
    compoundAdded: "Project added successfully!",
    compoundAddFailed: "Failed to add project. Please try again.",
    enterValidPrice:
      "Please enter a price greater than 0 for at least one duration type",
    uploadImage: "Please upload at least one image.",
    selectPurpose: "Please select a purpose for the unit (sell/rent)",
    errorProcessing:
      "An error occurred while processing your request. Please try again",
  },
  formLabels: {
    compoundName: "Project Name",
    description: "Description",
    city: "City",
    selectCity: "Select City",
    country: "Country",
    district: "District",
    selectDistrict: "Select District",
    cityFirst: "Select City First",
    area: "Area (m²)",
    gatedCommunity: "Gated Community",
    developer: "Developer",
    selectDeveloper: "Select developer",
    videoURL: "Video URL",
    googleMapsLink: "Google Maps Link",
    masterPlanImage: "Master Plan Image",
    selectedImage: "Selected",
    dragDropImage: "Click or drag and drop an image here",
    supportedFormats: "Supported formats: JPG, PNG, WEBP (Max 5MB each)",
    unitTitle: "Unit Title",
    buildingType: "Building Type",
    purpose: "Purpose",
    compound: "Compound",
    view: "View",
    isGated: "Gated Community",
    deliveryDate: "Delivery Date",
    deliveryStatus: "Delivery Status",
    bathroomCount: "Bathrooms",
    floor: "Floor",
    roomsCount: "Rooms",
    landArea: "Land Area (m²)",
    gardenSize: "Garden Size (m²)",
    finishing: "Finishing",
    garageArea: "Garage Area (m²)",
    images: "Images",
    districtFirst: "select district first",
    selectDistrict: "select district",
  },
  buttons: {
    addNew: "Add New",
    cancel: "Cancel",
    uploading: "Uploading...",
    uploadImage: "Upload Image",
    saving: "Saving...",
    saveProject: "Save Project",
    tryChat: "Try our Chatbot",
    requestDemo: "Request Demo",
    back: "Back",
    next: "Next",
    saveUnit: "Save Unit",
  },
  modal: {
    addNewProject: "Add New Project",
    addNewUnit: "Add New Unit",
    editUnit: "Edit Unit",
  },
  team: {
    addNew: "Add New",
    noMembers: "There are no team members yet",
    name: "Name",
    email: "Email",
    phone: "Phone",
    role: "Position",
    save: "Save",
    loading: "saving...",
  },
  compoundNames: {
    "lake view residence": "Lake View Residence",
    "the address east": "the address east",
    "new giza": "New Giza",
    Sarai: "Sarai",
    Wesal: "Wesal",
    "Palm Hills New Cairo": "Palm Hills New Cairo",
    "New Administrative Capital": "New Administrative Capital",
    بريفادو: "brevado",
    "the arabesque compound": "The Arabesque Compound",
    "ALDAU Development": "ALDAU Development",
    "New Giza": "New Giza",
    "El Borouj": "El Borouj",
    "El Rehab": "El Rehab",
    "Hassan Allam": "Hassan Allam",
    Madinaty: "Madinaty",
    Mivida: "Mivida",
    "Solare North Coast": "Solare North Coast",
    "Swan Lake": "Swan Lake",
    "Terrace Smouha": "Terrace Smouha",
    cairohouse: "Cairohouse",
    "jayd residence": "Jayd Residence",
    "marina marassi": "Marina Marassi",
    "mountain view ras el hekma": "Mountain View Ras El Hekma",
  },
  phasee:{
    updatePhasesuccess:"   phase updated successfuly",
    updatePhaseFaile :" Failed to update phase  ",
    addnew:"add new phase",
    delete:"phase delete successfuly"
   },
  MasrdeveloperNames: {
    " La Vista Developments": " La Vista Developments",
    " شركة سوديك (SODIC)": "SODIC",
    "Madinet Masr": "Madinet Masr",
    "Government-backed initiative": "Government-backed initiative",
    "Tatweer Misr": "Tatweer Misr",
    "Emaar Misr": "Emaar Misr",
    " La Vista Developments": "La Vista Developments",
    "Misr Italia": "Misr Italia",
    Founders: "Founders",
    "Saudi Egyptian Developers - SED": "Saudi Egyptian Developers - SED",
    "Capital Group Properties": "Capital Group Properties",
    "New Giza Developments": "New Giza Developments",
    "Dorra Developments": "Dorra Developments",
    "El Hazek Group": "El Hazek Group",
    "Arabia Holding": "Arabia Holding",
    TMG: "TMG",
    "شركة سوديك (SODIC)": "شركة سوديك (SODIC)",
    "Madinat Masr": "Madinat Masr",
    "Mountain View Developments": "Mountain View Developments",
    "ALDAU Development": "ALDAU Development",
    "G Developments": "G Developments",
    "Hassan Allam": "Hassan Allam",
    "OHK Consultants": "OHK Consultants",
    "Palm Hills": "Palm Hills",
  },
};
