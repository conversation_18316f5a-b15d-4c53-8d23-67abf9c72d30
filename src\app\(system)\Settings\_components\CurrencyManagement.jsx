"use client"
import React, { useState } from 'react';
import { Plus, Edit, Trash2 } from 'lucide-react';

const CurrencyManagement = () => {
  const [currencies, setCurrencies] = useState([
    {
      id: 1,
      name: 'Dollars',
      symbol: 'USD ($)',
      format: '$12,345.68',
      isDefault: false
    },
    {
      id: 2,
      name: 'Rupee',
      symbol: 'INR (₹)',
      format: '₹12,345.68',
      isDefault: false
    },
    {
      id: 3,
      name: 'Pounds',
      symbol: 'GBP (£)',
      format: '£12,345.68',
      isDefault: false
    },
    {
      id: 4,
      name: 'Euros',
      symbol: 'EUR (€)',
      format: '€12,345.68',
      isDefault: false
    },
    {
      id: 5,
      name: 'Egyptian pound',
      symbol: 'Egp (L.E)',
      format: '12,345.68 L.E',
      isDefault: true
    }
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [newCurrency, setNewCurrency] = useState({
    name: '',
    symbol: '',
    format: ''
  });

  const handleDelete = (id) => {
    setCurrencies(currencies.filter(currency => currency.id !== id));
  };

  const handleAddCurrency = () => {
    if (newCurrency.name && newCurrency.symbol && newCurrency.format) {
      const newId = Math.max(...currencies.map(c => c.id)) + 1;
      setCurrencies([...currencies, {
        id: newId,
        ...newCurrency,
        isDefault: false
      }]);
      setNewCurrency({ name: '', symbol: '', format: '' });
      setShowAddForm(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <button 
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-[#FF6500] hover:bg-[#e55a00] text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Currency
          </button>
        </div>

        {/* Add Currency Form */}
        {showAddForm && (
          <div className="p-6 bg-gray-50 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Currency</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Currency Name</label>
                <input
                  type="text"
                  value={newCurrency.name}
                  onChange={(e) => setNewCurrency({...newCurrency, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                  placeholder="e.g., Dollars"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Currency Symbol</label>
                <input
                  type="text"
                  value={newCurrency.symbol}
                  onChange={(e) => setNewCurrency({...newCurrency, symbol: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                  placeholder="e.g., USD ($)"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Format Example</label>
                <input
                  type="text"
                  value={newCurrency.format}
                  onChange={(e) => setNewCurrency({...newCurrency, format: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF6500] focus:border-transparent"
                  placeholder="e.g., $12,345.68"
                />
              </div>
            </div>
            <div className="mt-4 flex gap-2">
              <button
                onClick={handleAddCurrency}
                className="bg-[#FF6500] hover:bg-[#e55a00] text-white font-medium py-2 px-4 rounded-md transition-colors duration-200"
              >
                Add Currency
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors duration-200"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Table Header */}
        <div className="grid grid-cols-12 gap-4 p-6 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-600 uppercase tracking-wider">
          <div className="col-span-3">Currency</div>
          <div className="col-span-3">Currency Symbol</div>
          <div className="col-span-4">
            <div>Currency Format</div>
            <div className="text-xs normal-case text-gray-500 mt-1">(Sample: 12345.6789)</div>
          </div>
          <div className="col-span-2">Action</div>
        </div>

        {/* Currency Rows */}
        <div className="divide-y divide-gray-200">
          {currencies.map((currency) => (
            <div key={currency.id} className="grid grid-cols-12 gap-4 p-6 hover:bg-gray-50 transition-colors">
              <div className="col-span-3 text-gray-900 font-medium">{currency.name}</div>
              <div className="col-span-3 text-gray-700">{currency.symbol}</div>
              <div className="col-span-4 text-gray-700">{currency.format}</div>
              <div className="col-span-2 flex items-center gap-2">
                {currency.isDefault ? (
                  <div className="flex items-center gap-2">
                    <button className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-md hover:bg-gray-200 transition-colors">
                      <Edit className="w-3 h-3" />
                      Update
                    </button>
                    <div className="text-xs text-gray-500">Cannot Delete Default Currency.</div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <button className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-md hover:bg-gray-200 transition-colors">
                      <Edit className="w-3 h-3" />
                      Update
                    </button>
                    <button 
                      onClick={() => handleDelete(currency.id)}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-red-50 text-red-600 text-sm rounded-md hover:bg-red-100 transition-colors"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CurrencyManagement;