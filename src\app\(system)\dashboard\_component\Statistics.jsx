// "use client"
// import { MoreVertical } from 'lucide-react';
// import React from 'react'
// import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';


// const Statistics = () => {
// //     const revenueData = [
// //     { month: 'يناير', revenue: 45000, orders: 120 },
// //     { month: 'فبراير', revenue: 52000, orders: 140 },
// //     { month: 'مارس', revenue: 48000, orders: 130 },
// //     { month: 'أبريل', revenue: 61000, orders: 165 },
// //     { month: 'مايو', revenue: 75000, orders: 200 },
// //     { month: 'يونيو', revenue: 68000, orders: 180 },
// //     { month: 'يوليو', revenue: 82000, orders: 220 },
// //     { month: 'أغسطس', revenue: 79000, orders: 210 },
// //     { month: 'سبتمبر', revenue: 87000, orders: 240 },
// //     { month: 'أكتوبر', revenue: 92000, orders: 260 },
// //     { month: 'نوفمبر', revenue: 88000, orders: 230 },
// //     { month: 'ديسمبر', revenue: 95000, orders: 280 }
// //   ];
//  const categoryData = [
//     { name: 'مشروبات ساخنه', value: 35, color: '#8B5CF6' },
//     { name: 'مشروبات بارده', value: 25, color: '#06B6D4' },
//     { name: 'مخبوزات', value: 20, color: '#10B981' },
//     { name: 'وجبات', value: 15, color: '#F59E0B' },
//     { name: 'أخرى', value: 5, color: '#EF4444' }
//   ];
// const date = new Date();
// const today = date.getDate(); // اليوم الحالي
// const arabicMonths = [
//   "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
//   "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
// ];
// const currentMonth = arabicMonths[date.getMonth()];

// const revenueData = [];

// for (let i = 1; i <= today; i++) {
//   revenueData.push({
//     month: currentMonth,
//     day: `${i} ${currentMonth}`,
//     revenue: Math.floor(Math.random() * 10000) + 1000, // عشوائي بين 1000 و 11000
//     orders: Math.floor(Math.random() * 50) + 10         // عشوائي بين 10 و 60
//   });
// }

// console.log(revenueData);



//   return (
//    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
//      {/* Revenue Chart */}
//     <div className="lg:col-span-2 bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
//              <div className="flex items-center justify-between mb-6">
//                <div>
//                  <h3 className="text-lg font-semibold text-gray-900">الإيرادات الشهرية</h3>
//                  <p className="text-gray-600 text-sm">نظرة عامة على الإيرادات للشهر الحالي</p>
//                </div>
//                <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
//                  <MoreVertical className="h-5 w-5 text-gray-500" />
//                </button>
//              </div>
//              <div className="h-80">
//                <ResponsiveContainer width="100%" height="100%">
//                  <AreaChart data={revenueData}>
//                    <defs className=''>
//                      <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
//                        <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
//                        <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
//                      </linearGradient>
//                    </defs>
//                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
//                    <XAxis dataKey="day" stroke="#64748b" fontSize={12} />
//                    <YAxis stroke="#64748b" fontSize={12} />
//                    <Tooltip 
//                      contentStyle={{ 
//                        backgroundColor: 'white', 
//                        border: '1px solid #e2e8f0', 
//                        borderRadius: '12px',
//                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
//                      }} 
//                    />
//                    <Area 
//                      type="monotone" 
//                      dataKey="revenue" 
//                      stroke="#3B82F6" 
//                      strokeWidth={3}
//                      fill="url(#colorRevenue)" 
//                    />
//                  </AreaChart>
//                </ResponsiveContainer>
//              </div>
//            </div>
//            {/* Category Distribution */}
//             <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 ">
//           <div className="flex items-center justify-between mb-6">
//             <div>
//               <h3 className="text-lg font-semibold text-gray-900">توزيع الفئات</h3>
//               <p className="text-gray-600 text-sm">المبيعات حسب الفئة</p>
//             </div>
//           </div>
//           <div className="h-64">
//             <ResponsiveContainer width="100%" height="100%">
//               <PieChart>
//                 <Pie
//                   data={categoryData}
//                   cx="50%"
//                   cy="50%"
//                   innerRadius={60}
//                   outerRadius={100}
//                   paddingAngle={5}
//                   dataKey="value"
//                 >
//                   {categoryData.map((entry, index) => (
//                     <Cell key={`cell-${index}`} fill={entry.color} />
//                   ))}
//                 </Pie>
//                 <Tooltip />
//               </PieChart>
//             </ResponsiveContainer>
//           </div>
//           <div className="space-y-2 mt-4 ">
//             {categoryData.map((item, index) => (
//               <div key={index} className="flex items-center justify-between">
//                 <div className="flex items-center">
//                   <div className={`w-3 h-3 rounded-full ml-2`} style={{backgroundColor: item.color}}></div>
//                   <span className="text-sm text-gray-600">{item.name}</span>
//                 </div>
//                 <span className="text-sm font-medium text-gray-900">{item.value}%</span>
//               </div>
//             ))}
//           </div>
//         </div>
            
//       </div>
//   )
// }

// export default Statistics
"use client"
import { MoreVertical } from 'lucide-react';
import React from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

const Statistics = () => {
  const categoryData = [
    { name: 'Hot Drinks', value: 35, color: '#8B5CF6' },
    { name: 'Cold Drinks', value: 25, color: '#06B6D4' },
    { name: 'Bakery', value: 20, color: '#10B981' },
    { name: 'Meals', value: 15, color: '#F59E0B' },
    { name: 'Others', value: 5, color: '#EF4444' }
  ];

  const date = new Date();
  const today = date.getDate(); // Current day
  const englishMonths = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];
  const currentMonth = englishMonths[date.getMonth()];

  const revenueData = [];

  for (let i = 1; i <= today; i++) {
    revenueData.push({
      month: currentMonth,
      day: `${i} ${currentMonth}`,
      revenue: Math.floor(Math.random() * 10000) + 1000, // Random between 1000 and 11000
      orders: Math.floor(Math.random() * 50) + 10         // Random between 10 and 60
    });
  }

  console.log(revenueData);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Revenue Chart */}
      <div className="lg:col-span-2 bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Monthly Revenue</h3>
            <p className="text-gray-600 text-sm">Revenue overview for the current month</p>
          </div>
          <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <MoreVertical className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={revenueData}>
              <defs className=''>
                <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
              <XAxis dataKey="day" stroke="#64748b" fontSize={12} />
              <YAxis stroke="#64748b" fontSize={12} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'white', 
                  border: '1px solid #e2e8f0', 
                  borderRadius: '12px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }} 
              />
              <Area 
                type="monotone" 
                dataKey="revenue" 
                stroke="#3B82F6" 
                strokeWidth={3}
                fill="url(#colorRevenue)" 
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Category Distribution */}
      <div className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100 ">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Category Distribution</h3>
            <p className="text-gray-600 text-sm">Sales by category</p>
          </div>
        </div>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={categoryData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {categoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="space-y-2 mt-4 ">
          {categoryData.map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full ml-2`} style={{backgroundColor: item.color}}></div>
                <span className="text-sm text-gray-600">{item.name}</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{item.value}%</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Statistics